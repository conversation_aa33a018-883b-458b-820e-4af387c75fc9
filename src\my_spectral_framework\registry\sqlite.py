"""
SQLite 产物注册表实现。

本模块实现了基于 SQLite 数据库的产物注册表，提供了完整的产物元数据管理功能。
SQLite 是一个轻量级的嵌入式数据库，非常适合单机环境下的元数据存储。

设计理念：
1. 轻量级：使用 SQLite 作为嵌入式数据库，无需额外配置
2. 完整性：支持事务操作，确保数据一致性
3. 查询优化：使用索引优化查询性能
4. 兼容性：使用标准 SQL 语法，便于迁移到其他数据库


License: Apache-2.0
"""

import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from .base import BaseArtifactRegistry, RegistryError, ArtifactNotFoundError, DuplicateArtifactError
from ..core.utils import json_custom_serializer


class SQLiteArtifactRegistry(BaseArtifactRegistry):
    """
    基于 SQLite 的产物注册表实现。
    
    这个类使用 SQLite 数据库来存储和管理实验产物的元数据信息，
    提供了完整的 CRUD 操作和查询功能。
    """
    
    def __init__(self, db_path: str):
        """
        初始化 SQLite 产物注册表。

        Args:
            db_path: SQLite 数据库文件路径
        """
        super().__init__(db_path)
        self.db_path = Path(db_path)

        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 初始化数据库
        self._init_database()

        self.logger.info(f"SQLiteArtifactRegistry 初始化完成，数据库路径: {self.db_path}")

    def close(self):
        """关闭数据库连接（用于清理）"""
        # SQLite 使用 with 语句自动管理连接，这里主要是为了接口完整性
        pass
    
    def _init_database(self) -> None:
        """初始化数据库表结构。"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建产物表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS artifacts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id TEXT NOT NULL,
                    artifact_name TEXT NOT NULL,
                    uri TEXT NOT NULL,
                    format TEXT NOT NULL,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(run_id, artifact_name)
                )
            """)
            
            # 创建索引以优化查询性能
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_run_id ON artifacts(run_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_artifact_name ON artifacts(artifact_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_format ON artifacts(format)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_created_at ON artifacts(created_at)")
            
            conn.commit()
    
    def register_artifact(
        self,
        run_id: str,
        artifact_name: str,
        uri: str,
        format: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        注册一个产物到 SQLite 注册表。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            uri: 产物的存储位置 URI
            format: 产物格式
            metadata: 可选的元数据信息
            
        Returns:
            bool: 注册是否成功
        """
        try:
            # 验证数据有效性
            if not self.validate_artifact_data(run_id, artifact_name, uri, format):
                raise RegistryError("产物数据验证失败")
            
            # 序列化元数据
            metadata_json = None
            if metadata:
                metadata_json = json.dumps(metadata, default=json_custom_serializer)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在
                cursor.execute(
                    "SELECT id FROM artifacts WHERE run_id = ? AND artifact_name = ?",
                    (run_id, artifact_name)
                )
                
                if cursor.fetchone():
                    # 更新现有记录
                    cursor.execute("""
                        UPDATE artifacts 
                        SET uri = ?, format = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE run_id = ? AND artifact_name = ?
                    """, (uri, format, metadata_json, run_id, artifact_name))
                    
                    self.logger.info(f"更新产物注册: {run_id}/{artifact_name}")
                else:
                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO artifacts (run_id, artifact_name, uri, format, metadata)
                        VALUES (?, ?, ?, ?, ?)
                    """, (run_id, artifact_name, uri, format, metadata_json))
                    
                    self.logger.info(f"注册新产物: {run_id}/{artifact_name}")
                
                conn.commit()
                return True
                
        except sqlite3.Error as e:
            error_msg = f"注册产物失败: {run_id}/{artifact_name}, 错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
        except Exception as e:
            error_msg = f"注册产物时发生未知错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def get_artifact_uri(self, run_id: str, artifact_name: str) -> Optional[str]:
        """
        获取指定产物的存储 URI。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            Optional[str]: 产物的 URI，如果不存在则返回 None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT uri FROM artifacts WHERE run_id = ? AND artifact_name = ?",
                    (run_id, artifact_name)
                )
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except sqlite3.Error as e:
            error_msg = f"查询产物 URI 失败: {run_id}/{artifact_name}, 错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def get_artifact_metadata(self, run_id: str, artifact_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定产物的完整元数据。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            Optional[Dict[str, Any]]: 产物的元数据，如果不存在则返回 None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT run_id, artifact_name, uri, format, metadata, created_at, updated_at
                    FROM artifacts WHERE run_id = ? AND artifact_name = ?
                """, (run_id, artifact_name))
                
                result = cursor.fetchone()
                if not result:
                    return None
                
                # 解析元数据
                custom_metadata = {}
                if result[4]:  # metadata column
                    try:
                        custom_metadata = json.loads(result[4])
                    except json.JSONDecodeError:
                        self.logger.warning(f"无法解析产物元数据: {run_id}/{artifact_name}")
                
                return {
                    "run_id": result[0],
                    "artifact_name": result[1],
                    "uri": result[2],
                    "format": result[3],
                    "metadata": custom_metadata,
                    "created_at": result[5],
                    "updated_at": result[6]
                }
                
        except sqlite3.Error as e:
            error_msg = f"查询产物元数据失败: {run_id}/{artifact_name}, 错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def list_artifacts(self, run_id: str) -> List[Dict[str, Any]]:
        """
        列出指定实验运行的所有产物。
        
        Args:
            run_id: 实验运行 ID
            
        Returns:
            List[Dict[str, Any]]: 产物信息列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT artifact_name, uri, format, created_at, updated_at
                    FROM artifacts WHERE run_id = ?
                    ORDER BY created_at DESC
                """, (run_id,))
                
                artifacts = []
                for row in cursor.fetchall():
                    artifacts.append({
                        "artifact_name": row[0],
                        "uri": row[1],
                        "format": row[2],
                        "created_at": row[3],
                        "updated_at": row[4]
                    })
                
                return artifacts
                
        except sqlite3.Error as e:
            error_msg = f"列出产物失败: {run_id}, 错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def list_runs(self) -> List[str]:
        """
        列出所有实验运行 ID。
        
        Returns:
            List[str]: 实验运行 ID 列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT run_id FROM artifacts ORDER BY run_id")
                
                return [row[0] for row in cursor.fetchall()]
                
        except sqlite3.Error as e:
            error_msg = f"列出实验运行失败: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def delete_artifact(self, run_id: str, artifact_name: str) -> bool:
        """
        从注册表中删除指定产物。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "DELETE FROM artifacts WHERE run_id = ? AND artifact_name = ?",
                    (run_id, artifact_name)
                )
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    self.logger.info(f"删除产物: {run_id}/{artifact_name}")
                    return True
                else:
                    self.logger.warning(f"产物不存在，无法删除: {run_id}/{artifact_name}")
                    return False
                    
        except sqlite3.Error as e:
            error_msg = f"删除产物失败: {run_id}/{artifact_name}, 错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def delete_run(self, run_id: str) -> bool:
        """
        从注册表中删除指定实验运行的所有产物。
        
        Args:
            run_id: 实验运行 ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM artifacts WHERE run_id = ?", (run_id,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    self.logger.info(f"删除实验运行的所有产物: {run_id} ({deleted_count} 个产物)")
                    return True
                else:
                    self.logger.warning(f"实验运行不存在或无产物: {run_id}")
                    return False
                    
        except sqlite3.Error as e:
            error_msg = f"删除实验运行失败: {run_id}, 错误: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def search_artifacts(
        self,
        name_pattern: Optional[str] = None,
        format: Optional[str] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索符合条件的产物。
        
        Args:
            name_pattern: 产物名称模式（支持 SQL LIKE 语法）
            format: 产物格式
            created_after: 创建时间下限
            created_before: 创建时间上限
            
        Returns:
            List[Dict[str, Any]]: 符合条件的产物信息列表
        """
        try:
            conditions = []
            params = []
            
            if name_pattern:
                conditions.append("artifact_name LIKE ?")
                params.append(name_pattern)
            
            if format:
                conditions.append("format = ?")
                params.append(format)
            
            if created_after:
                conditions.append("created_at >= ?")
                params.append(created_after.isoformat())
            
            if created_before:
                conditions.append("created_at <= ?")
                params.append(created_before.isoformat())
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"""
                    SELECT run_id, artifact_name, uri, format, created_at, updated_at
                    FROM artifacts WHERE {where_clause}
                    ORDER BY created_at DESC
                """, params)
                
                artifacts = []
                for row in cursor.fetchall():
                    artifacts.append({
                        "run_id": row[0],
                        "artifact_name": row[1],
                        "uri": row[2],
                        "format": row[3],
                        "created_at": row[4],
                        "updated_at": row[5]
                    })
                
                return artifacts
                
        except sqlite3.Error as e:
            error_msg = f"搜索产物失败: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取注册表统计信息。
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总产物数
                cursor.execute("SELECT COUNT(*) FROM artifacts")
                total_artifacts = cursor.fetchone()[0]
                
                # 总实验运行数
                cursor.execute("SELECT COUNT(DISTINCT run_id) FROM artifacts")
                total_runs = cursor.fetchone()[0]
                
                # 按格式统计
                cursor.execute("""
                    SELECT format, COUNT(*) FROM artifacts 
                    GROUP BY format ORDER BY COUNT(*) DESC
                """)
                format_stats = dict(cursor.fetchall())
                
                # 最近创建的产物
                cursor.execute("""
                    SELECT run_id, artifact_name, created_at FROM artifacts 
                    ORDER BY created_at DESC LIMIT 5
                """)
                recent_artifacts = [
                    {"run_id": row[0], "artifact_name": row[1], "created_at": row[2]}
                    for row in cursor.fetchall()
                ]
                
                return {
                    "total_artifacts": total_artifacts,
                    "total_runs": total_runs,
                    "format_distribution": format_stats,
                    "recent_artifacts": recent_artifacts,
                    "database_path": str(self.db_path)
                }
                
        except sqlite3.Error as e:
            error_msg = f"获取统计信息失败: {e}"
            self.logger.error(error_msg)
            raise RegistryError(error_msg) from e
