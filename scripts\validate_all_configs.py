#!/usr/bin/env python3
"""
批量配置文件验证脚本

遍历 `conf/runs` 目录下的所有实验配置文件，
使用 Pydantic 模型进行验证，并生成详细报告。
"""
import importlib
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
if str(project_root / "src") not in sys.path:
    sys.path.insert(0, str(project_root / "src"))

from pydantic import ValidationError
from my_spectral_framework.core.config_models import ExperimentRootConfig


def find_config_files():
    conf_dir = project_root / "conf" / "runs"
    return sorted([f for f in conf_dir.rglob("*.py") if f.name != "__init__.py"])


def main():
    print("[INFO] 开始批量验证所有实验配置文件...")
    print("=" * 80)

    config_files = find_config_files()
    print(f"[INFO] 发现 {len(config_files)} 个配置文件待验证。\n")

    passed_count = 0
    failed_configs = []

    for file_path in config_files:
        relative_path = file_path.relative_to(project_root)
        module_name = str(relative_path.with_suffix('')).replace('/', '.').replace('\\', '.')

        try:
            print(f"[CHECKING] 正在验证: {relative_path}")
            config_module = importlib.import_module(module_name)

            if not hasattr(config_module, "EXPERIMENT_CONFIG"):
                raise AttributeError("文件中缺少 'EXPERIMENT_CONFIG' 字典")

            config_dict = config_module.EXPERIMENT_CONFIG
            ExperimentRootConfig.model_validate(config_dict)

            print("  [PASS] 验证通过\n")
            passed_count += 1

        except (ValidationError, AttributeError, ImportError, Exception) as e:
            print(f"  [FAIL] 验证失败: {relative_path}")
            error_details = f"    错误类型: {type(e).__name__}\n"
            if isinstance(e, ValidationError):
                for error in e.errors():
                    field = " -> ".join(map(str, error["loc"]))
                    error_details += f"    - 字段: `{field}`, 错误: {error['msg']}\n"
            else:
                error_details += f"    - 详情: {e}\n"
            print(error_details)
            failed_configs.append((relative_path, error_details))

    print("=" * 80)
    print("[SUMMARY] 验证总结")
    print("=" * 80)
    print(f"总计: {len(config_files)} | [PASS] 通过: {passed_count} | [FAIL] 失败: {len(failed_configs)}")

    if failed_configs:
        print("\n以下配置文件需要修复:")
        for path, _ in failed_configs:
            print(f"  - {path}")
        sys.exit(1)
    else:
        print("\n[SUCCESS] 所有配置文件均符合架构规范！")
        sys.exit(0)

if __name__ == "__main__":
    main()
