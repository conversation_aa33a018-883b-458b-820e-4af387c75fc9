"""
SOLID原则驱动的测试注册表 - 测试框架的神经中枢

这个模块实现了装饰器驱动的测试注册系统，让测试用例的添加变得像
为框架添加新模型一样简单和解耦。

核心思想：
- 测试函数通过装饰器自动注册到全局注册表
- 运行器从注册表中发现和执行测试，无需硬编码
- 丰富的元数据支持灵活的筛选和报告

Author: txy (基于总调度师的架构设计)
License: Apache-2.0
"""

from typing import Any, Callable, Dict, List, Optional

# 全局测试注册表 - 测试框架的神经中枢
TEST_REGISTRY: Dict[str, List[Dict[str, Any]]] = {
    "unit": [],
    "integration": [],
    "e2e": [],
    "performance": [],
}

def register_test(level: str, description: str, tags: Optional[List[str]] = None, priority: str = "normal"):
    """
    装饰器：将测试函数自动注册到全局测试注册表中。

    Args:
        level (str): 测试级别 ('unit', 'integration', 'e2e', 'performance')。
        description (str): 对测试职责的清晰描述。
        tags (List[str], optional): 用于筛选的标签。
        priority (str): 测试优先级 ('critical', 'high', 'normal', 'low')。

    Returns:
        Callable: 装饰后的测试函数。
    """
    if level not in TEST_REGISTRY:
        raise ValueError(f"无效的测试级别 '{level}'. 可选项: {list(TEST_REGISTRY.keys())}")

    def decorator(func: Callable) -> Callable:
        # 尝试获取类名（如果测试方法在类中）
        class_name = None
        if hasattr(func, '__qualname__') and '.' in func.__qualname__:
            class_name = func.__qualname__.split('.')[-2]

        test_case = {
            "function": func,
            "name": func.__name__,
            "level": level,
            "description": description,
            "tags": tags or [],
            "priority": priority,
            "module": func.__module__,
            "class_name": class_name,
        }
        TEST_REGISTRY[level].append(test_case)
        func._test_metadata = test_case
        return func
    return decorator

# --- 便捷别名 ---
def unit_test(description: str, **kwargs):
    return register_test("unit", description, **kwargs)

def integration_test(description: str, **kwargs):
    return register_test("integration", description, **kwargs)

def e2e_test(description: str, **kwargs):
    return register_test("e2e", description, **kwargs)

def performance_test(description: str, tags: Optional[List[str]] = None, **kwargs):
    tags = tags or []
    if "performance" not in tags:
        tags.append("performance")
    return register_test("performance", description, tags=tags, **kwargs)

# --- 查询接口 ---
def get_registered_tests(
    level: Optional[str] = None, tag: Optional[str] = None, priority: Optional[str] = None
) -> List[Dict[str, Any]]:
    """从注册表中获取符合条件的测试用例。"""
    tests = []
    levels_to_scan = [level] if level else TEST_REGISTRY.keys()
    for lvl in levels_to_scan:
        for test_case in TEST_REGISTRY.get(lvl, []):
            tag_match = tag is None or tag in test_case["tags"]
            priority_match = priority is None or test_case["priority"] == priority
            if tag_match and priority_match:
                tests.append(test_case)
    return tests

def get_registry_stats() -> Dict[str, Any]:
    """获取测试注册表的统计信息。"""
    stats = {
        "total_tests": 0,
        "by_level": {level: 0 for level in TEST_REGISTRY},
        "by_priority": {"critical": 0, "high": 0, "normal": 0, "low": 0},
        "all_tags": set(),
    }
    for level, tests in TEST_REGISTRY.items():
        stats["by_level"][level] = len(tests)
        stats["total_tests"] += len(tests)
        for test in tests:
            stats["by_priority"][test["priority"]] += 1
            stats["all_tags"].update(test["tags"])
    stats["all_tags"] = sorted(list(stats["all_tags"]))
    return stats
