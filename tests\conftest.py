"""
测试基础设施 - Pytest Fixtures

提供标准化的模拟数据、配置和上下文对象，供所有测试函数使用。
"""
import pytest
import numpy as np
from typing import Tuple
from pathlib import Path
import sys

# 项目路径设置
project_root = Path(__file__).parent.parent
if str(project_root / "src") not in sys.path:
    sys.path.insert(0, str(project_root / "src"))

from my_spectral_framework.core.context import ExperimentContext


@pytest.fixture(scope="session")
def mock_spectral_data() -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    创建一个全局共享的、可控的模拟光谱数据集。

    Returns:
        Tuple[X, y, groups]: 特征矩阵、标签向量、分组向量
    """
    np.random.seed(42)
    n_samples, n_features = 100, 50
    X = np.random.rand(n_samples, n_features)
    y = (X[:, 0] + X[:, 1] > 1.0).astype(int)
    groups = np.repeat(np.arange(n_samples // 5), 5)
    return X, y, groups


@pytest.fixture
def base_config() -> dict:
    """提供一个最小化的、有效的实验配置字典。"""
    return {
        "experiment_type": "single_run",
        "data_config": {
            "source_type": "opensa_test", "type": "Cls", "test_size": 0.25, "random_seed": 42,
        },
        "preprocessing_config": {"steps": [{"method": "SNV", "params": {}}]},
        "feature_selection_config": {"method": "None", "params": {}},
        "engine_config": {
            "engine": "sklearn",
            "ml_model_config": {
                "type": "Classification", "name": "RF", "params": {"n_estimators": 10, "random_state": 42},
            }
        },
        "evaluation_config": {},
        "experiment_config": {"name": "test_experiment"},
        "paths_config": {},
    }


@pytest.fixture
def test_context(base_config) -> ExperimentContext:
    """提供一个干净的 ExperimentContext 实例用于测试。"""
    return ExperimentContext(base_config)


