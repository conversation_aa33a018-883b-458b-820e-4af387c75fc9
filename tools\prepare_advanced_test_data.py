"""
Prepare test data for advanced data loading functionality.

This script adds required columns to the spectrai test data to make it
compatible with the new advanced data loading system.

Author: txy
License: Apache-2.0
"""

"""
Advanced Test Data Preparation Tool

This tool prepares advanced test data for spectral analysis experiments.
"""

import argparse
import sys
from pathlib import Path

import numpy as np
import pandas as pd

# 项目路径设置（现在通过包安装自动处理）
project_root = Path(__file__).parent.parent

# 临时添加 src 路径以确保导入工作（在包安装问题解决前）
src_path = project_root / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from my_spectral_framework.core.utils import get_logger, get_project_root

logger = get_logger(__name__)


def prepare_advanced_test_data(input_file, output_file):
    """Prepare spectrai data for advanced loading tests."""

    # Load existing spectrai data
    data_path = get_project_root() / input_file

    if not data_path.exists():
        print(f"❌ Error: {data_path} not found!")
        print("Please run convert_spectrai_data.py first to create the test data.")
        return False

    print(f"📊 Loading data from {data_path}")
    df = pd.read_csv(data_path)

    print(f"Original data shape: {df.shape}")
    print(f"Columns: {list(df.columns[:5])}... (showing first 5)")

    # Add spectrum_id column (required for advanced loading)
    df.insert(0, "spectrum_id", range(1, len(df) + 1))

    # Add sample_group column for group-aware CV testing
    # Create artificial groups: each group has 5 consecutive spectra
    group_size = 5
    n_groups = len(df) // group_size + (1 if len(df) % group_size > 0 else 0)
    groups = []

    for i in range(len(df)):
        group_id = f"group_{i // group_size + 1:03d}"
        groups.append(group_id)

    df.insert(1, "sample_group", groups)

    print(f"✓ Added spectrum_id column")
    print(f"✓ Added sample_group column with {len(set(groups))} groups")

    # Save enhanced data
    enhanced_path = get_project_root() / output_file
    enhanced_path.parent.mkdir(parents=True, exist_ok=True)

    df.to_csv(enhanced_path, index=False)

    print(f"✅ Enhanced data saved to {enhanced_path}")
    print(f"Final data shape: {df.shape}")
    print(f"Sample groups: {len(set(groups))} unique groups")
    print(f"Group size range: {min([groups.count(g) for g in set(groups)])} - {max([groups.count(g) for g in set(groups)])}")

    # Show sample of the data
    print("\n📋 Sample of enhanced data:")
    print(df[["spectrum_id", "sample_group", "label"]].head(10))

    return True


def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(description="Prepare spectrai data for advanced loading tests.")
    parser.add_argument(
        "--input-file",
        type=str,
        default="data/raw/spectrai_test_data.csv",
        help="Path to the input spectrai_test_data.csv file (relative to project root).",
    )
    parser.add_argument(
        "--output-file",
        type=str,
        default="data/raw/spectrai_advanced_test_data.csv",
        help="Path to the output advanced test data CSV file (relative to project root).",
    )
    args = parser.parse_args()

    # Convert relative paths to absolute
    input_path = get_project_root() / args.input_file
    output_path = get_project_root() / args.output_file

    success = prepare_advanced_test_data(str(input_path), str(output_path))
    if success:
        print("\n🎉 Advanced test data preparation completed!")
    else:
        print("\n❌ Advanced test data preparation failed!")


if __name__ == "__main__":
    main()
