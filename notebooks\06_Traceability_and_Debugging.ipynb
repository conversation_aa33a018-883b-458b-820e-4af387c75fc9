{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 6. 实验溯源与深度调试\n", "\n", "在这个教程中，我们将学习如何使用框架内置的强大溯源功能来确保实验的可复现性，并对数据处理的每一步进行深度调试。\n", "\n", "## 🎯 学习目标\n", "\n", "- 理解三种溯源级别：`none`、`basic`、`full`\n", "- 学会使用 `basic` 级别确保实验可复现\n", "- 掌握 `full` 级别进行深度数据调试\n", "- 实践光谱ID管理策略"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 0: 环境设置和模块导入\n", "import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import json\n", "import copy\n", "\n", "# 设置项目路径\n", "project_root = Path.cwd().parent\n", "if str(project_root) not in sys.path:\n", "    sys.path.insert(0, str(project_root))\n", "\n", "# 导入框架模块\n", "from src.my_spectral_framework.main import run_experiment_from_config\n", "from conf.base.presets import FAST_DEV_RF_PRESET\n", "\n", "print(\"✅ 环境设置完成\")\n", "print(f\"📁 项目根目录: {project_root}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 场景一: \"Basic\" 级别 - 确保实验可复现\n", "\n", "有时候，您只想确保您和您的同事能够精确地复现某次实验的训练/测试集划分，而不想保存GB级的中间数据。`\"basic\"` 级别就是为此设计的。\n", "\n", "### 🔧 配置说明\n", "- `traceability_config.level = \"basic\"`\n", "- 只保存ID划分信息，不保存完整数据矩阵\n", "- 磁盘占用极小，但足以复现实验"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 1: 配置一个 \"basic\" 级别的实验\n", "basic_config = copy.deepcopy(FAST_DEV_RF_PRESET)\n", "basic_config['experiment_config']['name'] = 'basic_traceability_demo'\n", "basic_config['traceability_config'] = {'level': 'basic'}\n", "\n", "print(\"🚀 运行 'basic' 级别溯源实验...\")\n", "print(f\"📋 实验配置: {basic_config['experiment_config']['name']}\")\n", "print(f\"🔍 溯源级别: {basic_config['traceability_config']['level']}\")\n", "\n", "results_basic = run_experiment_from_config(basic_config)\n", "print(\"✅ Basic 级别实验完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 2: 加载并检查ID划分文件\n", "exp_dir = Path('outputs') / results_basic['experiment_directory_name']\n", "split_file = exp_dir / 'debug_artifacts' / 'split_indices.json'\n", "\n", "if split_file.exists():\n", "    with open(split_file, 'r') as f:\n", "        split_ids = json.load(f)\n", "    \n", "    print(f\"✅ 成功加载ID划分文件: {split_file.name}\")\n", "    print(f\"📊 训练集包含 {len(split_ids['train_ids'])} 个样本ID\")\n", "    print(f\"📊 测试集包含 {len(split_ids['test_ids'])} 个样本ID\")\n", "    print(\"\\n🔍 前5个训练集ID:\", split_ids['train_ids'][:5])\n", "    print(\"🔍 前5个测试集ID:\", split_ids['test_ids'][:5])\n", "    \n", "    # 验证ID的唯一性\n", "    all_ids = set(split_ids['train_ids'] + split_ids['test_ids'])\n", "    total_samples = len(split_ids['train_ids']) + len(split_ids['test_ids'])\n", "    print(f\"\\n✅ ID唯一性检查: {len(all_ids)} 个唯一ID / {total_samples} 个总样本\")\n", "else:\n", "    print(f\"❌ 未找到ID划分文件: {split_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 场景二: \"Full\" 级别 - 深度调试每一步数据\n", "\n", "当模型表现不佳时，您需要检查数据在预处理、特征选择后到底变成了什么样。`\"full\"` 级别会将每一步的完整数据矩阵（带ID）保存下来。\n", "\n", "### 🔧 配置说明\n", "- `traceability_config.level = \"full\"`\n", "- 保存每个处理步骤后的完整数据矩阵\n", "- 所有数据都包含光谱ID，便于追踪单个样本"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 3: 配置一个 \"full\" 级别的实验\n", "full_config = copy.deepcopy(FAST_DEV_RF_PRESET)\n", "full_config['experiment_config']['name'] = 'full_traceability_demo'\n", "full_config['traceability_config'] = {'level': 'full'}\n", "\n", "print(\"\\n🚀 运行 'full' 级别溯源实验...\")\n", "print(f\"📋 实验配置: {full_config['experiment_config']['name']}\")\n", "print(f\"🔍 溯源级别: {full_config['traceability_config']['level']}\")\n", "\n", "results_full = run_experiment_from_config(full_config)\n", "print(\"✅ Full 级别实验完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 4: 探索保存的调试产物\n", "exp_dir_full = Path('outputs') / results_full['experiment_directory_name']\n", "debug_dir_full = exp_dir_full / 'debug_artifacts'\n", "\n", "print(f\"📁 调试产物目录: {debug_dir_full}\")\n", "print(\"\\n📄 保存的文件列表:\")\n", "for file in sorted(debug_dir_full.glob('*.csv')):\n", "    file_size = file.stat().st_size / 1024  # KB\n", "    print(f\"  📊 {file.name} ({file_size:.1f} KB)\")\n", "\n", "for file in sorted(debug_dir_full.glob('*.json')):\n", "    file_size = file.stat().st_size / 1024  # KB\n", "    print(f\"  📋 {file.name} ({file_size:.1f} KB)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 5: 加载并对比中间数据\n", "# 加载原始训练数据\n", "df_raw = pd.read_csv(debug_dir_full / 'X_train_raw.csv')\n", "print(f\"📊 原始训练数据形状: {df_raw.shape}\")\n", "print(f\"📋 列名: {list(df_raw.columns[:5])}... (显示前5列)\")\n", "\n", "# 加载预处理 (SNV) 后的数据\n", "df_processed = pd.read_csv(debug_dir_full / 'X_train_after_preprocessor.csv')\n", "print(f\"📊 预处理后数据形状: {df_processed.shape}\")\n", "\n", "# 追踪同一个ID的光谱\n", "spectrum_id_to_trace = df_raw['spectrum_id'].iloc[0]\n", "print(f\"\\n🔍 追踪光谱ID: {spectrum_id_to_trace}\")\n", "\n", "raw_spectrum = df_raw[df_raw['spectrum_id'] == spectrum_id_to_trace].drop('spectrum_id', axis=1).values.flatten()\n", "processed_spectrum = df_processed[df_processed['spectrum_id'] == spectrum_id_to_trace].drop('spectrum_id', axis=1).values.flatten()\n", "\n", "print(f\"📈 原始光谱数据点数: {len(raw_spectrum)}\")\n", "print(f\"📈 处理后光谱数据点数: {len(processed_spectrum)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 6: 可视化对比原始数据与处理后数据\n", "plt.figure(figsize=(15, 6))\n", "\n", "plt.subplot(1, 3, 1)\n", "plt.plot(raw_spectrum, 'b-', alpha=0.7, linewidth=1)\n", "plt.title(f'原始光谱\\\\nID: {spectrum_id_to_trace}', fontsize=12)\n", "plt.xlabel('特征索引')\n", "plt.ylabel('强度值')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.plot(processed_spectrum, 'r-', alpha=0.7, linewidth=1)\n", "plt.title(f'SNV处理后\\\\nID: {spectrum_id_to_trace}', fontsize=12)\n", "plt.xlabel('特征索引')\n", "plt.ylabel('标准化强度值')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.plot(raw_spectrum, 'b-', alpha=0.7, linewidth=1, label='原始')\n", "plt.plot(processed_spectrum, 'r-', alpha=0.7, linewidth=1, label='SNV处理后')\n", "plt.title('对比图', fontsize=12)\n", "plt.xlabel('特征索引')\n", "plt.ylabel('强度值')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 计算处理前后的统计差异\n", "print(f\"\\n📊 数据变化统计:\")\n", "print(f\"原始数据 - 均值: {raw_spectrum.mean():.4f}, 标准差: {raw_spectrum.std():.4f}\")\n", "print(f\"处理后数据 - 均值: {processed_spectrum.mean():.4f}, 标准差: {processed_spectrum.std():.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 场景三: ID一致性验证\n", "\n", "验证同一样本在不同处理步骤中保持相同的ID，这是溯源功能的核心保证。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 步骤 7: 验证ID一致性\n", "print(\"🔍 验证ID一致性...\")\n", "\n", "# 检查所有处理步骤的ID是否一致\n", "csv_files = list(debug_dir_full.glob('X_train_*.csv'))\n", "id_consistency = True\n", "reference_ids = None\n", "\n", "for i, csv_file in enumerate(sorted(csv_files)):\n", "    df = pd.read_csv(csv_file)\n", "    current_ids = list(df['spectrum_id'])\n", "    \n", "    if reference_ids is None:\n", "        reference_ids = current_ids\n", "        print(f\"📋 参考文件: {csv_file.name} ({len(current_ids)} 个ID)\")\n", "    else:\n", "        if current_ids == reference_ids:\n", "            print(f\"✅ {csv_file.name}: ID顺序一致\")\n", "        else:\n", "            print(f\"❌ {csv_file.name}: ID顺序不一致\")\n", "            id_consistency = False\n", "\n", "if id_consistency:\n", "    print(\"\\n🎉 所有处理步骤的ID保持完全一致！\")\n", "else:\n", "    print(\"\\n⚠️ 发现ID不一致的情况，需要检查数据处理流程\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎉 总结\n", "\n", "您现在已经掌握了如何使用框架的溯源功能：\n", "\n", "### 📚 关键知识点\n", "1. **`\\\"basic\\\"` 级别**: 保存ID划分信息，确保实验可复现，磁盘占用最小\n", "2. **`\\\"full\\\"` 级别**: 保存每步处理后的完整数据，支持深度调试\n", "3. **ID一致性**: 框架确保同一样本在所有处理步骤中保持相同ID\n", "\n", "### 🛠️ 实际应用场景\n", "- **论文复现**: 使用 `basic` 级别确保审稿人能精确复现您的实验\n", "- **模型调试**: 使用 `full` 级别追踪数据处理对特定样本的影响\n", "- **团队协作**: 通过ID划分文件确保团队成员使用相同的数据分割\n", "\n", "### 🚀 下一步\n", "- 尝试在您自己的数据上使用不同的溯源级别\n", "- 结合交叉验证和超参数优化使用溯源功能\n", "- 探索如何利用保存的中间数据进行更深入的分析\n", "\n", "这使得您的研究工作流既高效又可靠！"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}