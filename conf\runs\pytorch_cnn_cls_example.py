"""
一个标准的PyTorch CNN分类实验配置示例。

此配置展示了如何使用PyTorch引擎运行一个完整的分类实验，
包括SNV预处理、PCA特征选择和一个CNN1D分类器。
"""

EXPERIMENT_CONFIG = {
    "experiment_type": "single_run",
    
    "data_config": {
        "source_type": "opensa_test",
        "type": "Cls",
        "label_col_name": "label",
        "group_col_name": None,
        "spectrum_unique_id_col_name": "spectrum_id",
        "wave_number_range": None,
        "test_size": 0.2,
        "random_seed": 42,
    },
    
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}}
        ]
    },
    
    "feature_selection_config": {
        "method": "Pca",
        "params": {"n_components": 50}
    },
    
    "engine_config": {
        "engine": "pytorch",
        "pytorch_model_config": {
            "task_type": "Classification",
            "architecture": "CNN1D",
            "params": {
                "epochs": 10,
                "batch_size": 16,
                "learning_rate": 0.001,
                "device": "cpu",
                "n_filters_1": 8,
                "n_filters_2": 16,
                "kernel_size_1": 3,
                "kernel_size_2": 3,
                "dropout_rate": 0.2,
            },
        },
        "validation_config": {
            "strategy": "hold-out",
            "split_size": 0.2,
        },
    },
    
    "evaluation_config": {
        "metrics": {
            "classification": ["accuracy", "precision", "recall", "f1"],
            "regression": ["rmse", "r2", "mae"],
        },
        "save_predictions": True,
        "save_model": True,
    },
    
    "experiment_config": {
        "name": "pytorch_cnn_cls_example",
        "description": "PyTorch CNN分类示例",
        "tags": ["pytorch", "classification", "cnn", "deep_learning", "example"],
    },
    
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models",
        "results_dir": "results",
        "logs_dir": "logs",
    },
}
