"""
实验运行策略模块。

此模块实现了统一的实验运行器 ExperimentRunner。
它采用策略模式，通过委托给不同的 Evaluator 子类来执行具体的评估工作流
（如留出法、交叉验证、超参数优化）。
"""
import time
from abc import ABC, abstractmethod
from typing import Any, Dict

import numpy as np

from .core.config_models import ExperimentRootConfig
from .core.context import ExperimentContext
from .core.engine import BaseExecutionEngine
from .core.utils import get_environment_snapshot, get_logger, set_global_seed
from .core.registry import register_runner
from .core.config_adapter import ConfigAdapter
from .reporting.artifact_savers import DataSaver
from .evaluation.evaluators import create_evaluator, load_and_split_data
from .reporting.handlers import create_result_handler

logger = get_logger(__name__)


class BaseRunner(ABC):
    """所有实验运行器的抽象基类。"""
    def __init__(self, config: ExperimentRootConfig, engine: BaseExecutionEngine):
        self.config = config
        self.engine = engine
        self.context = ExperimentContext(config.model_dump())
        self.adapter = ConfigAdapter(self.config)
        self.logger = get_logger(f"runners.{self.__class__.__name__}")
        handler_name = self.config.experiment_config.result_handler
        self.handler = create_result_handler(handler_name, self.context)
        self.logger.info(f"已初始化结果处理器: {type(self.handler).__name__}")

    def _setup_experiment(self):
        self.logger.info("=" * 70)
        experiment_name = self.config.experiment_config.name
        experiment_type = self.adapter.get_experiment_type()
        engine_type = self.adapter.get_engine_type()
        self.logger.info(f"[START] [{self.__class__.__name__}] 开始执行: {experiment_name}")
        self.logger.info(f"实验类型: {experiment_type}, 引擎: {engine_type}")
        self.logger.info("=" * 70)
        self.handler.log_config(self.config.model_dump())
        set_global_seed(self.config.data_config.random_seed)
        env_snapshot = get_environment_snapshot()
        self.handler.log_artifact("environment", env_snapshot, "json", metadata={"type": "environment_snapshot"})

    def _save_traceability_data(self, X_train, X_test, y_train, y_test, ids_train, ids_test, level: str):
        """基于 traceability_level 保存调试数据。"""
        try:
            if level == "basic":
                # 保存分割索引
                split_indices_path = self.handler.experiment_dir / "debug_artifacts" / "split_indices.json"
                DataSaver.save_split_indices(ids_train, ids_test, split_indices_path)
                self.handler.log_artifact("split_indices", {"train_count": len(ids_train), "test_count": len(ids_test)}, "json", subdir="debug_artifacts")
                self.logger.info(f"已保存分割索引到: {split_indices_path}")

            elif level == "full":
                # 保存分割索引（basic 级别的内容）
                split_indices_path = self.handler.experiment_dir / "debug_artifacts" / "split_indices.json"
                DataSaver.save_split_indices(ids_train, ids_test, split_indices_path)
                self.handler.log_artifact("split_indices", {"train_count": len(ids_train), "test_count": len(ids_test)}, "json", subdir="debug_artifacts")

                # 保存原始训练和测试数据（采样限制避免内存峰值）
                sample_limit = 10000  # 默认采样限制

                X_train_raw_path = self.handler.experiment_dir / "debug_artifacts" / "X_train_raw.csv"
                DataSaver.save_matrix_csv("X_train", X_train, X_train_raw_path, id_column=ids_train, sample_limit=sample_limit)

                X_test_raw_path = self.handler.experiment_dir / "debug_artifacts" / "X_test_raw.csv"
                DataSaver.save_matrix_csv("X_test", X_test, X_test_raw_path, id_column=ids_test, sample_limit=sample_limit)

                self.logger.info(f"已保存原始数据到: debug_artifacts/ (采样限制: {sample_limit})")

        except Exception as e:
            self.logger.warning(f"保存 traceability 数据失败: {e}")

    def _inject_handler_to_pytorch_wrapper(self, model_pipeline):
        """为 PyTorch wrapper 注入 handler 以支持结构化训练日志。"""
        try:
            from .optimization.sklearn_wrappers import PyTorchSklearnWrapper

            # 检查 pipeline 中是否有 PyTorchSklearnWrapper
            for step_name, step_obj in model_pipeline.steps:
                if isinstance(step_obj, PyTorchSklearnWrapper):
                    step_obj.handler = self.handler
                    self.logger.debug(f"已为 PyTorch wrapper 注入 handler: {step_name}")
                    break
        except Exception as e:
            self.logger.warning(f"注入 handler 到 PyTorch wrapper 失败: {e}")

    def _finalize_experiment(self, start_time: float) -> Dict[str, Any]:
        execution_time = time.time() - start_time
        self.context.execution_time = execution_time
        self.handler.log_metric("execution_time_seconds", execution_time)
        results = self.handler.finalize()
        self.logger.info(f"实验在 {execution_time:.2f} 秒内完成")
        return results

    @abstractmethod
    def run(self) -> Dict[str, Any]:
        pass


class ExperimentRunner(BaseRunner):
    """统一的实验运行器，使用评估器策略。"""
    def run(self) -> Dict[str, Any]:
        start_time = time.time()
        self._setup_experiment()

        try:
            # 1. 数据加载和分割
            self.logger.info("步骤 1/4: 正在加载和处理数据...")
            X_train, X_test, y_train, y_test, ids_train, ids_test, groups_train, groups_test = load_and_split_data(self.config)

            # 将测试数据存入上下文，供评估器使用
            self.context.X_test = X_test
            self.context.y_test = y_test
            self.context.ids_test = ids_test
            self.context.groups_train = groups_train # 训练集的分组信息
            self.context.groups_test = groups_test

            self.logger.info(f"[OK] 数据准备就绪: {X_train.shape[0]} 训练/CV样本, {X_test.shape[0]} 最终测试样本")

            # 基于 traceability_level 保存调试数据
            traceability_level = self.adapter.get_traceability_level()
            if traceability_level in ["basic", "full"]:
                self._save_traceability_data(X_train, X_test, y_train, y_test, ids_train, ids_test, traceability_level)

            # 2. 构建模型管道
            self.logger.info(f"步骤 2/4: 正在使用 {type(self.engine).__name__} 构建模型管道...")
            dynamic_params = {'input_size': X_train.shape[1], 'n_outputs': len(np.unique(y_train))}
            model_pipeline = self.engine.build(self.config, dynamic_params=dynamic_params)

            # 为 PyTorch wrapper 设置 handler（如果是 PyTorch 引擎）
            self._inject_handler_to_pytorch_wrapper(model_pipeline)

            # 3. 创建评估器
            self.logger.info("步骤 3/4: 正在创建评估策略...")
            evaluator = create_evaluator(self.config.experiment_type, self.config, self.handler, self.context)
            self.logger.info(f"[OK] 已选择评估器: {type(evaluator).__name__}")

            # 4. 委托评估
            self.logger.info("步骤 4/4: 正在执行评估...")
            results = evaluator.evaluate(model_pipeline, X_train, y_train, groups_train, ids_train)
            self.handler.log_metrics_batch(results["metrics"])

        except Exception as e:
            self.logger.error(f"实验执行失败: {e}", exc_info=True)
            self.handler.handle_failure(e)
            raise

        return self._finalize_experiment(start_time)


# 向后兼容的 Runner 类 - 作为新架构的包装器
@register_runner("single_run")
class SingleRunRunner(BaseRunner):
    """单次运行实验的运行器（向后兼容）。"""
    def run(self) -> Dict[str, Any]:
        # 设置实验类型并委托给ExperimentRunner
        original_experiment_type = self.config.experiment_type
        self.config.experiment_type = "single_run"

        runner = ExperimentRunner(self.config, self.engine)
        runner.context = self.context
        runner.handler = self.handler
        runner.logger = self.logger

        try:
            result = runner.run()
        finally:
            # 恢复原始实验类型
            self.config.experiment_type = original_experiment_type

        return result


@register_runner("cross_validation")
class CrossValidationRunner(BaseRunner):
    """交叉验证实验的运行器（向后兼容）。"""
    def run(self) -> Dict[str, Any]:
        start_time = time.time()
        self._setup_experiment()

        try:
            # 1. 加载全量数据（不分割）
            self.logger.info("步骤 1/4: 正在加载全量数据用于交叉验证...")
            X, _, y, _, ids, _, groups, _ = load_and_split_data(self.config, perform_split=False)
            self.logger.info(f"[OK] 数据准备就绪: {X.shape[0]} 样本")

            # 2. 构建模型管道
            self.logger.info(f"步骤 2/4: 正在构建模型管道...")
            dynamic_params = {'input_size': X.shape[1], 'n_outputs': len(np.unique(y))}
            model_pipeline = self.engine.build(self.config, dynamic_params=dynamic_params)

            # 3. 创建交叉验证评估器
            self.logger.info("步骤 3/4: 正在创建交叉验证评估策略...")
            evaluator = create_evaluator("cross_validation", self.config, self.handler, self.context)

            # 4. 执行交叉验证
            self.logger.info("步骤 4/4: 正在执行交叉验证...")
            results = evaluator.evaluate(model_pipeline, X, y, groups=groups, ids=ids)
            self.handler.log_metrics_batch(results["metrics"])

        except Exception as e:
            self.logger.error(f"交叉验证实验失败: {e}", exc_info=True)
            self.handler.handle_failure(e)
            raise

        return self._finalize_experiment(start_time)


@register_runner("hyperparameter_optimization")
class HyperOptRunner(BaseRunner):
    """超参数优化实验的运行器（向后兼容）。"""
    def run(self) -> Dict[str, Any]:
        # 设置实验类型并委托给ExperimentRunner
        original_experiment_type = self.config.experiment_type
        self.config.experiment_type = "hyperparameter_optimization"

        runner = ExperimentRunner(self.config, self.engine)
        runner.context = self.context
        runner.handler = self.handler
        runner.logger = self.logger

        try:
            result = runner.run()
        finally:
            # 恢复原始实验类型
            self.config.experiment_type = original_experiment_type

        return result