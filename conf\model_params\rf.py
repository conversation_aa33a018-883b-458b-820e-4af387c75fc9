# conf/model_params/rf.py
"""
随机森林（RF）的模型参数预设。

这些预设为不同的使用场景（如快速开发、追求精度）提供了经过验证的参数组合。
"""

# 用于快速测试和开发
FAST_PARAMS = {
    "n_estimators": 10,
    "max_depth": 5,
    "min_samples_split": 2,
    "min_samples_leaf": 1,
    "random_state": 42
}

# 用于追求准确、稳健的结果
ACCURATE_PARAMS = {
    "n_estimators": 100,
    "max_depth": 10,
    "min_samples_split": 2,
    "min_samples_leaf": 1,
    "random_state": 42,
    "n_jobs": -1
}

# 默认参数（在速度和精度之间取得平衡）
DEFAULT_PARAMS = {
    "n_estimators": 100,
    "max_depth": None,
    "min_samples_split": 2,
    "min_samples_leaf": 1,
    "random_state": 42
}
