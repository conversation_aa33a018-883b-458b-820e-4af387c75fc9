"""
SOLID原则驱动的模型工厂测试 - 单元级精度验证
"""
import pytest
import numpy as np
from sklearn.base import BaseEstimator

# 导入被测试的函数和类
from my_spectral_framework.models.model_factory import get_model_instance
from my_spectral_framework.core.registry import list_registered_models

# 导入新的装饰器注册系统
from tests.core.test_registry import unit_test

# 定义所有需要测试的模型
TEST_CASES = [
    # 分类模型
    ("Classification", "RF", {"n_estimators": 10}),
    ("Classification", "SVM", {"probability": True}), # probability=True for predict_proba
    ("Classification", "ANN", {"max_iter": 50}),
    ("Classification", "PLS_DA", {"n_components": 5}),

    # 回归模型
    ("Regression", "RF", {"n_estimators": 10}),
    ("Regression", "SVR", {}),
    ("Regression", "ANN", {"max_iter": 50}),
    ("Regression", "PLS", {"n_components": 5}),
]

@unit_test(
    description="[OCP] 参数化测试：验证所有注册模型都能被工厂成功创建、训练和预测",
    tags=["model_factory", "registry", "critical", "smoke_test"],
    priority="critical"
)
@pytest.mark.parametrize("model_type, model_name, params", TEST_CASES)
def test_registry_driven_model_creation_and_fit(model_type, model_name, params):
    """
    职责：确保模型工厂能够为所有已知的、注册在案的模型类型正确地实例化、
    训练和预测，验证框架的插件化模型机制。
    """
    # 准备测试数据
    X_dummy = np.random.rand(20, 10)
    if model_type == "Classification":
        y_dummy = np.random.randint(0, 3, 20)
    else:
        y_dummy = np.random.rand(20)

    # 1. 创建模型实例
    # 注意：get_model_instance 现在只返回基础模型，不包含预处理器
    base_model = get_model_instance(
        model_type=model_type,
        model_name=model_name,
        model_params=params
    )

    # 2. 验证实例类型
    assert isinstance(base_model, BaseEstimator), f"{model_name} 应该是 BaseEstimator 的实例"

    # 3. 验证模型具有基本的 sklearn 接口
    assert hasattr(base_model, 'fit'), f"{model_name} 应该有 fit 方法"
    assert hasattr(base_model, 'predict'), f"{model_name} 应该有 predict 方法"

    # 4. 简单的拟合和预测测试
    try:
        base_model.fit(X_dummy, y_dummy)
        predictions = base_model.predict(X_dummy)
        assert predictions.shape == (20,), "预测结果的形状不正确"

        # 如果是分类器且支持，测试 predict_proba
        if model_type == "Classification" and hasattr(base_model, 'predict_proba'):
             # PLSDAAdapter does not have predict_proba in the same way
            if not "PLS_DA" in model_name:
                probas = base_model.predict_proba(X_dummy)
                assert probas.shape[0] == 20, "概率预测结果的样本数不正确"
                assert probas.shape[1] > 1, "概率预测结果的类别数不正确"

    except Exception as e:
        pytest.fail(f"模型 {model_name} ({model_type}) 的 fit/predict 失败: {e}")

@unit_test(
    description="[SRP] 验证模型注册表中包含了所有预期的模型",
    tags=["model_factory", "registry", "completeness"],
    priority="high"
)
def test_registry_has_all_necessary_models():
    """职责：确保模型注册表是完整的，没有遗漏任何核心模型。"""
    registered_models = list_registered_models()
    registered_keys = set(registered_models)

    expected_keys = set()
    for model_type, model_name, _ in TEST_CASES:
        key_name = model_name.upper()
        expected_keys.add(f"{model_type.lower()}_{key_name}")

    missing_models = expected_keys - registered_keys
    assert not missing_models, f"注册表中缺少以下模型: {missing_models}"
