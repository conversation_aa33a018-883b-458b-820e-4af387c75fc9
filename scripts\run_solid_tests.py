#!/usr/bin/env python3
"""
SOLID原则驱动的测试执行器 - v3.0 JUnit XML 版
"""
import argparse
import importlib
import json
import subprocess
import sys
import tempfile
import time
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Any, Dict, List

# 确保项目根目录在Python路径中
project_root = Path(__file__).resolve().parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
if str(project_root / "src") not in sys.path:
    sys.path.insert(0, str(project_root / "src"))

# --- 颜色定义 ---
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def discover_and_import_tests():
    """动态导入所有测试模块以触发装饰器注册。"""
    tests_dir = project_root / "tests"
    for path in tests_dir.rglob("*_solid.py"): # 只发现新的SOLID测试文件
        relative_path = path.relative_to(project_root)
        module_name = str(relative_path.with_suffix('')).replace('/', '.').replace('\\', '.')
        try:
            importlib.import_module(module_name)
        except ImportError as e:
            print(f"{Colors.WARNING}  - [WARNING] Could not import {module_name}: {e}{Colors.ENDC}", file=sys.stderr)

def parse_junit_xml(xml_path: Path) -> Dict[str, Any]:
    """解析 JUnit XML 文件并提取测试统计信息。"""
    try:
        tree = ET.parse(xml_path)
        root = tree.getroot()

        # 获取 testsuite 元素的属性
        testsuite = root if root.tag == 'testsuite' else root.find('testsuite')
        if testsuite is None:
            return {"total": 0, "passed": 0, "failed": 0, "errors": 0, "skipped": 0, "failures": []}

        total = int(testsuite.get('tests', 0))
        failures = int(testsuite.get('failures', 0))
        errors = int(testsuite.get('errors', 0))
        skipped = int(testsuite.get('skipped', 0))
        passed = total - failures - errors - skipped

        # 提取失败的测试名称
        failure_names = []
        for testcase in testsuite.findall('testcase'):
            if testcase.find('failure') is not None or testcase.find('error') is not None:
                classname = testcase.get('classname', '')
                name = testcase.get('name', '')
                failure_names.append(f"{classname}::{name}")

        return {
            "total": total,
            "passed": passed,
            "failed": failures,
            "errors": errors,
            "skipped": skipped,
            "failures": failure_names
        }
    except Exception as e:
        print(f"{Colors.WARNING}Warning: Failed to parse JUnit XML: {e}{Colors.ENDC}")
        return {"total": 0, "passed": 0, "failed": 0, "errors": 0, "skipped": 0, "failures": []}

def generate_report(results: Dict[str, Any], execution_time: float):
    """生成美化的总结报告。"""
    total = results.get('total', 0)
    passed = results.get('passed', 0)
    failed = results.get('failed', 0)
    errors = results.get('errors', 0)
    skipped = results.get('skipped', 0)

    print("\n" + "="*80)
    print(f"{Colors.BOLD}{Colors.HEADER}SOLID TEST FRAMEWORK SUMMARY (JUnit XML){Colors.ENDC}".center(90))
    print("="*80)

    success_rate = (passed / total * 100) if total > 0 else 0
    status_color = Colors.OKGREEN if (failed + errors) == 0 else Colors.FAIL
    print(f"  {Colors.BOLD}Status:{status_color} {'PASSED' if (failed + errors) == 0 else 'FAILED'}{Colors.ENDC}")
    print(f"  {Colors.BOLD}Execution Time:{Colors.ENDC} {execution_time:.2f} seconds")
    print(f"  {Colors.BOLD}Success Rate:{Colors.ENDC} {success_rate:.2f}%")

    print("\n" + "-"*40)
    print(f"  {Colors.BOLD}Test Breakdown:{Colors.ENDC}")
    print(f"    - {Colors.OKGREEN}Passed:{Colors.ENDC}  {passed}")
    print(f"    - {Colors.FAIL}Failed:{Colors.ENDC}  {failed}")
    print(f"    - {Colors.FAIL}Errors:{Colors.ENDC}  {errors}")
    print(f"    - {Colors.WARNING}Skipped:{Colors.ENDC} {skipped}")
    print(f"    - {Colors.BOLD}Total:{Colors.ENDC}   {total}")
    print("-" * 40)

    # 标准输出打印稳定摘要（按计划要求）
    print(f"Summary: total={total}, passed={passed}, failed={failed}, errors={errors}, skipped={skipped}")

    if (failed + errors) > 0 and results.get('failures'):
        print(f"\n{Colors.FAIL}{Colors.BOLD}Failed Tests Summary:{Colors.ENDC}")
        for failure in results['failures']:
            print(f"  - {Colors.FAIL}❌ {failure}{Colors.ENDC}")

    print("="*80)

def main():
    parser = argparse.ArgumentParser(description="SOLID原则驱动的测试执行器 - JUnit XML 版")
    parser.add_argument("--level", help="只运行指定级别的测试")
    parser.add_argument("--tag", help="只运行包含指定标签的测试")
    parser.add_argument("--priority", help="只运行指定优先级的测试")
    parser.add_argument("--stats", action="store_true", help="显示测试注册表统计信息")
    parser.add_argument("--junitxml-out", help="指定 JUnit XML 输出文件路径（可选）")
    parser.add_argument("-k", help="只运行匹配表达式的测试")
    parser.add_argument("-m", help="只运行标记的测试")
    parser.add_argument("--maxfail", help="在N次失败后停止")
    # 接受所有未被识别的参数，并将其传递给pytest
    args, unknown_args = parser.parse_known_args()

    # 显示 deprecation 提示
    print(f"{Colors.WARNING}[DEPRECATION] 已改用 JUnit XML 解析而不是 stdout 匹配，提高统计稳定性{Colors.ENDC}")

    discover_and_import_tests()
    from tests.core.test_registry import get_registered_tests, get_registry_stats

    if args.stats:
        print(json.dumps(get_registry_stats(), indent=2))
        return

    tests_to_run = get_registered_tests(level=args.level, tag=args.tag, priority=args.priority)
    if not tests_to_run:
        print(f"{Colors.OKGREEN}✅ No tests found matching the criteria. Nothing to run.{Colors.ENDC}")
        return

    test_paths = sorted(list(set(f"{test['module'].replace('.', '/')}.py" for test in tests_to_run)))

    # 创建临时 JUnit XML 文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as tmp_file:
        junit_path = Path(tmp_file.name)

    try:
        # 构建 pytest 命令
        cmd = [sys.executable, "-m", "pytest", "-v", f"--junitxml={junit_path}"] + test_paths

        # 添加透传参数
        if args.k:
            cmd.extend(["-k", args.k])
        if args.m:
            cmd.extend(["-m", args.m])
        if args.maxfail:
            cmd.extend(["--maxfail", args.maxfail])

        cmd.extend(unknown_args)

        start_time = time.time()
        process = subprocess.Popen(cmd, cwd=project_root, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8')

        stdout, stderr = process.communicate()
        end_time = time.time()

        print(stdout)
        if stderr:
            print(f"{Colors.WARNING}--- STDERR ---{Colors.ENDC}\n{stderr}")

        # 解析 JUnit XML 文件
        results = parse_junit_xml(junit_path)

        # 如果用户指定了输出路径，复制 XML 文件
        if args.junitxml_out:
            import shutil
            shutil.copy2(junit_path, args.junitxml_out)
            print(f"{Colors.OKCYAN}JUnit XML 已保存到: {args.junitxml_out}{Colors.ENDC}")

        generate_report(results, end_time - start_time)
        sys.exit(process.returncode)

    finally:
        # 清理临时文件
        if junit_path.exists():
            junit_path.unlink()

if __name__ == "__main__":
    main()
