"""
SOLID原则驱动的集成测试套件
"""
import json
import pytest
from pathlib import Path
import numpy as np
import pandas as pd
from sklearn.pipeline import Pipeline
from sklearn.base import BaseEstimator

# 导入被测试的模块
from my_spectral_framework.core.context import ExperimentContext
from my_spectral_framework.reporting.handlers import FileSystemResultHandler
from my_spectral_framework.core.engine import SklearnExecutionEngine, PytorchExecutionEngine, create_engine
from my_spectral_framework.core.config_models import (
    ExperimentRootConfig, ExperimentConfig, DataConfig, 
    PreprocessingConfig, ModelConfig, ExperimentType,
    FeatureSelectionConfig, EvaluationConfig, PathsConfig, SklearnEngineConfig
)
from my_spectral_framework.models.classic_ml._base import SpectralModelWrapper
from my_spectral_framework.optimization.sklearn_wrappers import PyTorchSklearnWrapper
from my_spectral_framework.core.steps import (
    DataLoadingStep, DataSplittingStep, EvaluationStep,
    FeatureSelectionStep, ModelTrainingStep, PreprocessingStep
)
from my_spectral_framework.runners import SingleRunRunner

# 导入新的装饰器注册系统
from tests.core.test_registry import integration_test

# --- Fixtures for PyTorch Engine Testing ---
@pytest.fixture
def sklearn_base_config(base_config):
    return ExperimentRootConfig.model_validate(base_config)

@pytest.fixture
def pytorch_base_config(base_config):
    config_dict = base_config.copy()
    config_dict['engine_config'] = {
        "engine": "pytorch",
        "pytorch_model_config": {
            "task_type": "Classification",
            "architecture": "CNN1D",
            "params": {"epochs": 1, "batch_size": 4, "device": "cpu"},
        }
    }
    return ExperimentRootConfig.model_validate(config_dict)

@pytest.fixture
def temp_output_dir(tmp_path):
    """创建临时输出目录"""
    return tmp_path / "test_output"

# --- Test Classes ---

class TestContextHandlerIntegration:
    """测试上下文和处理器的集成"""

    @integration_test(
        description="[SRP] 验证实验上下文正确初始化所有必要属性",
        tags=["context", "initialization", "integration"],
        priority="high"
    )
    def test_context_initialization(self, base_config):
        """测试上下文初始化"""
        context = ExperimentContext(base_config)
        assert context.config == base_config
        assert context.run_id is not None
        assert context.start_time is not None
        assert context.evaluation_results == {}
        assert context.errors == []

    @integration_test(
        description="[SRP] 验证FileSystemResultHandler能基于上下文成功创建实验目录结构",
        tags=["handler", "filesystem", "initialization", "critical"],
        priority="critical"
    )
    def test_handler_directory_creation(self, test_context, temp_output_dir):
        test_context.config["output_config"] = {"base_dir": str(temp_output_dir)}
        handler = FileSystemResultHandler(test_context)
        assert handler.experiment_dir.exists()
        assert (handler.experiment_dir / "models").exists()

    @integration_test(
        description="[ISP] 验证从上下文到处理器的数据流在finalization时是正确的",
        tags=["context", "handler", "data_flow"],
        priority="high"
    )
    def test_context_to_handler_data_flow_on_finalize(self, test_context, temp_output_dir):
        test_context.config["output_config"] = {"base_dir": str(temp_output_dir)}
        test_context.add_evaluation_results({"accuracy": 0.95})
        test_context.execution_time = 2.45
        handler = FileSystemResultHandler(test_context)
        handler.log_metrics_batch(test_context.evaluation_results)
        handler.finalize()
        summary_file = handler.experiment_dir / "experiment_summary.json"
        assert summary_file.exists()
        with open(summary_file, "r") as f:
            summary = json.load(f)
        assert summary["metrics"]["accuracy"]["value"] == 0.95
        assert summary["experiment_info"]["duration_seconds"] == 2.45

class TestExecutionEngineIntegration:
    """测试 ExecutionEngine 的集成功能"""

    @pytest.fixture
    def basic_config(self):
        """创建基本配置"""
        return ExperimentRootConfig(
            experiment_type=ExperimentType.SINGLE_RUN,
            experiment_config=ExperimentConfig(
                name="test_engine",
                description="测试执行引擎"
            ),
            data_config=DataConfig(
                source_type="opensa_test",
                test_size=0.3,
                random_seed=42
            ),
            preprocessing_config=PreprocessingConfig(
                steps=[{"method": "SNV", "params": {}}]
            ),
            feature_selection_config=FeatureSelectionConfig(
                method="Pca",
                params={"n_components": 10}
            ),
            evaluation_config=EvaluationConfig(),
            paths_config=PathsConfig(),
            engine_config=SklearnEngineConfig(
                engine="sklearn",
                ml_model_config=ModelConfig(
                    type="Classification",
                    name="RF",
                    params={"n_estimators": 10, "random_state": 42}
                )
            )
        )

    @integration_test(
        description="[SRP] 验证 SklearnExecutionEngine 能否成功构建一个sklearn Pipeline",
        tags=["engine", "sklearn", "build", "critical"],
        priority="critical"
    )
    def test_sklearn_engine_basic_build(self, basic_config):
        engine = SklearnExecutionEngine()
        model = engine.build(basic_config)
        assert isinstance(model, Pipeline)
        assert model.steps[-1][0] == "model"
        assert isinstance(model.steps[-1][1], BaseEstimator)

    @integration_test(
        description="[SRP] 验证 create_engine 工厂函数能否正确创建引擎实例",
        tags=["engine", "factory"],
        priority="high"
    )
    def test_create_engine_factory(self):
        assert isinstance(create_engine("sklearn"), SklearnExecutionEngine)
        assert isinstance(create_engine("pytorch"), PytorchExecutionEngine)
        with pytest.raises(ValueError):
            create_engine("invalid_engine")

class TestPipelineOrchestration:
    """测试Pipeline的完整集成"""

    @integration_test(
        description="[ISP] 验证完整数据处理流水线的端到端数据流",
        tags=["pipeline", "data_flow", "integration", "critical"],
        priority="critical"
    )
    def test_complete_data_pipeline_flow(self, test_context, base_config):
        payload = {}
        steps = [DataLoadingStep(base_config), DataSplittingStep(base_config)]
        for step in steps:
            payload = step.process(test_context, payload)
        expected_keys = ["X_raw", "y_raw", "X_train", "X_test", "y_train", "y_test"]
        for key in expected_keys:
            assert key in payload

    @integration_test(
        description="[ISP] 验证所有组件创建步骤能协同工作并构建一个有效的sklearn Pipeline",
        tags=["pipeline", "component_creation", "integration"],
        priority="high"
    )
    def test_complete_pipeline_component_flow(self, test_context, base_config):
        payload = {}
        steps = [PreprocessingStep(base_config), FeatureSelectionStep(base_config)]
        for step in steps:
            payload = step.process(test_context, payload)

        # 验证预处理器和特征选择器已创建
        assert "preprocessor" in payload
        assert "feature_selector" in payload

    @integration_test(
        description="[DIP] 验证评估步骤能正确处理来自上游步骤的数据和模型",
        tags=["pipeline", "evaluation", "integration"],
        priority="high"
    )
    def test_evaluation_step_integration(self, test_context, base_config):
        # 模拟完整的流水线输出
        X_test = np.random.rand(20, 20)
        y_test = np.random.randint(0, 2, 20)

        # 创建一个简单的训练模型用于测试
        from sklearn.ensemble import RandomForestClassifier
        model = RandomForestClassifier(n_estimators=5, random_state=42)
        model.fit(np.random.rand(50, 20), np.random.randint(0, 2, 50))

        # 生成预测结果
        y_pred = model.predict(X_test)

        payload = {
            "X_test": X_test,
            "y_test": y_test,
            "y_pred": y_pred,
            "trained_model": model
        }

        eval_step = EvaluationStep(base_config)
        result = eval_step.process(test_context, payload)

        assert "metrics" in result
        assert "accuracy" in result["metrics"]

class TestArtifactManagement:
    """测试工件管理的集成"""

    @integration_test(
        description="[SRP] 验证实验工件能被正确保存和检索",
        tags=["artifacts", "storage", "integration"],
        priority="high"
    )
    def test_artifact_storage_and_retrieval(self, test_context, temp_output_dir):
        test_context.config["output_config"] = {"base_dir": str(temp_output_dir)}
        handler = FileSystemResultHandler(test_context)

        # 验证处理器能正确创建目录结构
        assert handler.experiment_dir.exists()

        # 验证基本的日志功能
        test_metrics = {"accuracy": 0.95, "f1_score": 0.92}
        handler.log_metrics_batch(test_metrics)

        # 验证处理器能正常工作
        assert handler.experiment_dir.exists()
        assert (handler.experiment_dir / "models").exists()

class TestFailureHandling:
    """测试失败处理的集成"""

    @integration_test(
        description="[SRP] 验证实验上下文能正确记录和处理错误",
        tags=["failure_handling", "error_handling", "integration"],
        priority="high"
    )
    def test_error_recording_and_handling(self, test_context):
        # 记录一个错误
        test_error = Exception("Test error for integration testing")
        test_context.errors.append(test_error)

        # 验证错误被正确记录
        assert len(test_context.errors) == 1
        assert str(test_context.errors[0]) == "Test error for integration testing"

    @integration_test(
        description="[SRP] 验证Runner能优雅处理执行过程中的异常",
        tags=["failure_handling", "runner", "integration"],
        priority="high"
    )
    def test_runner_exception_handling(self, base_config):
        # 创建一个会导致错误的配置
        bad_config = base_config.copy()
        bad_config["data_config"]["source_type"] = "invalid_source"

        # 验证无效配置会导致错误
        from my_spectral_framework.data.factory import get_data_source
        with pytest.raises(ValueError):
            get_data_source(bad_config)
