"""
光谱分析框架的统一模型包装器基类。

本模块提供了 SpectralModelWrapper 类，它将预处理器和机器学习模型组合成一个统一的、
类型安全的 sklearn 兼容接口。这个设计消除了代码重复，提供了一致的 fit/predict 流程。

设计理念：采用适配器模式和组合模式，将预处理和模型训练/预测流程封装在一个类中，
确保所有模型都有相同的接口，同时支持灵活的预处理器配置。


License: Apache-2.0
"""

from typing import Any, Optional, Union

import numpy as np
from numpy.typing import NDArray
from sklearn.base import BaseEstimator, ClassifierMixin, RegressorMixin
from sklearn.cross_decomposition import PLSRegression
from sklearn.utils.validation import check_array, check_X_y

from ...core.utils import get_logger

logger = get_logger(__name__)


class PLSDAAdapter(BaseEstimator, ClassifierMixin):
    """
    PLS-DA 分类适配器。

    将 PLSRegression 的输出转换为分类预测，通过 argmax 操作
    将连续输出转换为类别标签。
    """
    
    def __init__(self, pls_model: PLSRegression):
        """
        初始化 PLS-DA 适配器。
        
        Args:
            pls_model: 已配置的 PLSRegression 实例
        """
        self.pls_model = pls_model
        self.classes_ = None
        
    def fit(self, X: NDArray[np.floating], y: NDArray[np.integer]) -> 'PLSDAAdapter':
        """
        训练 PLS-DA 模型。
        
        Args:
            X: 训练特征，形状 (n_samples, n_features)
            y: 训练标签，形状 (n_samples,)
            
        Returns:
            self: 返回自身实例
        """
        # 存储类别信息
        self.classes_ = np.unique(y)
        n_classes = len(self.classes_)
        
        # 将标签转换为 one-hot 编码用于 PLS 回归
        y_onehot = np.zeros((len(y), n_classes))
        for i, class_label in enumerate(self.classes_):
            y_onehot[y == class_label, i] = 1
            
        # 训练 PLS 模型
        self.pls_model.fit(X, y_onehot)
        return self
        
    def predict(self, X: NDArray[np.floating]) -> NDArray[np.integer]:
        """
        进行分类预测。
        
        Args:
            X: 测试特征，形状 (n_samples, n_features)
            
        Returns:
            预测的类别标签，形状 (n_samples,)
        """
        # 获取 PLS 回归预测
        y_pred_proba = self.pls_model.predict(X)
        
        # 通过 argmax 转换为类别预测
        y_pred_indices = np.argmax(y_pred_proba, axis=1)
        
        # 映射回原始类别标签
        return self.classes_[y_pred_indices]
        
    def predict_proba(self, X: NDArray[np.floating]) -> NDArray[np.floating]:
        """
        预测类别概率。
        
        Args:
            X: 测试特征，形状 (n_samples, n_features)
            
        Returns:
            类别概率，形状 (n_samples, n_classes)
        """
        # 获取 PLS 回归预测并进行 softmax 归一化
        y_pred_raw = self.pls_model.predict(X)
        
        # 简单的归一化处理（可以考虑使用 softmax）
        y_pred_proba = np.abs(y_pred_raw)  # 确保非负
        y_pred_proba = y_pred_proba / (y_pred_proba.sum(axis=1, keepdims=True) + 1e-8)
        
        return y_pred_proba


class SpectralModelWrapper(BaseEstimator, ClassifierMixin):
    """
    光谱分析的统一模型包装器。
    
    这个类将预处理器和机器学习模型组合成一个统一的 sklearn 兼容接口，
    提供标准的 fit/predict/predict_proba 方法，消除了代码重复并确保类型安全。
    
    设计理念：
    1. 组合模式：将预处理器和模型作为组件组合
    2. 适配器模式：为所有模型提供统一接口
    3. 管道模式：自动处理预处理 -> 模型训练/预测的流程
    """
    
    def __init__(
        self,
        preprocessor: Optional[BaseEstimator],
        model: BaseEstimator,
        task_type: str = "classification"
    ):
        """
        初始化光谱模型包装器。

        Args:
            preprocessor: sklearn 兼容的预处理器，可以为 None 或 "passthrough"
            model: sklearn 兼容的机器学习模型
            task_type: 任务类型，"classification" 或 "regression"
        """
        self.preprocessor = preprocessor
        self.model = model
        self.task_type = task_type  # 不修改原始参数，保持 sklearn clone 兼容性

        # 内部状态
        self.preprocessor_ = None
        self.model_ = None
        self.is_fitted_ = False
        
    def fit(self, X: NDArray[np.floating], y: NDArray[Union[np.integer, np.floating]]) -> 'SpectralModelWrapper':
        """
        训练光谱模型。
        
        执行完整的训练流程：数据验证 -> 预处理 -> 模型训练
        
        Args:
            X: 训练特征，形状 (n_samples, n_features)
            y: 训练标签，形状 (n_samples,)
            
        Returns:
            self: 返回自身实例
        """
        # 验证输入数据
        X, y = check_X_y(X, y)
        
        logger.info(f"开始训练 {self.task_type.lower()} 模型，数据形状: {X.shape}")
        
        # 1. 预处理步骤
        if self.preprocessor is None or self.preprocessor == "passthrough":
            # 无预处理
            self.preprocessor_ = None
            X_processed = X
            logger.info("跳过预处理步骤")
        else:
            # 应用预处理
            self.preprocessor_ = self.preprocessor
            X_processed = self.preprocessor_.fit_transform(X)
            logger.info(f"预处理完成，输出形状: {X_processed.shape}")
        
        # 2. 模型训练步骤
        self.model_ = self.model
        self.model_.fit(X_processed, y)
        
        # 3. 设置拟合状态
        self.is_fitted_ = True
        
        logger.info(f"{self.task_type.lower()} 模型训练完成")
        return self
        
    def predict(self, X: NDArray[np.floating]) -> NDArray[Union[np.integer, np.floating]]:
        """
        进行预测。
        
        执行完整的预测流程：数据验证 -> 预处理 -> 模型预测
        
        Args:
            X: 测试特征，形状 (n_samples, n_features)
            
        Returns:
            预测结果，形状 (n_samples,)
        """
        # 检查模型是否已训练
        if not self.is_fitted_:
            raise ValueError("模型尚未训练。请先调用 fit() 方法。")
            
        # 验证输入数据
        X = check_array(X)
        
        # 1. 预处理步骤
        if self.preprocessor_ is None:
            X_processed = X
        else:
            X_processed = self.preprocessor_.transform(X)
            
        # 2. 模型预测步骤
        predictions = self.model_.predict(X_processed)
        
        return predictions
        
    def predict_proba(self, X: NDArray[np.floating]) -> NDArray[np.floating]:
        """
        预测类别概率（仅适用于分类任务）。
        
        Args:
            X: 测试特征，形状 (n_samples, n_features)
            
        Returns:
            类别概率，形状 (n_samples, n_classes)
            
        Raises:
            ValueError: 如果模型不支持概率预测或任务类型不是分类
        """
        if self.task_type.lower() != "classification":
            raise ValueError("predict_proba 仅适用于分类任务")
            
        # 检查模型是否已训练
        if not self.is_fitted_:
            raise ValueError("模型尚未训练。请先调用 fit() 方法。")
            
        # 验证输入数据
        X = check_array(X)
        
        # 1. 预处理步骤
        if self.preprocessor_ is None:
            X_processed = X
        else:
            X_processed = self.preprocessor_.transform(X)
            
        # 2. 模型概率预测步骤
        if hasattr(self.model_, 'predict_proba'):
            probabilities = self.model_.predict_proba(X_processed)
        else:
            raise ValueError(f"模型 {type(self.model_).__name__} 不支持概率预测")
            
        return probabilities
        
    def score(self, X: NDArray[np.floating], y: NDArray[Union[np.integer, np.floating]]) -> float:
        """
        计算模型评分。
        
        Args:
            X: 测试特征
            y: 真实标签
            
        Returns:
            模型评分（分类任务返回准确率，回归任务返回 R² 分数）
        """
        if self.task_type.lower() == "classification":
            from sklearn.metrics import accuracy_score
            y_pred = self.predict(X)
            return accuracy_score(y, y_pred)
        else:  # regression
            from sklearn.metrics import r2_score
            y_pred = self.predict(X)
            return r2_score(y, y_pred)
            
    def get_params(self, deep: bool = True) -> dict:
        """
        获取模型参数（sklearn 兼容性要求）。
        
        Args:
            deep: 是否返回嵌套参数
            
        Returns:
            参数字典
        """
        params = {
            'preprocessor': self.preprocessor,
            'model': self.model,
            'task_type': self.task_type
        }
        
        if deep:
            # 获取预处理器参数
            if self.preprocessor is not None and hasattr(self.preprocessor, 'get_params'):
                preprocessor_params = self.preprocessor.get_params(deep=True)
                params.update({f'preprocessor__{k}': v for k, v in preprocessor_params.items()})
                
            # 获取模型参数
            if hasattr(self.model, 'get_params'):
                model_params = self.model.get_params(deep=True)
                params.update({f'model__{k}': v for k, v in model_params.items()})
                
        return params
        
    def set_params(self, **params) -> 'SpectralModelWrapper':
        """
        设置模型参数（sklearn 兼容性要求）。
        
        Args:
            **params: 要设置的参数
            
        Returns:
            self: 返回自身实例
        """
        # 分离直接参数和嵌套参数
        direct_params = {}
        preprocessor_params = {}
        model_params = {}
        
        for key, value in params.items():
            if key.startswith('preprocessor__'):
                preprocessor_params[key[14:]] = value  # 移除 'preprocessor__' 前缀
            elif key.startswith('model__'):
                model_params[key[7:]] = value  # 移除 'model__' 前缀
            else:
                direct_params[key] = value
                
        # 设置直接参数
        for key, value in direct_params.items():
            setattr(self, key, value)
            
        # 设置预处理器参数
        if preprocessor_params and self.preprocessor is not None:
            if hasattr(self.preprocessor, 'set_params'):
                self.preprocessor.set_params(**preprocessor_params)
                
        # 设置模型参数
        if model_params and hasattr(self.model, 'set_params'):
            self.model.set_params(**model_params)
            
        return self
