"""
结果处理器策略模式实现模块。

本模块实现了结果处理的策略模式，支持在不同的结果存储后端（文件系统、MLflow、Weights & Biases 等）
之间无缝切换，而无需修改核心实验逻辑。设计理念：采用策略模式和抽象工厂模式，
提供统一的结果处理接口，支持多种存储后端，确保实验结果的可追踪性和可扩展性。

Author: txy
License: Apache-2.0
"""

import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Union

import joblib
import numpy as np
import pandas as pd

from ..core.context import ExperimentContext
from ..core.utils import get_logger, json_custom_serializer
from ..core.registry import register_result_handler, get_result_handler_class
from ..core.config_adapter import ConfigAdapter
from ..storage import LocalStorageBackend
from ..registry import SQLiteArtifactRegistry

logger = get_logger(__name__)


class BaseResultHandler(ABC):
    """
    所有结果处理器的抽象基类。

    此接口定义了所有结果处理器必须遵循的契约，支持在不同存储后端之间无缝切换。
    设计理念：采用抽象基类定义统一接口，确保不同实现的一致性和可替换性。
    """

    def __init__(self, context: ExperimentContext):
        """
        使用实验上下文初始化处理器。

        Args:
            context: 包含元数据和配置的实验上下文
        """
        self.context = context
        self.logger = get_logger(f"handlers.{self.__class__.__name__}")

    @abstractmethod
    def log_metric(self, key: str, value: float, step: int = None) -> None:
        """
        记录标量评估指标。

        Args:
            key: 指标名称（如 'accuracy', 'rmse'）
            value: 指标值
            step: 用于跟踪指标演变的可选步骤编号
        """
        pass

    @abstractmethod
    def log_parameter(self, key: str, value: Any) -> None:
        """
        记录实验参数。

        Args:
            key: 参数名称（如 'learning_rate', 'n_estimators'）
            value: 参数值
        """
        pass

    @abstractmethod
    def log_artifact(
        self, artifact_name: str, data: Any, format: str, subdir: str = "", metadata: Dict[str, Any] = None
    ) -> None:
        """
        记录实验工件。

        Args:
            artifact_name: 工件名称
            data: 工件数据（模型、DataFrame、数组等）
            format: 文件格式（'joblib', 'csv', 'json', 'png' 等）
            subdir: 用于组织的子目录
            metadata: 关于工件的可选元数据
        """
        pass

    @abstractmethod
    def log_config(self, config: Dict[str, Any]) -> None:
        """
        记录实验配置。

        Args:
            config: 完整的实验配置
        """
        pass

    @abstractmethod
    def finalize(self) -> Dict[str, Any]:
        """
        完成实验并执行清理。

        Returns:
            Summary of the experiment results
        """
        pass

    @abstractmethod
    def log_figure(self, artifact_name: str, figure: Any, subdir: str = "figures") -> None:
        """
        记录一个matplotlib或类似图表的对象。

        Args:
            artifact_name: 图表的名称 (不含扩展名)
            figure: 图表对象 (例如, matplotlib.figure.Figure)
            subdir: 保存图表的子目录
        """
        pass

    @abstractmethod
    def handle_failure(self, exception: Exception) -> None:
        """
        处理实验失败情况。

        当实验运行过程中发生异常时，此方法负责保存失败信息，
        包括错误详情、堆栈跟踪、配置信息等，以便后续分析和调试。

        Args:
            exception: 导致实验失败的异常对象

        设计理念：
        - 失败也是有价值的信息，应该被妥善记录
        - 提供足够的上下文信息帮助调试
        - 确保失败不会导致数据丢失
        """
        pass

    def log_metrics_batch(self, metrics: Dict[str, float], step: int = None) -> None:
        """
        Record multiple metrics at once.

        Args:
            metrics: Dictionary of metric name -> value pairs
            step: Optional step number
        """
        for key, value in metrics.items():
            self.log_metric(key, value, step)

    def log_parameters_batch(self, parameters: Dict[str, Any]) -> None:
        """
        Record multiple parameters at once.

        Args:
            parameters: Dictionary of parameter name -> value pairs
        """
        for key, value in parameters.items():
            self.log_parameter(key, value)


@register_result_handler("filesystem")
class FileSystemResultHandler(BaseResultHandler):
    """
    Concrete implementation that saves results to the local filesystem.

    This handler creates a structured directory layout and saves all
    artifacts, metrics, and metadata as files.
    """

    def __init__(self, context: ExperimentContext):
        """Initialize filesystem handler with storage and registry backends."""
        super().__init__(context)

        # Create config adapter for unified access
        self.adapter = ConfigAdapter(self.context.config)

        # Generate unique experiment directory
        self.experiment_dir = self._generate_experiment_dir()
        self.results_dir = self.experiment_dir / "results"
        self.models_dir = self.experiment_dir / "models"
        self.predictions_dir = self.experiment_dir / "predictions"
        self.artifacts_dir = self.experiment_dir / "artifacts"

        # Create directory structure
        self._create_dirs()

        # Initialize storage and registry backends
        self.storage = LocalStorageBackend(base_path=self.experiment_dir)

        # SQLite 数据库放在实验目录下
        registry_db_path = self.experiment_dir / "artifacts_registry.db"
        self.registry = SQLiteArtifactRegistry(db_path=str(registry_db_path))

        # Initialize tracking
        self.logged_metrics = {}
        self.logged_parameters = {}
        self.logged_artifacts = []

        self.logger.info(f"Experiment directory: {self.experiment_dir}")
        self.logger.info(f"Storage backend: {type(self.storage).__name__}")
        self.logger.info(f"Registry backend: {type(self.registry).__name__}")

    def _generate_experiment_dir(self) -> Path:
        """Generate unique experiment directory name using ConfigAdapter."""
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

        # Use ConfigAdapter for unified access
        experiment_type = self.adapter.get_experiment_type()
        engine_type = self.adapter.get_engine_type()
        model_name = self.adapter.get_model_name()
        preprocessing_chain = self.adapter.get_preprocessing_chain()

        # Create descriptive directory name
        if experiment_type == "single_run":
            dir_name = f"{timestamp}_single_{engine_type}_{model_name}_{preprocessing_chain}"
        elif experiment_type == "cross_validation":
            dir_name = f"{timestamp}_cv_{engine_type}_{model_name}_{preprocessing_chain}"
        elif experiment_type == "hyperparameter_optimization":
            dir_name = f"{timestamp}_hyperopt_{engine_type}_{model_name}_{preprocessing_chain}"
        else:
            dir_name = f"{timestamp}_{experiment_type}_{engine_type}_{model_name}"

        # Get base directory from adapter
        base_dir = self.adapter.get_outputs_dir()
        return Path(base_dir) / dir_name

    def _create_dirs(self) -> None:
        """Create experiment directory structure."""
        for directory in [self.experiment_dir, self.results_dir, self.models_dir, self.predictions_dir, self.artifacts_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def log_metric(self, key: str, value: float, step: int = None) -> None:
        """Record a metric."""
        # Handle different value types
        if isinstance(value, (list, tuple)):
            # For list/tuple values, convert to string or take mean if numeric
            if len(value) > 0 and isinstance(value[0], (int, float)):
                processed_value = float(sum(value) / len(value))  # Take mean
            else:
                processed_value = str(value)  # Convert to string
        elif isinstance(value, (int, float)):
            processed_value = float(value)
        else:
            processed_value = str(value)  # Convert other types to string

        self.logged_metrics[key] = {"value": processed_value, "step": step, "timestamp": datetime.now().isoformat()}

        # Also update context for backward compatibility
        if hasattr(self.context, "evaluation_results"):
            if isinstance(processed_value, (int, float)):
                self.context.evaluation_results[key] = processed_value
            else:
                # For non-numeric values, store as string
                self.context.evaluation_results[key] = str(processed_value)

        self.logger.debug(f"Logged metric: {key} = {value}")

    def log_parameter(self, key: str, value: Any) -> None:
        """Record a parameter."""
        # Convert to JSON-serializable format
        if isinstance(value, (np.ndarray, pd.Series)):
            value = value.tolist()
        elif isinstance(value, np.generic):
            value = value.item()

        self.logged_parameters[key] = value
        self.logger.debug(f"Logged parameter: {key} = {value}")

    def log_artifact(
        self, artifact_name: str, data: Any, format: str, subdir: str = "", metadata: Dict[str, Any] = None
    ) -> None:
        """Record an artifact using storage and registry backends."""
        try:
            # 使用存储后端保存数据
            uri = self.storage.save(
                data=data,
                artifact_name=artifact_name,
                format=format,
                subdir=subdir,
                metadata=metadata
            )

            # 使用注册表记录元数据
            success = self.registry.register_artifact(
                run_id=self.context.run_id,
                artifact_name=artifact_name,
                uri=uri,
                format=format,
                metadata=metadata
            )

            if success:
                # 记录到本地跟踪列表（向后兼容）
                artifact_info = {
                    "name": artifact_name,
                    "path": uri,
                    "format": format,
                    "timestamp": datetime.now().isoformat(),
                    "metadata": metadata or {},
                }
                self.logged_artifacts.append(artifact_info)

                self.logger.info(f"产物 '{artifact_name}' 已保存并注册: {uri}")
            else:
                self.logger.warning(f"产物 '{artifact_name}' 保存成功但注册失败")

        except Exception as e:
            error_msg = f"保存产物 '{artifact_name}' 失败: {e}"
            self.logger.error(error_msg)
            if hasattr(self.context, "errors"):
                self.context.errors.append(error_msg)
            raise

    def log_config(self, config: Dict[str, Any]) -> None:
        """Record experiment configuration."""
        config_path = self.experiment_dir / "config.json"

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, default=json_custom_serializer)
            self.logger.info(f"Configuration saved to: {config_path}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")

    def finalize(self) -> Dict[str, Any]:
        """Finalize experiment and save summary."""
        try:
            # 确保 environment.json 存在
            self._ensure_environment_json()

            # 汇总关键训练指标
            key_metrics = self._extract_key_metrics()

            # Create experiment summary
            summary = {
                "experiment_info": {
                    "directory": str(self.experiment_dir),
                    "start_time": getattr(self.context, "start_time", None),
                    "end_time": datetime.now().isoformat(),
                    "duration_seconds": getattr(self.context, "execution_time", None),
                },
                "metrics": {
                    **self.logged_metrics,
                    "key_metrics": key_metrics  # 添加关键指标汇总
                },
                "parameters": self.logged_parameters,
                "artifacts": self.logged_artifacts,
                "context": self._extract_context_summary(),
            }

            # Save experiment summary
            summary_path = self.experiment_dir / "experiment_summary.json"
            with open(summary_path, "w", encoding="utf-8") as f:
                json.dump(summary, f, indent=2, default=json_custom_serializer)

            self.logger.info(f"Experiment summary saved to: {summary_path}")

            # Update context with experiment directory for backward compatibility
            if hasattr(self.context, "_experiment_dirname"):
                self.context._experiment_dirname = self.experiment_dir.name

            return summary

        except Exception as e:
            self.logger.error(f"Failed to finalize experiment: {e}")
            return {"error": str(e)}

    def _ensure_environment_json(self):
        """确保 environment.json 存在，如果不存在则生成。"""
        env_path = self.experiment_dir / "environment.json"
        if not env_path.exists():
            try:
                from ..core.utils import get_environment_snapshot
                env_snapshot = get_environment_snapshot()
                with open(env_path, 'w', encoding='utf-8') as f:
                    json.dump(env_snapshot, f, indent=2, default=json_custom_serializer)
                self.logger.info(f"Generated environment.json: {env_path}")
            except Exception as e:
                self.logger.warning(f"Failed to generate environment.json: {e}")

    def _extract_key_metrics(self) -> Dict[str, Any]:
        """提取关键训练指标的最新值。"""
        key_metrics = {}

        # 提取最后一次的训练损失
        if "train/loss" in self.logged_metrics:
            key_metrics["final_train_loss"] = self.logged_metrics["train/loss"]

        # 提取训练时间
        if "fit_time" in self.logged_metrics:
            key_metrics["fit_time"] = self.logged_metrics["fit_time"]

        # 提取最后一次的学习率
        if "train/learning_rate" in self.logged_metrics:
            key_metrics["final_learning_rate"] = self.logged_metrics["train/learning_rate"]

        # 提取最后一次的吞吐量
        if "train/throughput" in self.logged_metrics:
            key_metrics["final_throughput"] = self.logged_metrics["train/throughput"]

        return key_metrics

    def log_figure(self, artifact_name: str, figure: Any, subdir: str = "figures") -> None:
        """将matplotlib图表保存为.png文件并注册为产物。"""
        try:
            # 创建一个临时的二进制buffer来保存图表
            from io import BytesIO
            buf = BytesIO()
            figure.savefig(buf, format='png', bbox_inches='tight', dpi=300)
            buf.seek(0)

            # 使用log_artifact来保存buffer中的内容
            # 注意：log_artifact需要修改以支持直接接收bytes数据
            # 暂时我们先保存到临时文件
            temp_path = self.experiment_dir / "temp_fig.png"
            with open(temp_path, 'wb') as f:
                f.write(buf.read())

            with open(temp_path, 'rb') as f:
                self.log_artifact(artifact_name, f.read(), "png", subdir=subdir)

            temp_path.unlink()  # 删除临时文件
            self.logger.info(f"图表 '{artifact_name}' 已保存到 {subdir} 目录")

        except Exception as e:
            error_msg = f"记录图表 '{artifact_name}' 失败: {e}"
            self.logger.error(error_msg)
            if hasattr(self.context, "errors"):
                self.context.errors.append(error_msg)

    def handle_failure(self, exception: Exception) -> None:
        """
        处理实验失败情况，创建失败归档。

        当实验运行过程中发生异常时，此方法会：
        1. 创建标记为 _FAILED 的实验目录
        2. 保存错误信息和堆栈跟踪
        3. 尽力保存当前的配置和上下文信息
        4. 记录失败时间和环境信息

        Args:
            exception: 导致实验失败的异常对象
        """
        try:
            # 创建失败目录（在原目录名后添加 _FAILED 后缀）
            failed_dir_name = f"{self.experiment_dir.name}_FAILED"
            failed_dir = self.experiment_dir.parent / failed_dir_name

            # 如果失败目录已存在，添加时间戳避免冲突
            if failed_dir.exists():
                timestamp = datetime.now().strftime("%H%M%S")
                failed_dir_name = f"{self.experiment_dir.name}_FAILED_{timestamp}"
                failed_dir = self.experiment_dir.parent / failed_dir_name

            # 创建失败目录
            failed_dir.mkdir(parents=True, exist_ok=True)

            # 收集错误信息
            import traceback
            error_info = {
                "error_type": type(exception).__name__,
                "error_message": str(exception),
                "error_traceback": traceback.format_exc(),
                "failure_time": datetime.now().isoformat(),
                "run_id": self.context.run_id,
                "experiment_directory": str(self.experiment_dir),
                "failed_directory": str(failed_dir)
            }

            # 尝试保存配置信息
            try:
                if hasattr(self.context, 'config'):
                    error_info["config"] = self.context.config
            except Exception as config_error:
                error_info["config_error"] = f"无法保存配置: {config_error}"

            # 尝试保存上下文摘要
            try:
                error_info["context_summary"] = self._extract_context_summary()
            except Exception as context_error:
                error_info["context_error"] = f"无法保存上下文: {context_error}"

            # 尝试保存已记录的指标和参数
            try:
                error_info["logged_metrics"] = self.logged_metrics
                error_info["logged_parameters"] = self.logged_parameters
                error_info["logged_artifacts"] = self.logged_artifacts
            except Exception as log_error:
                error_info["log_error"] = f"无法保存日志信息: {log_error}"

            # 保存错误信息到 error.json
            error_file = failed_dir / "error.json"
            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump(error_info, f, indent=2, default=json_custom_serializer)

            # 尝试复制已有的产物到失败目录
            try:
                if self.experiment_dir.exists():
                    import shutil
                    for item in self.experiment_dir.iterdir():
                        if item.is_file():
                            shutil.copy2(item, failed_dir / item.name)
                        elif item.is_dir() and item.name != failed_dir.name:
                            shutil.copytree(item, failed_dir / item.name, dirs_exist_ok=True)
            except Exception as copy_error:
                # 复制失败不应该阻止错误记录
                self.logger.warning(f"复制现有产物到失败目录时出错: {copy_error}")

            # 更新实验目录为失败目录
            self.experiment_dir = failed_dir

            self.logger.error(f"实验失败已归档到: {failed_dir}")
            self.logger.error(f"错误类型: {type(exception).__name__}")
            self.logger.error(f"错误信息: {str(exception)}")

        except Exception as archive_error:
            # 如果失败归档本身也失败了，至少要记录到日志
            self.logger.critical(f"失败归档过程中发生错误: {archive_error}")
            self.logger.critical(f"原始错误: {type(exception).__name__}: {str(exception)}")

    def _extract_context_summary(self) -> Dict[str, Any]:
        """Extract key information from context."""
        summary = {}

        if hasattr(self.context, "data_dimensions"):
            summary["data_dimensions"] = self.context.data_dimensions

        if hasattr(self.context, "processing_steps"):
            summary["processing_steps"] = self.context.processing_steps

        if hasattr(self.context, "evaluation_results"):
            summary["evaluation_results"] = self.context.evaluation_results

        if hasattr(self.context, "fold_results"):
            summary["fold_results"] = self.context.fold_results

        if hasattr(self.context, "errors"):
            summary["errors"] = self.context.errors

        if hasattr(self.context, "is_cross_validation"):
            summary["is_cross_validation"] = self.context.is_cross_validation

        return summary

    def handle_failure(self, exception: Exception) -> str:
        """
        处理实验失败，保存错误快照。

        Args:
            exception: 发生的异常

        Returns:
            str: 错误快照文件的路径
        """
        import traceback
        from datetime import datetime

        # 收集异常信息
        error_info = {
            "type": type(exception).__name__,
            "message": str(exception),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.now().isoformat()
        }

        # 确定保存路径
        if hasattr(self, 'experiment_dir') and self.experiment_dir:
            # 如果实验目录已创建，保存在实验目录下
            error_path = self.experiment_dir / "error.json"
        else:
            # 如果实验目录未创建，保存在输出目录下
            outputs_dir = Path(self.adapter.get_outputs_dir() if hasattr(self, 'adapter') else "outputs")
            outputs_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            error_path = outputs_dir / f"error-{timestamp}.json"

        # 确保父目录存在
        error_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存错误信息
        try:
            with open(error_path, 'w', encoding='utf-8') as f:
                json.dump(error_info, f, indent=2, ensure_ascii=False)

            self.logger.error(f"错误快照已保存到: {error_path}")
            return str(error_path)

        except Exception as save_error:
            self.logger.error(f"保存错误快照失败: {save_error}")
            return str(error_path)  # 返回路径即使保存失败


@register_result_handler("mlflow")
class MLflowResultHandler(BaseResultHandler):
    """
    Concrete implementation that logs results to MLflow.

    This handler demonstrates how easy it is to switch to a different
    result storage backend without changing any experiment logic.

    Note: This is a demonstration implementation. To use it, install MLflow:
    pip install mlflow
    """

    def __init__(self, context: ExperimentContext, experiment_name: str = "spectral_analysis"):
        """Initialize MLflow handler."""
        super().__init__(context)

        try:
            import mlflow
            import mlflow.sklearn

            self.mlflow = mlflow

            # Set experiment
            mlflow.set_experiment(experiment_name)

            # Start run
            self.run = mlflow.start_run()
            self.logger.info(f"Started MLflow run: {self.run.info.run_id}")

        except ImportError:
            raise ImportError("MLflow is required for MLflowResultHandler. Install with: pip install mlflow")

    def log_metric(self, key: str, value: float, step: int = None) -> None:
        """Log metric to MLflow."""
        self.mlflow.log_metric(key, value, step)
        self.logger.debug(f"Logged metric to MLflow: {key} = {value}")

    def log_parameter(self, key: str, value: Any) -> None:
        """Log parameter to MLflow."""
        # Convert complex types to strings for MLflow
        if isinstance(value, (dict, list)):
            value = str(value)
        self.mlflow.log_param(key, value)
        self.logger.debug(f"Logged parameter to MLflow: {key} = {value}")

    def log_artifact(
        self, artifact_name: str, data: Any, format: str, subdir: str = "", metadata: Dict[str, Any] = None
    ) -> None:
        """Log artifact to MLflow."""
        import os
        import tempfile

        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=f".{format}", delete=False) as tmp_file:
            tmp_path = tmp_file.name

            try:
                # Save data to temporary file
                if format == "joblib":
                    joblib.dump(data, tmp_path)
                elif format == "csv":
                    if isinstance(data, pd.DataFrame):
                        data.to_csv(tmp_path, index=False)
                    else:
                        raise ValueError(f"Cannot save {type(data)} as CSV")
                elif format == "json":
                    with open(tmp_path, "w") as f:
                        json.dump(data, f, indent=2, default=json_custom_serializer)
                else:
                    raise ValueError(f"Unsupported format for MLflow: {format}")

                # Log to MLflow
                if subdir:
                    artifact_path = f"{subdir}/{artifact_name}.{format}"
                else:
                    artifact_path = f"{artifact_name}.{format}"

                self.mlflow.log_artifact(tmp_path, artifact_path)
                self.logger.info(f"Artifact '{artifact_name}' logged to MLflow")

            finally:
                # Clean up temporary file
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)

    def log_config(self, config: Dict[str, Any]) -> None:
        """Log configuration to MLflow."""
        # Flatten config for MLflow parameters
        flattened = self._flatten_dict(config)
        for key, value in flattened.items():
            self.log_parameter(key, value)

    def log_figure(self, artifact_name: str, figure: Any, subdir: str = "figures") -> None:
        """将matplotlib图表记录到MLflow。"""
        try:
            # 创建临时文件保存图表
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                figure.savefig(tmp_file.name, format='png', bbox_inches='tight', dpi=300)
                tmp_path = tmp_file.name

            try:
                # 记录到MLflow
                artifact_path = f"{subdir}/{artifact_name}.png"
                self.mlflow.log_artifact(tmp_path, artifact_path)
                self.logger.info(f"图表 '{artifact_name}' 已记录到 MLflow")
            finally:
                # 清理临时文件
                if os.path.exists(tmp_path):
                    os.unlink(tmp_path)

        except Exception as e:
            error_msg = f"记录图表到 MLflow 失败 '{artifact_name}': {e}"
            self.logger.error(error_msg)

    def finalize(self) -> Dict[str, Any]:
        """Finalize MLflow run."""
        try:
            # End MLflow run
            self.mlflow.end_run()
            self.logger.info(f"Ended MLflow run: {self.run.info.run_id}")

            return {
                "mlflow_run_id": self.run.info.run_id,
                "mlflow_experiment_id": self.run.info.experiment_id,
                "mlflow_run_url": f"http://localhost:5000/#/experiments/{self.run.info.experiment_id}/runs/{self.run.info.run_id}",
            }
        except Exception as e:
            self.logger.error(f"Failed to finalize MLflow run: {e}")
            return {"error": str(e)}

    def handle_failure(self, exception: Exception) -> None:
        """
        处理 MLflow 实验失败情况。

        Args:
            exception: 导致实验失败的异常对象
        """
        try:
            import traceback

            # 记录失败信息到 MLflow
            self.mlflow.log_param("experiment_status", "FAILED")
            self.mlflow.log_param("error_type", type(exception).__name__)
            self.mlflow.log_param("error_message", str(exception))

            # 记录失败时间
            self.mlflow.log_param("failure_time", datetime.now().isoformat())

            # 尝试记录堆栈跟踪（作为 artifact）
            try:
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                    f.write(traceback.format_exc())
                    f.flush()
                    self.mlflow.log_artifact(f.name, "error_traceback.txt")
            except Exception as trace_error:
                self.logger.warning(f"无法保存堆栈跟踪到 MLflow: {trace_error}")

            # 设置运行状态为失败
            self.mlflow.set_tag("mlflow.runName", f"FAILED_{self.context.run_id}")

            self.logger.error(f"MLflow 实验失败已记录: {type(exception).__name__}: {str(exception)}")

        except Exception as mlflow_error:
            self.logger.critical(f"记录失败信息到 MLflow 时出错: {mlflow_error}")
        finally:
            # 确保 MLflow 运行被正确结束
            try:
                self.mlflow.end_run(status="FAILED")
            except Exception as end_error:
                self.logger.warning(f"结束 MLflow 运行时出错: {end_error}")

    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = "", sep: str = ".") -> Dict[str, Any]:
        """Flatten nested dictionary for MLflow parameters."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)


def create_result_handler(handler_name: str, context: ExperimentContext) -> BaseResultHandler:
    """
    根据名称创建并返回一个结果处理器实例的工厂函数。

    🚀 重构：现在完全由注册表驱动，支持插件化扩展。用户可以通过
    @register_result_handler 装饰器添加自定义结果处理器，无需修改此工厂函数。

    Args:
        handler_name: 处理器名称 ('filesystem', 'mlflow', 'wandb' 等)
        context: 实验上下文

    Returns:
        配置好的结果处理器实例

    Raises:
        KeyError: 如果处理器名称未在注册表中找到
    """
    handler_name = handler_name.lower()

    # 🚀 从注册表获取处理器类
    handler_class = get_result_handler_class(handler_name)

    # 🔧 根据不同的处理器类型，使用适当的参数实例化
    if handler_name == "mlflow":
        # MLflow 需要额外的 experiment_name 参数
        exp_name = context.config.get("experiment_config", {}).get("name", "spectral_analysis")
        return handler_class(context, experiment_name=exp_name)
    else:
        # 大多数处理器只需要 context 参数
        return handler_class(context)
