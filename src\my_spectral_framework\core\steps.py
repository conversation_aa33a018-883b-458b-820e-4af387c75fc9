"""
管道步骤抽象模块。

本模块为光谱分析管道实现了“责任链”设计模式。
每个步骤都是一个独立的、可测试的、可重用的组件，可以自由组合，形成灵活的数据处理流程。

作者: txy
许可证: Apache-2.0
"""

from abc import ABC, abstractmethod  # 从abc模块导入ABC（抽象基类）和abstractmethod（抽象方法）
from typing import Any, Dict, Tuple  # 从typing模块导入类型提示，如字典（Dict）、任意类型（Any）、元组（Tuple）

import numpy as np  # 导入NumPy库，通常用于处理数值数组，这里用于数据操作
from sklearn.pipeline import Pipeline  # 导入Pipeline用于构建多步预处理流水线

from .context import ExperimentContext  # 从当前包的context模块导入ExperimentContext类，用于管理实验的元数据和结果
from .utils import get_logger, get_project_root  # 从当前包的utils模块导入get_logger函数和get_project_root函数

logger = get_logger(__name__)  # 获取当前模块的日志记录器，用于记录运行信息


class PipelineStep(ABC):
    """
    所有管道步骤的抽象基类。

    这个类定义了所有管道步骤必须遵循的基本结构和行为。
    它是一个抽象类，意味着你不能直接创建它的实例，
    而必须创建它的子类并实现其抽象方法。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化管道步骤。

        每个管道步骤在创建时都需要一个配置字典。
        这个配置字典包含了该步骤运行所需的参数。

        Args:
            config (Dict[str, Any]): 当前步骤的配置字典。
                                     例如，数据加载步骤可能需要文件路径，
                                     预处理步骤可能需要指定处理方法。
        """
        self.config = config  # 将传入的配置字典保存为实例属性
        self.name = self.__class__.__name__  # 获取当前类的名称作为步骤的名称，例如"DataLoadingStep"
        self.logger = get_logger(f"steps.{self.name}")  # 为当前步骤创建一个专用的日志记录器，方便追踪特定步骤的日志

    @abstractmethod
    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行当前步骤的逻辑。

        这是所有子类必须实现的抽象方法。
        它定义了每个管道步骤的核心功能：接收数据、处理数据，并传递给下一个步骤。

        Args:
            context (ExperimentContext): 实验上下文对象，用于存储和访问整个实验的元数据、
                                         中间结果和配置信息。它像一个共享的“黑板”，
                                         所有步骤都可以读写。
            payload (Dict[str, Any]): 负载字典，包含在管道步骤之间传递的数据。
                                       每个步骤都会从payload中读取所需数据，
                                       并将处理后的数据或新生成的数据添加到payload中。

        Returns:
            Dict[str, Any]: 更新后的负载字典。这个字典将作为下一个管道步骤的输入。
        """
        pass  # 抽象方法，子类必须实现它，这里只是一个占位符

    def __repr__(self):
        """
        返回当前步骤的字符串表示。

        当打印一个PipelineStep对象时，会调用这个方法，
        返回一个易于理解的字符串，例如"DataLoadingStep()"。
        """
        return f"{self.name}()"  # 返回步骤名称加上括号，表示这是一个可执行的步骤


class DataLoadingStep(PipelineStep):
    """
    加载原始光谱数据。

    这个步骤负责从各种来源加载光谱数据，并将其添加到管道的负载（payload）中，
    供后续步骤使用。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        加载数据并添加到负载中。

        根据配置中指定的数据源类型，从文件或测试数据集中加载光谱数据。
        加载的数据包括特征（X）、标签（y）、分组信息（groups）和元数据（metadata）。
        加载完成后，会将数据的形状、样本数、特征数等元信息添加到实验上下文中，
        并将原始数据添加到负载中。

        Args:
            context (ExperimentContext): 实验上下文，用于记录数据加载的元信息。
            payload (Dict[str, Any]): 负载字典，用于存储加载的原始数据。

        Returns:
            Dict[str, Any]: 更新后的负载字典，包含加载的原始数据。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：加载数据")  # 记录当前步骤的执行信息

        # 使用数据源工厂获取相应的数据源策略实例
        from ..data.factory import get_data_source

        # 1. 使用工厂获取对应的数据源策略实例
        data_source = get_data_source(self.config)

        # 2. 调用 load 方法，所有加载逻辑已在策略类中封装
        X, y, groups, ids, metadata = data_source.load()

        # 合并元数据，确保兼容性
        if metadata is None:
            metadata = {}

        # 将数据元数据添加到实验上下文中，以便后续报告和分析
        data_metadata = {
            "data_shape": X.shape,  # 数据的形状（样本数，特征数）
            "n_samples": X.shape[0],  # 样本数量
            "n_features": X.shape[1],  # 特征数量
            "n_classes": len(np.unique(y)),  # 类别数量（通过标签的唯一值计算）
            "has_groups": groups is not None,  # 是否包含分组信息
            "n_groups": len(np.unique(groups)) if groups is not None else 0,  # 分组数量
        }
        context.add_data_metadata(data_metadata)  # 将元数据添加到上下文

        # 更新负载字典，将加载的原始数据添加到其中
        payload.update(
            {
                "X_raw": X,  # 原始特征数据
                "y_raw": y,  # 原始标签数据
                "groups": groups,  # 分组信息
                "ids_raw": ids,  # 原始光谱ID
                "metadata": metadata,  # 额外元数据
            }
        )

        self.logger.info(f"数据加载完成: {X.shape[0]} 个样本, {X.shape[1]} 个特征")  # 记录数据加载结果
        return payload  # 返回更新后的负载字典


class DataSplittingStep(PipelineStep):
    """
    将数据分割成训练集和测试集。

    这个步骤负责将原始数据集按照一定的比例分割成用于模型训练的训练集
    和用于模型评估的测试集。它支持根据分组信息进行分割，以确保分组的完整性。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        分割数据并添加到负载中。

        从负载中获取原始特征（X_raw）、标签（y_raw）和分组信息（groups）。
        根据配置中的 `test_size` 和 `random_seed` 参数，使用 `train_test_split`
        函数进行数据分割。如果 `test_size` 为0，则所有数据都用于训练。
        分割完成后，将训练集和测试集的形状等元信息添加到实验上下文中，
        并将分割后的数据添加到负载中。

        Args:
            context (ExperimentContext): 实验上下文，用于记录数据分割的元信息。
            payload (Dict[str, Any]): 负载字典，包含原始数据，并用于存储分割后的数据。

        Returns:
            Dict[str, Any]: 更新后的负载字典，包含训练集和测试集。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：分割数据")  # 记录当前步骤的执行信息

        from sklearn.model_selection import train_test_split  # 导入Scikit-learn的train_test_split函数

        X_raw = payload["X_raw"]  # 从负载中获取原始特征数据
        y_raw = payload["y_raw"]  # 从负载中获取原始标签数据
        groups = payload.get("groups")  # 从负载中获取分组信息，如果没有则为None
        ids_raw = payload["ids_raw"]  # 从负载中获取原始光谱ID

        data_cfg = self.config["data_config"]  # 从步骤配置中获取数据配置
        test_size = data_cfg.get("test_size", 0.3)  # 获取测试集大小比例，默认为0.3
        random_seed = data_cfg.get("random_seed", 42)  # 获取随机种子，用于保证结果可复现性，默认为42

        if test_size == 0:
            # 如果测试集大小为0，表示不进行分割，所有数据都作为训练集
            X_train, X_test = X_raw, np.array([]).reshape(0, X_raw.shape[1])  # 训练集为全部数据，测试集为空数组
            y_train, y_test = y_raw, np.array([])  # 训练集标签为全部标签，测试集标签为空数组
            groups_train = groups  # 训练集分组信息为原始分组信息
            groups_test = None if groups is None else np.array([])  # 测试集分组信息为空
            ids_train, ids_test = ids_raw, []  # 训练集ID为全部ID，测试集ID为空
        else:
            # 根据任务类型决定是否使用分层抽样
            task_type = context.config.get("model_config", {}).get("type", "Classification")
            use_stratify = task_type == "Classification"
            stratify_param = y_raw if use_stratify else None

            if groups is not None:
                # 如果存在分组信息，则按分组进行分割，以保持组的完整性
                X_train, X_test, y_train, y_test, groups_train, groups_test, ids_train, ids_test = train_test_split(
                    X_raw,
                    y_raw,
                    groups,
                    ids_raw,  # 输入数据：特征、标签、分组、ID
                    test_size=test_size,  # 测试集大小比例
                    random_state=random_seed,  # 随机种子
                    stratify=stratify_param,  # 分层抽样（仅分类任务）
                )
            else:
                # 如果没有分组信息，则直接分割特征和标签
                X_train, X_test, y_train, y_test, ids_train, ids_test = train_test_split(
                    X_raw,
                    y_raw,
                    ids_raw,  # 输入数据：特征、标签、ID
                    test_size=test_size,  # 测试集大小比例
                    random_state=random_seed,  # 随机种子
                    stratify=stratify_param,  # 分层抽样（仅分类任务）
                )
                groups_train = groups_test = None  # 没有分组信息，所以都为None

        # 将分割元数据添加到实验上下文中
        context.add_split_metadata(
            X_train.shape,
            X_test.shape,  # 训练集和测试集的形状
            split_method="train_test_split",  # 分割方法
            test_size=test_size,  # 测试集大小
            random_seed=random_seed,  # 随机种子
        )

        # 更新负载字典，添加分割后的训练集和测试集数据
        payload.update(
            {
                "X_train": X_train,  # 训练集特征
                "X_test": X_test,  # 测试集特征
                "y_train": y_train,  # 训练集标签
                "y_test": y_test,  # 测试集标签
                "groups_train": groups_train,  # 训练集分组信息
                "groups_test": groups_test,  # 测试集分组信息
                "ids_train": ids_train,  # 训练集光谱ID
                "ids_test": ids_test,  # 测试集光谱ID
            }
        )

        self.logger.info(f"数据分割完成: {len(X_train)} 个训练样本, {len(X_test)} 个测试样本")  # 记录分割结果
        return payload  # 返回更新后的负载字典


class PreprocessingStep(PipelineStep):
    """
    为管道创建预处理转换器或预处理流水线。

    这个步骤负责根据配置选择并实例化一个或多个预处理转换器，
    并将它们组合成一个scikit-learn Pipeline。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建并返回预处理流水线。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：创建预处理流水线")

        from ..preprocessing.spectral_processors import get_transformer

        preprocessing_cfg_dict = self.config.get("preprocessing_config", {})

        # 兼容旧格式（单个字典）和新格式（包含steps列表的字典）
        if "steps" in preprocessing_cfg_dict:
            steps_config = preprocessing_cfg_dict["steps"]
        elif "method" in preprocessing_cfg_dict: # 向后兼容
            steps_config = [preprocessing_cfg_dict]
        else:
            steps_config = []

        if not steps_config or (len(steps_config) == 1 and steps_config[0]['method'] == 'None'):
            self.logger.info("没有配置预处理步骤，将跳过。")
            payload["preprocessor"] = ("preprocessor", "passthrough")
            context.add_preprocessing_metadata("None", {}, (0,0), (0,0))
            return payload

        # 构建预处理流水线
        pipeline_steps = []
        step_methods = []
        for i, step_cfg in enumerate(steps_config):
            method = step_cfg["method"]
            params = step_cfg.get("params", {})

            if method == "None":
                continue

            transformer = get_transformer(method, params)
            if transformer:
                step_name = f"{method.lower()}_{i}"
                pipeline_steps.append((step_name, transformer))
                step_methods.append(method)

        if not pipeline_steps:
            self.logger.info("所有预处理步骤均为'None'，将跳过。")
            payload["preprocessor"] = ("preprocessor", "passthrough")
            context.add_preprocessing_metadata("None", {}, (0,0), (0,0))
            return payload

        # 创建预处理流水线
        preprocessing_pipeline = Pipeline(pipeline_steps)

        context.add_preprocessing_metadata(
            method=" -> ".join(step_methods),
            params={step[0]: step[1].get_params() for step in pipeline_steps},
            initial_shape=(0, 0),
            final_shape=(0, 0),
        )

        payload["preprocessor"] = ("preprocessor", preprocessing_pipeline)

        self.logger.info(f"预处理流水线已配置: {' -> '.join(step_methods)}")
        return payload


class FeatureSelectionStep(PipelineStep):
    """
    为管道创建特征选择转换器。

    这个步骤负责根据配置选择并实例化一个特征选择器（Selector），
    例如PCA、Lars、UVE或CARS等。这个选择器将被添加到管道的负载中，
    用于在模型训练之前减少数据的维度或选择最重要的特征。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建并返回特征选择转换器。

        从步骤配置中获取特征选择方法和参数。根据指定的方法，
        实例化相应的特征选择器（如PCA, LarsSelector等）。
        将特征选择方法的元信息添加到实验上下文中，并将创建的选择器添加到负载中。

        Args:
            context (ExperimentContext): 实验上下文，用于记录特征选择的元信息。
            payload (Dict[str, Any]): 负载字典，用于存储创建的特征选择器。

        Returns:
            Dict[str, Any]: 更新后的负载字典，包含特征选择器。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：创建特征选择转换器")  # 记录当前步骤的执行信息

        # 使用统一的工厂函数创建特征选择器
        from ..feature_selection.spectral_selectors import get_selector

        fs_cfg = self.config["feature_selection_config"]  # 从步骤配置中获取特征选择配置
        method = fs_cfg["method"]  # 获取特征选择方法名称
        params = fs_cfg.get("params", {})  # 获取特征选择方法的参数，如果没有则为空字典

        # 使用工厂函数创建选择器，实现架构统一
        if method == "None":
            selector = "passthrough"  # 不进行任何特征选择，直接通过
        else:
            selector = get_selector(method, params)

        # 将特征选择元数据添加到实验上下文中
        context.add_feature_selection_metadata(
            method,
            params,  # 特征选择方法和参数
            initial_shape=(0, 0),  # 初始形状，将在管道执行时设置
            final_shape=(0, 0),  # 最终形状，将在管道执行时设置
        )

        # 将选择器添加到负载字典中，键为'feature_selector'
        payload["feature_selector"] = ("feature_selector", selector)

        self.logger.info(f"特征选择器 '{method}' 已为管道配置")  # 记录特征选择器的配置信息
        return payload  # 返回更新后的负载字典


class ModelTrainingStep(PipelineStep):
    """
    为管道创建模型实例。

    这个步骤负责根据配置选择并实例化一个机器学习模型。
    这个模型实例将被添加到管道的负载中，供后续的训练步骤使用。
    注意：这个步骤只创建模型实例，不进行实际的训练。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建并返回模型实例。

        从步骤配置中获取模型类型、名称和参数。使用 `get_model_instance` 函数
        实例化相应的模型。将模型的元信息添加到实验上下文中，并将创建的模型实例添加到负载中。

        Args:
            context (ExperimentContext): 实验上下文，用于记录模型的元信息。
            payload (Dict[str, Any]): 负载字典，用于存储创建的模型实例。

        Returns:
            Dict[str, Any]: 更新后的负载字典，包含模型实例。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：创建模型实例")  # 记录当前步骤的执行信息

        from ..models.model_factory import get_model_instance  # 动态导入模型工厂函数

        model_cfg = self.config["model_config"]  # 从步骤配置中获取模型配置
        model_type = model_cfg["type"]  # 获取模型类型（例如：分类、回归）
        model_name = model_cfg["name"]  # 获取模型名称（例如：SVM, RandomForest）
        # 获取模型参数，如果params或特定模型名称的参数不存在，则为空字典
        model_params = model_cfg.get("params", {}).get(model_name, {})

        # 获取模型实例（此时模型尚未训练）
        model_instance = get_model_instance(model_type, model_name, model_params)

        # 将模型元数据添加到实验上下文中
        context.add_model_metadata(model_name, model_type, model_params)

        # 将模型实例添加到负载字典中，键为'model'
        payload["model"] = ("model", model_instance)

        self.logger.info(f"模型 '{model_name}' 已为管道配置")  # 记录模型的配置信息
        return payload  # 返回更新后的负载字典


class PredictionStep(PipelineStep):
    """
    使用训练好的模型进行预测。

    这个步骤负责利用之前训练好的模型对测试集数据进行预测，
    并将预测结果添加到管道的负载中。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        进行预测并添加到负载中。

        从负载中获取训练好的模型（trained_model）和测试集特征（X_test）。
        如果测试集有数据，则使用模型进行预测；否则，预测结果为空数组。
        将预测结果（y_pred）添加到负载中。

        Args:
            context (ExperimentContext): 实验上下文（在此步骤中主要用于日志记录，但可扩展）。
            payload (Dict[str, Any]): 负载字典，包含训练好的模型和测试集特征，并用于存储预测结果。

        Returns:
            Dict[str, Any]: 更新后的负载字典，包含预测结果。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：进行预测")  # 记录当前步骤的执行信息

        trained_model = payload["trained_model"]  # 从负载中获取训练好的模型
        X_test = payload["X_test"]  # 从负载中获取测试集特征数据

        if len(X_test) > 0:
            # 如果测试集有数据，则使用训练好的模型进行预测
            y_pred = trained_model.predict(X_test)
        else:
            # 如果测试集没有数据，则预测结果为空数组
            y_pred = np.array([])

        # 更新负载字典，添加预测结果
        payload.update({"y_pred": y_pred})  # 预测的标签结果

        self.logger.info(f"预测完成: {len(y_pred)} 个样本")  # 记录预测样本数量
        return payload  # 返回更新后的负载字典


class EvaluationStep(PipelineStep):
    """
    计算并记录评估指标。

    这个步骤负责根据模型的预测结果和真实的测试集标签，
    计算各种性能评估指标，并将这些指标记录到实验上下文中，
    以便后续的报告和分析。
    """

    def process(self, context: ExperimentContext, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算指标并更新上下文。

        从负载中获取真实的测试集标签（y_test）和模型的预测结果（y_pred）。
        如果测试集和预测结果都存在，则根据模型类型计算相应的评估指标。
        将计算出的指标添加到实验上下文中，并存储在负载中供后续处理。

        Args:
            context (ExperimentContext): 实验上下文，用于记录评估结果和详细结果。
            payload (Dict[str, Any]): 负载字典，包含测试集标签和预测结果，并用于存储计算出的指标。

        Returns:
            Dict[str, Any]: 更新后的负载字典，包含评估指标。
        """
        self.logger.info(f"正在执行 {self.name} 步骤：计算评估指标")  # 记录当前步骤的执行信息

        from ..evaluation.metrics import calculate_metrics  # 动态导入计算指标的函数

        y_test = payload["y_test"]  # 从负载中获取真实的测试集标签
        y_pred = payload["y_pred"]  # 从负载中获取模型的预测结果

        if len(y_test) > 0 and len(y_pred) > 0:
            # 如果测试集和预测结果都存在，则进行评估
            # 获取模型配置（支持新的判别联合配置结构）
            if isinstance(self.config, dict):
                # 字典格式配置
                engine_config = self.config.get("engine_config", {})
                if "ml_model_config" in engine_config:
                    model_type = engine_config["ml_model_config"]["type"]
                elif "pytorch_model_config" in engine_config:
                    # 对于PyTorch引擎，默认为分类任务
                    model_type = "Classification"
                else:
                    # 兼容旧格式
                    model_cfg = self.config.get("ml_model_config") or self.config.get("model_config")
                    if model_cfg:
                        model_type = model_cfg["type"]
                    elif self.config.get("pytorch_model_config"):
                        model_type = "Classification"
                    else:
                        raise ValueError("无法确定模型类型：缺少有效的模型配置")
            else:
                # Pydantic对象
                if hasattr(self.config, 'engine_config'):
                    if hasattr(self.config.engine_config, 'ml_model_config'):
                        model_type = self.config.engine_config.ml_model_config.type
                    elif hasattr(self.config.engine_config, 'pytorch_model_config'):
                        model_type = "Classification"
                    else:
                        raise ValueError("无法确定模型类型：engine_config 中缺少有效的模型配置")
                else:
                    # 兼容旧格式
                    if hasattr(self.config, 'ml_model_config') and self.config.ml_model_config:
                        model_type = self.config.ml_model_config.type
                    elif hasattr(self.config, 'pytorch_model_config') and self.config.pytorch_model_config:
                        model_type = "Classification"
                    else:
                        raise ValueError("无法确定模型类型：缺少有效的模型配置")

            # 计算评估指标
            metrics = calculate_metrics(y_test, y_pred, model_type)

            # 将评估结果添加到实验上下文中
            context.add_evaluation_results(metrics)
            # 添加详细结果，包括真实值、预测值和指标，方便后续分析
            context.add_detailed_result(
                {
                    "y_true": y_test.tolist() if hasattr(y_test, "tolist") else y_test,  # 真实标签，转换为列表（如果不是）
                    "y_pred": y_pred.tolist() if hasattr(y_pred, "tolist") else y_pred,  # 预测标签，转换为列表（如果不是）
                    "metrics": metrics,  # 计算出的指标
                }
            )

            # 将指标存储在负载中，以便后续处理程序访问
            payload["metrics"] = metrics

            self.logger.info(f"评估完成: 计算了 {len(metrics)} 个指标")  # 记录评估完成信息
        else:
            self.logger.info("没有可用于评估的测试数据")  # 如果没有测试数据，则跳过评估
            payload["metrics"] = {}  # 负载中的指标设置为空字典

        return payload  # 返回更新后的负载字典
