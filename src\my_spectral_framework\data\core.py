"""
数据核心模块，提供面向对象的数据容器。

本模块实现了 SpectralData 类，为用户提供更直观、更易用的光谱数据交互方式。
这个类封装了光谱数据及其元数据，支持链式操作和 Jupyter Notebook 友好的显示。

设计理念：
1. 面向对象：将数据和操作封装在一起，提供更直观的API
2. 链式操作：支持方法链调用，提升代码可读性
3. Jupyter友好：提供美观的HTML显示和repr方法
4. 历史追踪：记录所有数据处理步骤，确保可重现性


License: Apache-2.0
"""
import numpy as np
from typing import Dict, Any, Optional, List
from ..core.utils import get_logger

logger = get_logger(__name__)


class SpectralData:
    """
    一个封装光谱数据及其元数据的面向对象容器。
    
    这个类为用户提供了一个更直观、更易用的方式来处理光谱数据，
    特别适合在 Jupyter Notebook 环境中进行交互式数据分析。
    
    主要特性：
    - 封装光谱数据 (X)、标签 (y) 和波长信息
    - 支持链式操作的预处理方法
    - 自动记录处理历史
    - Jupyter Notebook 友好的显示
    - 丰富的元数据支持
    
    Examples:
        >>> # 创建 SpectralData 对象
        >>> data = SpectralData(X, y, wavelengths=wavelengths)
        >>> print(data)  # 显示数据摘要
        
        >>> # 链式预处理
        >>> processed_data = data.preprocess('SNV').preprocess('SG', w=11, p=2)
        
        >>> # 波长切片
        >>> sliced_data = data.slice(1000, 2000)
    """
    
    def __init__(self, 
                 X: np.ndarray, 
                 y: np.ndarray, 
                 wavelengths: Optional[np.ndarray] = None, 
                 metadata: Optional[Dict[str, Any]] = None):
        """
        初始化 SpectralData 对象。
        
        Args:
            X: 光谱数据矩阵，形状为 (n_samples, n_features)
            y: 标签数组，形状为 (n_samples,)
            wavelengths: 波长数组，形状为 (n_features,)，可选
            metadata: 额外的元数据字典，可选
        """
        self.X = X
        self.y = y
        self.wavelengths = wavelengths
        self.metadata = metadata if metadata is not None else {}
        self._processing_history: List[Dict[str, Any]] = []
        
        logger.debug(f"创建 SpectralData 对象: {self.X.shape[0]} 样本, {self.X.shape[1]} 特征")

    def __repr__(self) -> str:
        """返回对象的字符串表示。"""
        return (f"SpectralData(samples={self.X.shape[0]}, features={self.X.shape[1]}, "
                f"classes={len(np.unique(self.y))}, history_steps={len(self._processing_history)})")
    
    def _repr_html_(self) -> str:
        """为 Jupyter Notebook 提供 HTML 表示。"""
        html = "<div style='border: 1px solid #ddd; padding: 10px; border-radius: 5px;'>"
        html += "<h4 style='margin-top: 0; color: #333;'>🔬 SpectralData Object</h4>"
        html += "<table style='border-collapse: collapse; width: 100%;'>"
        html += "<tr><td style='padding: 5px; border: 1px solid #eee;'><strong>Samples</strong></td>"
        html += f"<td style='padding: 5px; border: 1px solid #eee;'>{self.X.shape[0]}</td></tr>"
        html += "<tr><td style='padding: 5px; border: 1px solid #eee;'><strong>Features</strong></td>"
        html += f"<td style='padding: 5px; border: 1px solid #eee;'>{self.X.shape[1]}</td></tr>"
        html += "<tr><td style='padding: 5px; border: 1px solid #eee;'><strong>Classes</strong></td>"
        html += f"<td style='padding: 5px; border: 1px solid #eee;'>{len(np.unique(self.y))}</td></tr>"
        
        if self.wavelengths is not None:
            html += "<tr><td style='padding: 5px; border: 1px solid #eee;'><strong>Wavelength Range</strong></td>"
            html += f"<td style='padding: 5px; border: 1px solid #eee;'>{self.wavelengths.min():.2f} - {self.wavelengths.max():.2f}</td></tr>"
        
        html += "<tr><td style='padding: 5px; border: 1px solid #eee;'><strong>Processing Steps</strong></td>"
        html += f"<td style='padding: 5px; border: 1px solid #eee;'>{len(self._processing_history)}</td></tr>"
        html += "</table>"
        
        if self._processing_history:
            html += "<details style='margin-top: 10px;'>"
            html += "<summary style='cursor: pointer; color: #666;'>📋 Processing History</summary>"
            html += "<ul style='margin: 5px 0; padding-left: 20px;'>"
            for i, step in enumerate(self._processing_history):
                html += f"<li><strong>{i+1}.</strong> {step['method']}"
                if step['params']:
                    html += f" <small style='color: #666;'>({step['params']})</small>"
                html += "</li>"
            html += "</ul></details>"
        
        html += "</div>"
        return html

    def preprocess(self, method: str, **params) -> 'SpectralData':
        """
        对数据应用一个预处理方法（in-place）。
        
        Args:
            method: 预处理方法名称
            **params: 预处理方法的参数
            
        Returns:
            self: 支持链式调用
        """
        try:
            from ..preprocessing.spectral_processors import get_transformer
            
            transformer = get_transformer(method, params)
            if transformer:
                self.X = transformer.fit_transform(self.X)
                self._processing_history.append({'method': method, 'params': params})
                logger.info(f"应用预处理方法 '{method}': {self.X.shape}")
            else:
                logger.warning(f"未找到预处理方法: {method}")
        except Exception as e:
            logger.error(f"预处理方法 '{method}' 执行失败: {e}")
            
        return self

    def slice(self, wv_min: float, wv_max: float) -> 'SpectralData':
        """
        根据波数范围对光谱数据进行切片（in-place）。
        
        Args:
            wv_min: 最小波数
            wv_max: 最大波数
            
        Returns:
            self: 支持链式调用
            
        Raises:
            ValueError: 当波长信息不可用时
        """
        if self.wavelengths is None:
            raise ValueError("Wavelength information is not available for slicing.")
        
        indices = np.where((self.wavelengths >= wv_min) & (self.wavelengths <= wv_max))[0]
        self.X = self.X[:, indices]
        self.wavelengths = self.wavelengths[indices]
        self._processing_history.append({
            'method': 'slice', 
            'params': {'wv_min': wv_min, 'wv_max': wv_max}
        })
        
        logger.info(f"波长切片 [{wv_min}, {wv_max}]: {self.X.shape}")
        return self
    
    def copy(self) -> 'SpectralData':
        """
        创建当前对象的深拷贝。
        
        Returns:
            SpectralData: 新的 SpectralData 对象
        """
        new_data = SpectralData(
            X=self.X.copy(),
            y=self.y.copy(),
            wavelengths=self.wavelengths.copy() if self.wavelengths is not None else None,
            metadata=self.metadata.copy()
        )
        new_data._processing_history = self._processing_history.copy()
        return new_data
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取数据摘要信息。
        
        Returns:
            Dict: 包含数据摘要的字典
        """
        summary = {
            'n_samples': self.X.shape[0],
            'n_features': self.X.shape[1],
            'n_classes': len(np.unique(self.y)),
            'class_distribution': dict(zip(*np.unique(self.y, return_counts=True))),
            'processing_steps': len(self._processing_history),
            'has_wavelengths': self.wavelengths is not None
        }
        
        if self.wavelengths is not None:
            summary['wavelength_range'] = (float(self.wavelengths.min()), float(self.wavelengths.max()))
            
        return summary
