# ===================================================================
# My Spectral Framework v2 .gitignore
# 最后更新: 2025-07-25
# ===================================================================

# 忽略所有 lock 文件，强制使用 uv/pip install 从 pyproject.toml 安装
poetry.lock
pipfile.lock
uv.lock

# ===================================================================
# Python 构建产物和缓存
# ===================================================================
__pycache__/
*.py[cod]
*$py.class
*.egg
*.egg-info/
dist/
build/
*.so

# ===================================================================
# 虚拟环境
# ===================================================================
.venv/
venv/
ENV/
env/

# ===================================================================
# 项目生成的核心产物 (日志、输出、报告)
# ===================================================================
# 忽略实验输出和日志目录下的所有内容
# (目录本身通过 .gitkeep 文件保留)
outputs/*
!outputs/.gitkeep
logs/*
!logs/.gitkeep
test_outputs/

# ===================================================================
# 测试和覆盖率报告
# ===================================================================
.coverage
.coverage.*
coverage.xml
htmlcov/
pytest_cache/
.pytest_cache/
tests/reports/

# ===================================================================
# Jupyter Notebook 临时文件
# ===================================================================
.ipynb_checkpoints/

# ===================================================================
# 开发工具和数据库
# ===================================================================
# 忽略 SpecStory (AI 聊天记录)
.specstory/

# 忽略 Optuna 或其他实验跟踪数据库
*.db
*.sqlite
*.sqlite3
mlruns/

# ===================================================================
# IDE 和编辑器配置文件
# ===================================================================
.vscode/
.idea/
*.swp
*~

# ===================================================================
# 忽略用户特定的本地数据和配置
# ===================================================================
# 忽略包含硬编码路径和个人数据测试的配置
conf/runs/local_examples/

# 忽略包含硬编码路径的 notebooks
notebooks/local/

# 建议用户将真实数据放在 data/real/ 目录下，并忽略它
# 这可以防止意外提交大型或敏感的真实数据集
data/real/

# 忽略根目录下的特定、用户本地的运行脚本和配置
run_stratified_group_cv_cnn_hyperopt.py
config_stratified_group_cv_cnn_hyperopt.json
verify_traceability_success.py
# ===================================================================
# 操作系统生成的文件
# ===================================================================
.DS_Store
Thumbs.db
plan/