"""
光谱分析框架的数据分割工具模块。

本模块提供多种将光谱数据分割为训练集和测试集的方法，包括随机分割、SPXY 和 Kennard-Stone 算法。
设计理念：提供多样化的数据分割策略，确保训练集和测试集的代表性，支持交叉验证和样本选择算法，
保证模型评估的可靠性和泛化能力。

Author: txy
License: Apache-2.0
"""

from typing import List, Optional, Tuple

import numpy as np
from sklearn.model_selection import GroupKFold, KFold, StratifiedGroupKFold, StratifiedKFold, train_test_split

from ..core.utils import get_logger

logger = get_logger(__name__)

# 尝试导入 kennard-stone 包以支持高级交叉验证
try:
    from kennard_stone import KFold as KSKFold

    KENNARD_STONE_AVAILABLE = True
    logger.info("Kennard-Stone 包可用于高级交叉验证")
except ImportError:
    KSKFold = None
    KENNARD_STONE_AVAILABLE = False
    # 仅在模块级别警告一次
    logger.warning("未找到 'kennard-stone' 包。'kennard_stone' 交叉验证方法将不可用。")

# 防止重复警告的全局标志
_KENNARD_STONE_WARNING_SHOWN = not KENNARD_STONE_AVAILABLE


def get_cv_indices(
    X: np.ndarray, y: np.ndarray, groups: Optional[np.ndarray], cv_method: str, n_splits: int, random_state: int
) -> List[Tuple[np.ndarray, np.ndarray]]:
    """
    根据指定方法生成交叉验证的训练和验证索引。

    设计思路：提供多种交叉验证策略，包括分层分组、Kennard-Stone 和简单分层方法，
    确保验证过程的科学性和可靠性，特别适用于光谱数据的特殊需求。

    Args:
        X: 特征矩阵 (n_samples, n_features)
        y: 目标向量 (n_samples,)
        groups: 组标识符 (n_samples,) 或 None
        cv_method: 交叉验证方法 ("stratified_group_kfold", "kennard_stone", "stratified")
        n_splits: 交叉验证折数
        random_state: 用于可重现性的随机种子

    Returns:
        每个折的 (训练索引, 验证索引) 元组列表

    Raises:
        ValueError: 如果交叉验证方法不受支持或缺少必需数据
        ImportError: 如果所需包不可用
    """
    logger.info(f"--- 使用方法 {cv_method} 生成 {n_splits} 折的交叉验证索引 ---")

    if cv_method == "stratified_group_kfold":
        if groups is None:
            raise ValueError("'stratified_group_kfold' 需要组信息 ('group_col_name')。")

        splitter = StratifiedGroupKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
        cv_indices = list(splitter.split(X, y, groups))

        logger.info(f"生成了 {len(cv_indices)} 个分层分组折")
        for i, (train_idx, val_idx) in enumerate(cv_indices):
            train_groups = len(np.unique(groups[train_idx]))
            val_groups = len(np.unique(groups[val_idx]))
            logger.info(
                f"  第 {i+1} 折：{len(train_idx)} 个训练样本 ({train_groups} 个组)，"
                f"{len(val_idx)} 个验证样本 ({val_groups} 个组)"
            )

        return cv_indices

    elif cv_method == "kennard_stone":
        if not KENNARD_STONE_AVAILABLE:
            raise ImportError(
                "'kennard_stone' 交叉验证方法需要 'kennard-stone' 包。安装命令：pip install kennard-stone"
            )

        if groups is None:
            raise ValueError("'kennard_stone' 方法（组级别）需要组信息。")

        # 获取唯一组及其代表性光谱（均值）
        unique_groups, group_indices_map = np.unique(groups, return_inverse=True)
        X_group_representatives = np.array([X[group_indices_map == i].mean(axis=0) for i in range(len(unique_groups))])

        logger.info(f"使用 {len(unique_groups)} 个组代表进行 Kennard-Stone 交叉验证")

        # 在组代表上应用 Kennard-Stone 分割
        if KSKFold is not None:
            ks_kfold = KSKFold(n_splits=n_splits)
        else:
            raise ImportError("KSKFold 不可用")
        fold_indices = []

        for fold_idx, (_, val_group_indices) in enumerate(ks_kfold.split(X_group_representatives)):
            val_groups = unique_groups[val_group_indices]
            val_mask = np.isin(groups, val_groups)
            train_idx = np.where(~val_mask)[0]
            val_idx = np.where(val_mask)[0]
            fold_indices.append((train_idx, val_idx))

            logger.info(
                f"  第 {fold_idx+1} 折：{len(train_idx)} 个训练样本，"
                f"{len(val_idx)} 个验证样本 ({len(val_groups)} 个验证组)"
            )

        return fold_indices

    elif cv_method == "stratified":
        # 简单分层交叉验证（忽略组）
        from sklearn.model_selection import StratifiedKFold

        splitter = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
        cv_indices = list(splitter.split(X, y))

        logger.info(f"生成了 {len(cv_indices)} 个分层折（忽略组）")
        return cv_indices

    else:
        supported_methods = ["stratified_group_kfold", "kennard_stone", "stratified"]
        raise ValueError(f"不支持的交叉验证方法：'{cv_method}'。支持的方法：{supported_methods}")


def split_data(
    X: np.ndarray, y: np.ndarray, method: str = "random", test_size: float = 0.2, random_seed: int = 123
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    使用指定方法将数据分割为训练集和测试集。

    设计思路：提供多种数据分割算法，包括随机分割、SPXY（基于 X-Y 联合距离）
    和 Kennard-Stone（基于 X 空间距离）算法，确保训练集和测试集的代表性。

    Args:
        X: 特征矩阵，形状 (n_samples, n_features)
        y: 目标向量，形状 (n_samples,)
        method: 分割方法 ("random", "spxy", "ks")
        test_size: 测试集大小比例 (0.0 到 1.0)
        random_seed: 用于可重现性的随机种子

    Returns:
        元组 (X_train, X_test, y_train, y_test)

    Raises:
        ValueError: 如果方法不受支持
    """
    if method not in ["random", "spxy", "ks"]:
        raise ValueError(f"不支持的分割方法：{method}。支持的方法：['random', 'spxy', 'ks']")

    logger.info(f"使用 {method} 方法分割数据 (test_size={test_size})")

    if method == "random":
        return _random_split(X, y, test_size, random_seed)
    elif method == "spxy":
        return _spxy_split(X, y, test_size)
    elif method == "ks":
        return _ks_split(X, y, test_size)
    else:
        # This should never be reached due to earlier validation, but added for type safety
        raise ValueError(f"Unsupported method: {method}")


def _random_split(
    X: np.ndarray, y: np.ndarray, test_size: float, random_seed: int
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Random splitting using sklearn's train_test_split.

    Args:
        X: Feature matrix
        y: Target vector
        test_size: Ratio of test set size
        random_seed: Random seed

    Returns:
        Tuple of (X_train, X_test, y_train, y_test)
    """
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_seed)

    logger.info(f"Random split completed: {X_train.shape[0]} train, {X_test.shape[0]} test samples")
    return X_train, X_test, y_train, y_test


def _spxy_split(X: np.ndarray, y: np.ndarray, test_size: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    SPXY (Sample set Partitioning based on joint X-Y distances) algorithm.

    This algorithm considers both X and Y spaces when selecting representative samples.

    Args:
        X: Feature matrix
        y: Target vector
        test_size: Ratio of test set size

    Returns:
        Tuple of (X_train, X_test, y_train, y_test)
    """
    x_backup = X.copy()
    y_backup = y.copy()
    M = X.shape[0]
    N = round((1 - test_size) * M)
    samples = np.arange(M)

    # Standardize y for distance calculation
    y_std = (y - np.mean(y)) / np.std(y)

    # Initialize distance matrices
    D = np.zeros((M, M))
    Dy = np.zeros((M, M))

    # Calculate distance matrices
    for i in range(M - 1):
        xa = X[i, :]
        ya = y_std[i]
        for j in range((i + 1), M):
            xb = X[j, :]
            yb = y_std[j]
            D[i, j] = np.linalg.norm(xa - xb)
            Dy[i, j] = np.linalg.norm(ya - yb)

    # Normalize and combine distance matrices
    Dmax = np.max(D)
    Dymax = np.max(Dy)
    D = D / Dmax + Dy / Dymax

    # Find the first two points with maximum distance
    maxD = D.max(axis=0)
    index_row = D.argmax(axis=0)
    index_column = maxD.argmax()

    m = np.zeros(N, dtype=int)
    m[0] = index_row[index_column]
    m[1] = index_column

    dminmax = np.zeros(N)
    dminmax[1] = D[m[0], m[1]]

    # Select remaining points
    for i in range(2, N):
        pool = np.delete(samples, m[:i])
        dmin = np.zeros(M - i)

        for j in range(M - i):
            indexa = pool[j]
            d = np.zeros(i)
            for k in range(i):
                indexb = m[k]
                if indexa < indexb:
                    d[k] = D[indexa, indexb]
                else:
                    d[k] = D[indexb, indexa]
            dmin[j] = np.min(d)

        dminmax[i] = np.max(dmin)
        index = np.argmax(dmin)
        m[i] = pool[index]

    # Create train/test split
    m_complement = np.delete(np.arange(X.shape[0]), m)

    X_train = x_backup[m, :]
    y_train = y_backup[m]
    X_test = x_backup[m_complement, :]
    y_test = y_backup[m_complement]

    logger.info(f"SPXY split completed: {X_train.shape[0]} train, {X_test.shape[0]} test samples")
    return X_train, X_test, y_train, y_test


def _ks_split(X: np.ndarray, y: np.ndarray, test_size: float) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Kennard-Stone algorithm for sample selection.

    This algorithm selects samples based on X-space distances only,
    ensuring representative coverage of the feature space.

    Args:
        X: Feature matrix
        y: Target vector
        test_size: Ratio of test set size

    Returns:
        Tuple of (X_train, X_test, y_train, y_test)
    """
    M = X.shape[0]
    N = round((1 - test_size) * M)
    samples = np.arange(M)

    # Calculate distance matrix
    D = np.zeros((M, M))
    for i in range(M - 1):
        xa = X[i, :]
        for j in range((i + 1), M):
            xb = X[j, :]
            D[i, j] = np.linalg.norm(xa - xb)

    # Find the first two points with maximum distance
    maxD = np.max(D, axis=0)
    index_row = np.argmax(D, axis=0)
    index_column = np.argmax(maxD)

    m = np.zeros(N, dtype=int)
    m[0] = index_row[index_column]
    m[1] = index_column

    dminmax = np.zeros(N)
    dminmax[1] = D[m[0], m[1]]

    # Select remaining points
    for i in range(2, N):
        pool = np.delete(samples, m[:i])
        dmin = np.zeros(M - i)

        for j in range(M - i):
            indexa = pool[j]
            d = np.zeros(i)
            for k in range(i):
                indexb = m[k]
                if indexa < indexb:
                    d[k] = D[indexa, indexb]
                else:
                    d[k] = D[indexb, indexa]
            dmin[j] = np.min(d)

        dminmax[i] = np.max(dmin)
        index = np.argmax(dmin)
        m[i] = pool[index]

    # Create train/test split
    m_complement = np.delete(np.arange(X.shape[0]), m)

    X_train = X[m, :]
    y_train = y[m]
    X_test = X[m_complement, :]
    y_test = y[m_complement]

    logger.info(f"Kennard-Stone split completed: {X_train.shape[0]} train, {X_test.shape[0]} test samples")
    return X_train, X_test, y_train, y_test


def validate_split(X_train: np.ndarray, X_test: np.ndarray, y_train: np.ndarray, y_test: np.ndarray) -> None:
    """
    Validate the data split for common issues.

    Args:
        X_train: Training features
        X_test: Test features
        y_train: Training targets
        y_test: Test targets

    Raises:
        ValueError: If validation fails
    """
    # Check shapes consistency
    if X_train.shape[0] != y_train.shape[0]:
        raise ValueError("Training features and targets have different number of samples")

    if X_test.shape[0] != y_test.shape[0]:
        raise ValueError("Test features and targets have different number of samples")

    if X_train.shape[1] != X_test.shape[1]:
        raise ValueError("Training and test features have different number of features")

    # Check for empty sets
    if X_train.shape[0] == 0:
        raise ValueError("Training set is empty")

    if X_test.shape[0] == 0:
        raise ValueError("Test set is empty")

    # Log split statistics
    total_samples = X_train.shape[0] + X_test.shape[0]
    train_ratio = X_train.shape[0] / total_samples
    test_ratio = X_test.shape[0] / total_samples

    logger.info(f"Split validation passed:")
    logger.info(f"  Total samples: {total_samples}")
    logger.info(f"  Training: {X_train.shape[0]} samples ({train_ratio:.2%})")
    logger.info(f"  Test: {X_test.shape[0]} samples ({test_ratio:.2%})")
    logger.info(f"  Features: {X_train.shape[1]}")


def get_cv_splitter(cv_cfg: dict, groups=None):
    """
    根据配置创建并返回一个sklearn交叉验证分割器。

    Args:
        cv_cfg: 包含CV策略的配置字典 ('method', 'n_splits', 'random_state')
        groups: 分组数据（可选）

    Returns:
        一个配置好的sklearn交叉验证分割器实例
    """
    logger = get_logger(__name__)
    cv_method = cv_cfg.get("method", "stratified")
    n_splits = cv_cfg.get("n_splits", 5)
    random_state = cv_cfg.get("random_state", 42)

    logger.info(f"配置交叉验证: 方法={cv_method}, 折数={n_splits}, 分组数据={'有' if groups is not None else '无'}")

    if cv_method in ["stratified_group", "stratified_group_kfold"] and groups is not None:
        logger.info("使用 StratifiedGroupKFold - 确保同组样本不会同时出现在训练集和测试集中")
        return StratifiedGroupKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
    elif cv_method in ["group", "group_kfold"] and groups is not None:
        logger.info("使用 GroupKFold - 按组分割但不考虑标签分层")
        return GroupKFold(n_splits=n_splits)
    elif cv_method in ["stratified", "stratified_kfold"]:
        logger.info("使用 StratifiedKFold - 保持标签分布但不考虑分组")
        return StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
    elif cv_method in ["kfold", "simple"]:
        logger.info("使用 KFold - 简单随机分割")
        return KFold(n_splits=n_splits, shuffle=True, random_state=random_state)
    else:
        if groups is not None:
            logger.warning(f"未识别的CV方法 '{cv_method}'，但检测到分组信息，回退到 StratifiedGroupKFold")
            return StratifiedGroupKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
        else:
            logger.warning(f"未识别的CV方法 '{cv_method}'，回退到默认的 StratifiedKFold")
            return StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=random_state)
