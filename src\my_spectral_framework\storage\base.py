"""
存储后端抽象基类模块。

本模块定义了存储后端的抽象接口，支持不同类型的存储系统（本地文件系统、云存储、数据库等）。
采用策略模式设计，使得存储逻辑与业务逻辑完全解耦，提供统一的存储接口。

设计理念：
1. 策略模式：不同的存储后端实现相同的接口
2. 开闭原则：支持新的存储后端扩展，无需修改现有代码
3. 单一职责：每个存储后端只负责特定类型的存储操作
4. 依赖倒置：上层代码依赖抽象接口，而非具体实现


License: Apache-2.0
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, Optional, Union

from ..core.utils import get_logger

logger = get_logger(__name__)


class BaseStorageBackend(ABC):
    """
    存储后端的抽象基类。
    
    定义了所有存储后端必须实现的核心接口。存储后端的职责是处理数据的
    持久化存储和检索，支持多种数据格式和存储位置。
    
    设计理念：
    - 抽象化：定义统一的存储接口，隐藏具体实现细节
    - 可扩展性：支持不同类型的存储后端（本地、云端、数据库等）
    - 类型安全：明确的输入输出类型，确保接口契约
    - 错误处理：统一的错误处理机制
    """
    
    def __init__(self, base_path: Union[str, Path]):
        """
        初始化存储后端。
        
        Args:
            base_path: 存储的基础路径
        """
        self.base_path = Path(base_path)
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
    @abstractmethod
    def save(
        self, 
        data: Any, 
        artifact_name: str, 
        format: str, 
        subdir: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        保存数据到存储后端。
        
        Args:
            data: 要保存的数据对象
            artifact_name: 产物名称
            format: 数据格式（'joblib', 'csv', 'json', 'txt' 等）
            subdir: 子目录路径
            metadata: 可选的元数据信息
            
        Returns:
            str: 存储位置的 URI 或路径
            
        Raises:
            ValueError: 当格式不支持时
            IOError: 当存储操作失败时
        """
        pass
        
    @abstractmethod
    def load(self, uri: str, format: str) -> Any:
        """
        从存储后端加载数据。
        
        Args:
            uri: 数据的存储位置 URI 或路径
            format: 数据格式
            
        Returns:
            Any: 加载的数据对象
            
        Raises:
            FileNotFoundError: 当文件不存在时
            ValueError: 当格式不支持时
            IOError: 当加载操作失败时
        """
        pass
        
    @abstractmethod
    def get_uri(self, artifact_name: str, format: str, subdir: str = "") -> str:
        """
        获取产物的存储 URI。
        
        Args:
            artifact_name: 产物名称
            format: 数据格式
            subdir: 子目录路径
            
        Returns:
            str: 完整的存储 URI
        """
        pass
        
    @abstractmethod
    def exists(self, uri: str) -> bool:
        """
        检查指定 URI 的数据是否存在。
        
        Args:
            uri: 要检查的 URI
            
        Returns:
            bool: 如果数据存在则返回 True，否则返回 False
        """
        pass
        
    @abstractmethod
    def delete(self, uri: str) -> bool:
        """
        删除指定 URI 的数据。
        
        Args:
            uri: 要删除的数据 URI
            
        Returns:
            bool: 如果删除成功则返回 True，否则返回 False
        """
        pass
        
    @abstractmethod
    def list_artifacts(self, subdir: str = "") -> list:
        """
        列出指定子目录下的所有产物。
        
        Args:
            subdir: 子目录路径
            
        Returns:
            list: 产物信息列表
        """
        pass
        
    def get_metadata(self, uri: str) -> Dict[str, Any]:
        """
        获取产物的元数据信息。
        
        Args:
            uri: 产物的 URI
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        # 默认实现，子类可以重写
        return {
            "uri": uri,
            "backend_type": self.__class__.__name__,
            "base_path": str(self.base_path)
        }


class StorageError(Exception):
    """存储操作相关的异常类。"""
    pass


class UnsupportedFormatError(StorageError):
    """不支持的格式异常。"""
    pass


class StorageNotFoundError(StorageError):
    """存储位置不存在异常。"""
    pass
