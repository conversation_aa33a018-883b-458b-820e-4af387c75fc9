{"experiment_info": {"directory": "results\\2025-08-01_07-54-20_single_EngineType.PYTORCH_CNN1D_SNV", "start_time": "2025-08-01T07:54:20.825557", "end_time": "2025-08-01T07:54:23.345220", "duration_seconds": 2.498664379119873}, "metrics": {"train/loss": {"value": 1.1355471738747187, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "train/learning_rate": {"value": 0.001, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "train/epoch_time": {"value": 0.16324067115783691, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "train/throughput": {"value": 1329.3255808179283, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "fit_time": {"value": 0.3522176742553711, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "accuracy": {"value": 0.7311827956989247, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "precision": {"value": 0.7273900293255133, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "recall": {"value": 0.7311827956989247, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "f1_score": {"value": 0.7062375433675353, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "confusion_matrix": {"value": "[[13, 0, 4, 4], [0, 24, 0, 0], [12, 0, 7, 5], [0, 0, 0, 24]]", "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "precision_class_0.0": {"value": 0.52, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "recall_class_0.0": {"value": 0.6190476190476191, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "f1_class_0.0": {"value": 0.5652173913043478, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "precision_class_1.0": {"value": 1.0, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "recall_class_1.0": {"value": 1.0, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "f1_class_1.0": {"value": 1.0, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "precision_class_2.0": {"value": 0.6363636363636364, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "recall_class_2.0": {"value": 0.2916666666666667, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "f1_class_2.0": {"value": 0.4, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "precision_class_3.0": {"value": 0.7272727272727273, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "recall_class_3.0": {"value": 1.0, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "f1_class_3.0": {"value": 0.8421052631578947, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "execution_time_seconds": {"value": 2.498664379119873, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "key_metrics": {"final_train_loss": {"value": 1.1355471738747187, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "fit_time": {"value": 0.3522176742553711, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "final_learning_rate": {"value": 0.001, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}, "final_throughput": {"value": 1329.3255808179283, "step": null, "timestamp": "2025-08-01T07:54:23.338221"}}}, "parameters": {}, "artifacts": [{"name": "environment", "path": "E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\results\\2025-08-01_07-54-20_single_EngineType.PYTORCH_CNN1D_SNV\\results\\environment.json", "format": "json", "timestamp": "2025-08-01T07:54:22.940074", "metadata": {"type": "environment_snapshot"}}, {"name": "final_model", "path": "E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\results\\2025-08-01_07-54-20_single_EngineType.PYTORCH_CNN1D_SNV\\models\\final_model.joblib", "format": "joblib", "timestamp": "2025-08-01T07:54:23.333657", "metadata": {}}, {"name": "predictions", "path": "E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\results\\2025-08-01_07-54-20_single_EngineType.PYTORCH_CNN1D_SNV\\predictions\\predictions.csv", "format": "csv", "timestamp": "2025-08-01T07:54:23.338221", "metadata": {}}], "context": {"data_dimensions": {}, "processing_steps": ["evaluation"], "evaluation_results": {"train/loss": 1.1355471738747187, "train/learning_rate": 0.001, "train/epoch_time": 0.16324067115783691, "train/throughput": 1329.3255808179283, "fit_time": 0.3522176742553711, "accuracy": 0.7311827956989247, "precision": 0.7273900293255133, "recall": 0.7311827956989247, "f1_score": 0.7062375433675353, "confusion_matrix": "[[13, 0, 4, 4], [0, 24, 0, 0], [12, 0, 7, 5], [0, 0, 0, 24]]", "precision_class_0.0": 0.52, "recall_class_0.0": 0.6190476190476191, "f1_class_0.0": 0.5652173913043478, "precision_class_1.0": 1.0, "recall_class_1.0": 1.0, "f1_class_1.0": 1.0, "precision_class_2.0": 0.6363636363636364, "recall_class_2.0": 0.2916666666666667, "f1_class_2.0": 0.4, "precision_class_3.0": 0.7272727272727273, "recall_class_3.0": 1.0, "f1_class_3.0": 0.8421052631578947, "execution_time_seconds": 2.498664379119873}, "fold_results": [], "errors": [], "is_cross_validation": false}}