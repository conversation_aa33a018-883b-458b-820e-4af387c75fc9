"""
Comprehensive test script for hyperparameter optimization functionality.

This script tests:
1. Grid search optimization with Random Forest
2. Random search optimization with SVM
3. Result saving and loading
4. Configuration validation

Author: txy
License: Apache-2.0
"""

import pytest
pytest.skip("Legacy optimizer test skipped: will be migrated to new tuner flow", allow_module_level=True)

import json
import sys
from pathlib import Path

import numpy as np
import pandas as pd

from my_spectral_framework.core.utils import get_logger
from my_spectral_framework.optimization.optimizer import HyperparameterTuner

# 项目路径设置（现在通过包安装自动处理）


logger = get_logger(__name__)


def test_grid_search_rf():
    """Test grid search optimization with Random Forest."""
    print("🧪 Testing Grid Search with Random Forest...")

    config = {
        "experiment_type": "hyperparameter_optimization",
        "data_config": {
            "source_type": "advanced_file",
            "file_path": "data/raw/spectrai_advanced_test_data.csv",
            "label_col_name": "label",
            "spectrum_unique_id_col_name": "spectrum_id",
            "group_col_name": "sample_group",
            "test_size": 0.3,
            "random_seed": 42,
        },
        "preprocessing_config": {"method": "SNV", "params": {}},
        "feature_selection_config": {"method": "Pca", "params": {"n_components": 10}},
        "model_config": {"type": "Classification", "name": "RF", "params": {"RF": {"random_state": 42}}},
        "hyperparameter_optimization_config": {
            "strategy": "grid_search",
            "cv_folds": 3,
            "scoring": "accuracy",
            "param_grid": {"model__n_estimators": [10, 20], "model__max_depth": [3, 5]},
        },
        "experiment_config": {
            "name": "test_rf_grid_search",
            "description": "Test RF grid search",
            "tags": ["test"],
            "log_level": "INFO",
            "save_results": True,
        },
    }

    try:
        tuner = HyperparameterTuner(config)
        results = tuner.tune_and_evaluate()

        # Validate results
        assert "best_results" in results
        assert "optimization_config" in results
        assert "search_summary" in results

        best_results = results["best_results"]
        assert "best_cv_score" in best_results
        assert "best_params" in best_results
        assert "test_score" in best_results

        # Check that we tested the expected number of combinations
        expected_combinations = 2 * 2  # n_estimators * max_depth
        assert results["search_summary"]["total_fits"] == expected_combinations

        print(f"  ✓ Grid search completed successfully")
        print(f"  ✓ Best CV score: {best_results['best_cv_score']:.4f}")
        print(f"  ✓ Test score: {best_results['test_score']:.4f}")
        print(f"  ✓ Best parameters: {best_results['best_params']}")

        return True

    except Exception as e:
        print(f"  ❌ Grid search test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_random_search_svm():
    """Test random search optimization with SVM."""
    print("\n🧪 Testing Random Search with SVM...")

    from scipy.stats import uniform

    config = {
        "experiment_type": "hyperparameter_optimization",
        "data_config": {
            "source_type": "advanced_file",
            "file_path": "data/raw/spectrai_advanced_test_data.csv",
            "label_col_name": "label",
            "spectrum_unique_id_col_name": "spectrum_id",
            "group_col_name": "sample_group",
            "test_size": 0.3,
            "random_seed": 42,
        },
        "preprocessing_config": {"method": "MSC", "params": {}},
        "feature_selection_config": {"method": "Lars", "params": {"nums": 15}},
        "model_config": {"type": "Classification", "name": "SVM", "params": {"SVM": {"kernel": "rbf", "random_state": 42}}},
        "hyperparameter_optimization_config": {
            "strategy": "random_search",
            "cv_folds": 3,
            "n_iter": 5,  # Small number for testing
            "scoring": "f1_weighted",
            "param_grid": {"model__C": uniform(0.1, 10), "model__gamma": ["scale", "auto", 0.001, 0.01, 0.1, 1.0]},
        },
        "experiment_config": {
            "name": "test_svm_random_search",
            "description": "Test SVM random search",
            "tags": ["test"],
            "log_level": "INFO",
            "save_results": True,
        },
    }

    try:
        tuner = HyperparameterTuner(config)
        results = tuner.tune_and_evaluate()

        # Validate results
        assert "best_results" in results
        assert "optimization_config" in results
        assert "search_summary" in results

        best_results = results["best_results"]
        assert "best_cv_score" in best_results
        assert "best_params" in best_results
        assert "test_score" in best_results

        # Check that we tested the expected number of iterations
        expected_iterations = 5
        assert results["search_summary"]["total_fits"] == expected_iterations

        print(f"  ✓ Random search completed successfully")
        print(f"  ✓ Best CV score: {best_results['best_cv_score']:.4f}")
        print(f"  ✓ Test score: {best_results['test_score']:.4f}")
        print(f"  ✓ Best parameters: {best_results['best_params']}")

        return True

    except Exception as e:
        print(f"  ❌ Random search test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_result_files():
    """Test that optimization results are properly saved."""
    print("\n🧪 Testing Result File Generation...")

    outputs_dir = Path("outputs")
    if not outputs_dir.exists():
        print("  ❌ No outputs directory found")
        return False

    # Find recent optimization experiments
    experiment_dirs = [d for d in outputs_dir.iterdir() if d.is_dir()]
    optimization_dirs = []

    for exp_dir in experiment_dirs:
        optimization_subdir = exp_dir / "optimization"
        if optimization_subdir.exists():
            optimization_dirs.append(exp_dir)

    if not optimization_dirs:
        print("  ❌ No optimization result directories found")
        return False

    # Check the most recent optimization directory
    latest_dir = max(optimization_dirs, key=lambda x: x.stat().st_mtime)
    optimization_subdir = latest_dir / "optimization"

    print(f"  Checking optimization results in: {latest_dir.name}")

    # Check required files
    required_files = ["cv_results_detailed.csv", "best_model.joblib", "optimization_summary.json"]

    for file_name in required_files:
        file_path = optimization_subdir / file_name
        if not file_path.exists():
            print(f"  ❌ Missing required file: {file_name}")
            return False
        print(f"  ✓ Found: {file_name}")

    # Validate CV results CSV
    try:
        cv_results = pd.read_csv(optimization_subdir / "cv_results_detailed.csv")
        print(f"  ✓ CV results CSV loaded: {cv_results.shape}")

        # Check required columns
        required_columns = ["mean_test_score", "std_test_score", "rank_test_score"]
        for col in required_columns:
            if col not in cv_results.columns:
                print(f"  ❌ Missing column in CV results: {col}")
                return False

        print(f"  ✓ CV results format validated")

    except Exception as e:
        print(f"  ❌ Error reading CV results: {e}")
        return False

    # Validate optimization summary JSON
    try:
        with open(optimization_subdir / "optimization_summary.json", "r") as f:
            summary = json.load(f)

        required_keys = ["optimization_config", "best_results", "search_summary"]
        for key in required_keys:
            if key not in summary:
                print(f"  ❌ Missing key in summary: {key}")
                return False

        print(f"  ✓ Optimization summary format validated")

    except Exception as e:
        print(f"  ❌ Error reading optimization summary: {e}")
        return False

    print(f"  ✓ All result files validated successfully")
    return True


def main():
    """Run all hyperparameter optimization tests."""
    print("=" * 70)
    print("HYPERPARAMETER OPTIMIZATION COMPREHENSIVE TEST")
    print("=" * 70)

    tests = [
        ("Grid Search with Random Forest", test_grid_search_rf),
        ("Random Search with SVM", test_random_search_svm),
        ("Result File Generation", test_result_files),
    ]

    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ {test_name} test crashed: {e}")
            results[test_name] = False

    # Summary
    print("\n" + "=" * 70)
    print("HYPERPARAMETER OPTIMIZATION TEST SUMMARY")
    print("=" * 70)

    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False

    if all_passed:
        print("\n🎉 ALL HYPERPARAMETER OPTIMIZATION TESTS PASSED!")
        print("The optimization module is ready for production use.")
    else:
        print("\n⚠️ Some hyperparameter optimization tests failed.")
        print("Please review the implementation.")

    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
