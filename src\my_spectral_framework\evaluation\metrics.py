"""
光谱分析框架的评估指标模块。

本模块为分类和回归任务提供全面的评估指标，改编自 OpenSA 的评估方法。
设计理念：提供统一的指标计算接口，支持多种评估指标，确保评估结果的准确性和可比性。

Author: txy
License: Apache-2.0
"""

from typing import Any, Dict, Optional, Union

import numpy as np
from sklearn.metrics import (
    accuracy_score,
    classification_report,
    confusion_matrix,
    f1_score,
    mean_absolute_error,
    mean_squared_error,
    precision_recall_curve,
    precision_score,
    r2_score,
    recall_score,
    roc_auc_score,
    roc_curve,
)
from sklearn.preprocessing import StandardScaler

from ..core.utils import get_logger

logger = get_logger(__name__)


def calculate_classification_metrics(y_true: np.ndarray, y_pred: np.ndarray, average: str = "weighted") -> Dict[str, float]:
    """
    计算全面的分类指标。

    Args:
        y_true: 真实标签，形状 (n_samples,)
        y_pred: 预测标签，形状 (n_samples,)
        average: 多类指标的平均策略

    Returns:
        包含分类指标的字典
    """
    logger.info("正在计算分类指标")

    metrics = {}

    # 基本指标
    metrics["accuracy"] = accuracy_score(y_true, y_pred)
    metrics["precision"] = precision_score(y_true, y_pred, average=average, zero_division=0)
    metrics["recall"] = recall_score(y_true, y_pred, average=average, zero_division=0)
    metrics["f1_score"] = f1_score(y_true, y_pred, average=average, zero_division=0)

    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    metrics["confusion_matrix"] = cm.tolist()

    # 对于二分类，添加额外指标
    if len(np.unique(y_true)) == 2:
        try:
            metrics["auc_roc"] = roc_auc_score(y_true, y_pred)
        except ValueError:
            metrics["auc_roc"] = 0.0
            logger.warning("无法计算 AUC-ROC 分数")

    # 类别级指标
    unique_classes = np.unique(y_true)
    if len(unique_classes) > 2:
        precision_per_class = precision_score(y_true, y_pred, average=None, zero_division=0)
        recall_per_class = recall_score(y_true, y_pred, average=None, zero_division=0)
        f1_per_class = f1_score(y_true, y_pred, average=None, zero_division=0)

        for i, class_label in enumerate(unique_classes):
            metrics[f"precision_class_{class_label}"] = precision_per_class[i]
            metrics[f"recall_class_{class_label}"] = recall_per_class[i]
            metrics[f"f1_class_{class_label}"] = f1_per_class[i]

    logger.info(f"分类指标计算完成：准确率={metrics['accuracy']:.6f}")
    return metrics


def calculate_regression_metrics(
    y_true: np.ndarray, y_pred: np.ndarray, scaler: Optional[StandardScaler] = None
) -> Dict[str, float]:
    """
    计算全面的回归指标。

    Args:
        y_true: 真实值，形状 (n_samples,)
        y_pred: 预测值，形状 (n_samples,)
        scaler: 可选的缩放器，用于逆变换值

    Returns:
        包含回归指标的字典
    """
    logger.info("正在计算回归指标")

    # 确保数组是一维的
    y_true = np.asarray(y_true).ravel()
    y_pred = np.asarray(y_pred).ravel()

    # 如果提供了缩放器，应用逆变换
    if scaler is not None:
        logger.info("使用提供的缩放器应用逆变换")
        y_true_orig = scaler.inverse_transform(y_true.reshape(-1, 1)).ravel()
        y_pred_orig = scaler.inverse_transform(y_pred.reshape(-1, 1)).ravel()
    else:
        y_true_orig = y_true
        y_pred_orig = y_pred

    metrics = {}

    # 基本回归指标
    mse = mean_squared_error(y_true_orig, y_pred_orig)
    metrics["mse"] = mse
    metrics["rmse"] = np.sqrt(mse)
    metrics["mae"] = mean_absolute_error(y_true_orig, y_pred_orig)
    metrics["r2"] = r2_score(y_true_orig, y_pred_orig)

    # 附加指标
    # 平均绝对百分比误差 (MAPE)
    non_zero_mask = y_true_orig != 0
    if np.any(non_zero_mask):
        mape = np.mean(np.abs((y_true_orig[non_zero_mask] - y_pred_orig[non_zero_mask]) / y_true_orig[non_zero_mask])) * 100
        metrics["mape"] = mape
    else:
        metrics["mape"] = np.inf
        logger.warning("无法计算 MAPE：所有真实值都为零")

    # 相对均方根误差 (RRMSE)
    if np.mean(y_true_orig) != 0:
        metrics["rrmse"] = (metrics["rmse"] / np.mean(y_true_orig)) * 100
    else:
        metrics["rrmse"] = np.inf
        logger.warning("无法计算 RRMSE：真实值的均值为零")

    # 残差统计
    residuals = y_true_orig - y_pred_orig
    metrics["residual_mean"] = np.mean(residuals)
    metrics["residual_std"] = np.std(residuals)
    metrics["residual_min"] = np.min(residuals)
    metrics["residual_max"] = np.max(residuals)

    # 预测统计
    metrics["pred_mean"] = np.mean(y_pred_orig)
    metrics["pred_std"] = np.std(y_pred_orig)
    metrics["true_mean"] = np.mean(y_true_orig)
    metrics["true_std"] = np.std(y_true_orig)

    logger.info(f"回归指标计算完成：RMSE={metrics['rmse']:.6f}, R²={metrics['r2']:.6f}")
    return metrics


def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray, task_type: str, **kwargs) -> Dict[str, float]:
    """
    根据任务类型计算指标。

    Args:
        y_true: 真实值/标签
        y_pred: 预测值/标签
        task_type: 任务类型（"classification" 或 "regression"）
        **kwargs: 特定指标函数的附加参数

    Returns:
        包含计算指标的字典
    """
    task_type = task_type.lower()

    if task_type == "classification":
        return calculate_classification_metrics(y_true, y_pred, **kwargs)
    elif task_type == "regression":
        return calculate_regression_metrics(y_true, y_pred, **kwargs)
    else:
        raise ValueError(f"不支持的任务类型：{task_type}。支持的类型：['classification', 'regression']")


def format_metrics_report(metrics: Dict[str, Any], task_type: str) -> str:
    """
    将指标格式化为可读的报告。

    Args:
        metrics: 计算得出的指标字典
        task_type: 任务类型（"classification" 或 "regression"）

    Returns:
        格式化的字符串报告
    """
    task_type = task_type.lower()

    report_lines = []
    report_lines.append("=" * 50)
    report_lines.append(f"{task_type.upper()} METRICS REPORT")
    report_lines.append("=" * 50)

    if task_type == "classification":
        report_lines.append(f"Accuracy:     {metrics.get('accuracy', 0):.6f}")
        report_lines.append(f"Precision:    {metrics.get('precision', 0):.6f}")
        report_lines.append(f"Recall:       {metrics.get('recall', 0):.6f}")
        report_lines.append(f"F1-Score:     {metrics.get('f1_score', 0):.6f}")

        if "auc_roc" in metrics:
            report_lines.append(f"AUC-ROC:      {metrics['auc_roc']:.6f}")

        # Add class-wise metrics if available
        class_metrics = {k: v for k, v in metrics.items() if k.startswith(("precision_class_", "recall_class_", "f1_class_"))}
        if class_metrics:
            report_lines.append("\nClass-wise Metrics:")
            classes = set()
            for key in class_metrics.keys():
                if "class_" in key:
                    class_label = key.split("_")[-1]
                    classes.add(class_label)

            for class_label in sorted(classes):
                precision = metrics.get(f"precision_class_{class_label}", 0)
                recall = metrics.get(f"recall_class_{class_label}", 0)
                f1 = metrics.get(f"f1_class_{class_label}", 0)
                report_lines.append(f"  Class {class_label}: P={precision:.4f}, R={recall:.4f}, F1={f1:.4f}")

    elif task_type == "regression":
        report_lines.append(f"RMSE:         {metrics.get('rmse', 0):.6f}")
        report_lines.append(f"MAE:          {metrics.get('mae', 0):.6f}")
        report_lines.append(f"R²:           {metrics.get('r2', 0):.6f}")
        report_lines.append(f"MSE:          {metrics.get('mse', 0):.6f}")

        if "mape" in metrics and not np.isinf(metrics["mape"]):
            report_lines.append(f"MAPE:         {metrics['mape']:.2f}%")

        if "rrmse" in metrics and not np.isinf(metrics["rrmse"]):
            report_lines.append(f"RRMSE:        {metrics['rrmse']:.2f}%")

        report_lines.append("\nPrediction Statistics:")
        report_lines.append(f"  Pred Mean:  {metrics.get('pred_mean', 0):.6f}")
        report_lines.append(f"  Pred Std:   {metrics.get('pred_std', 0):.6f}")
        report_lines.append(f"  True Mean:  {metrics.get('true_mean', 0):.6f}")
        report_lines.append(f"  True Std:   {metrics.get('true_std', 0):.6f}")

        report_lines.append("\nResidual Statistics:")
        report_lines.append(f"  Mean:       {metrics.get('residual_mean', 0):.6f}")
        report_lines.append(f"  Std:        {metrics.get('residual_std', 0):.6f}")
        report_lines.append(f"  Min:        {metrics.get('residual_min', 0):.6f}")
        report_lines.append(f"  Max:        {metrics.get('residual_max', 0):.6f}")

    report_lines.append("=" * 50)

    return "\n".join(report_lines)


def get_primary_metric(metrics: Dict[str, float], task_type: str) -> float:
    """
    Get the primary metric for a given task type.

    Args:
        metrics: Dictionary of calculated metrics
        task_type: Type of task ("classification" or "regression")

    Returns:
        Primary metric value
    """
    task_type = task_type.lower()

    if task_type == "classification":
        return metrics.get("accuracy", 0.0)
    elif task_type == "regression":
        return metrics.get("r2", 0.0)
    else:
        raise ValueError(f"Unsupported task type: {task_type}")


def compare_metrics(metrics1: Dict[str, float], metrics2: Dict[str, float], task_type: str) -> Dict[str, Any]:
    """
    Compare two sets of metrics.

    Args:
        metrics1: First set of metrics
        metrics2: Second set of metrics
        task_type: Type of task

    Returns:
        Dictionary containing comparison results
    """
    primary_metric1 = get_primary_metric(metrics1, task_type)
    primary_metric2 = get_primary_metric(metrics2, task_type)

    comparison = {
        "primary_metric_1": primary_metric1,
        "primary_metric_2": primary_metric2,
        "improvement": primary_metric2 - primary_metric1,
        "relative_improvement": ((primary_metric2 - primary_metric1) / primary_metric1 * 100) if primary_metric1 != 0 else 0,
        "better_model": 2 if primary_metric2 > primary_metric1 else 1,
    }

    return comparison
