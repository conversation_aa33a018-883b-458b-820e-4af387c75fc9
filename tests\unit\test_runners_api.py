"""
Runner API 单元测试。

测试重构后的 Runner 类的 API 接口，
验证 Runner 类的基本功能和配置处理。


License: Apache-2.0
"""

import numpy as np
import pytest
from sklearn.datasets import make_classification

from my_spectral_framework.core.config_models import (
    DataConfig, ExperimentConfig, ExperimentRootConfig, ExperimentType,
    ModelConfig, PreprocessingConfig, FeatureSelectionConfig,
    EvaluationConfig, PathsConfig, SklearnEngineConfig
)
from my_spectral_framework.runners import SingleRunRunner, CrossValidationRunner

# 导入测试装饰器
from tests.core.test_registry import unit_test


class TestRunnersAPI:
    """测试 Runner API 的单元功能"""
    
    @pytest.fixture
    def sample_config(self):
        """创建示例实验配置"""
        return ExperimentRootConfig(
            experiment_type=ExperimentType.SINGLE_RUN,
            experiment_config=ExperimentConfig(
                name="test_runner_api",
                description="测试 Runner API 集成"
            ),
            data_config=DataConfig(
                source_type="opensa_test",
                test_size=0.3,
                random_seed=42
            ),
            preprocessing_config=PreprocessingConfig(
                steps=[
                    {"method": "SNV", "params": {}},
                    {"method": "SS", "params": {}}
                ]
            ),
            feature_selection_config=FeatureSelectionConfig(
                method="Pca",
                params={"n_components": 10}
            ),
            evaluation_config=EvaluationConfig(),
            paths_config=PathsConfig(),
            engine_config=SklearnEngineConfig(
                engine="sklearn",
                ml_model_config=ModelConfig(
                    type="Classification",
                    name="RF",
                    params={"n_estimators": 10, "random_state": 42}
                )
            )
        )
    
    @pytest.fixture
    def sample_data(self):
        """创建示例数据"""
        X, y = make_classification(
            n_samples=100,
            n_features=50,
            n_classes=3,
            n_informative=30,
            random_state=42
        )
        return X, y
    
    @unit_test(
        description="验证 SingleRunRunner 与 Pydantic 配置对象的创建",
        tags=["runner", "pydantic", "single_run"],
        priority="high"
    )
    def test_single_run_runner_with_pydantic_config(self, sample_config, sample_data, tmp_path):
        """
        测试用例1：使用 Pydantic 配置对象创建和运行 SingleRunRunner
        """
        # 设置临时输出路径
        sample_config.paths_config.output_dir = str(tmp_path)
        
        # 创建模拟引擎
        from unittest.mock import Mock
        from my_spectral_framework.core.engine import BaseExecutionEngine
        mock_engine = Mock(spec=BaseExecutionEngine)
        mock_engine.build.return_value = Mock()

        # 创建 Runner
        runner = SingleRunRunner(sample_config, mock_engine)
        
        # 验证 Runner 创建成功
        assert runner is not None
        assert isinstance(runner, SingleRunRunner)
        
        # 由于我们使用 test_data，需要模拟数据加载
        # 这里我们跳过实际运行，只测试 Runner 的创建和配置
        print("✅ SingleRunRunner 创建成功")
    
    @unit_test(
        description="验证 CrossValidationRunner 与 Pydantic 配置对象的创建",
        tags=["runner", "pydantic", "cross_validation"],
        priority="high"
    )
    def test_cross_validation_runner_with_pydantic_config(self, sample_config, tmp_path):
        """
        测试用例2：使用 Pydantic 配置对象创建 CrossValidationRunner
        """
        # 修改为交叉验证类型
        sample_config.experiment_type = ExperimentType.CROSS_VALIDATION
        sample_config.paths_config.output_dir = str(tmp_path)
        
        # 创建模拟引擎
        from unittest.mock import Mock
        from my_spectral_framework.core.engine import BaseExecutionEngine
        mock_engine = Mock(spec=BaseExecutionEngine)
        mock_engine.build.return_value = Mock()

        # 创建 Runner
        runner = CrossValidationRunner(sample_config, mock_engine)
        
        # 验证 Runner 创建成功
        assert runner is not None
        assert isinstance(runner, CrossValidationRunner)
        
        print("✅ CrossValidationRunner 创建成功")
    
    @unit_test(
        description="验证 ExecutionEngine 的基本功能",
        tags=["engine", "model_factory", "unit"],
        priority="critical"
    )
    def test_execution_engine_integration(self, sample_config):
        """
        测试用例3：直接测试 ExecutionEngine 的集成
        """
        from my_spectral_framework.core.engine import SklearnExecutionEngine
        
        # 创建执行引擎
        engine = SklearnExecutionEngine()
        
        # 构建模型
        model = engine.build(sample_config)
        
        # 验证模型构建成功
        assert model is not None
        assert hasattr(model, 'fit')
        assert hasattr(model, 'predict')
        
        # 验证模型类型 - 现在引擎返回 sklearn Pipeline
        from sklearn.pipeline import Pipeline
        from sklearn.base import BaseEstimator
        assert isinstance(model, Pipeline)
        assert model.steps[-1][0] == "model"
        assert isinstance(model.steps[-1][1], BaseEstimator)
        
        print(f"✅ ExecutionEngine 构建模型成功: {type(model).__name__}")
    
    @unit_test(
        description="验证模型训练和预测的基本流程",
        tags=["training", "prediction", "unit"],
        priority="critical"
    )
    def test_model_training_and_prediction(self, sample_config, sample_data):
        """
        测试用例4：测试完整的模型训练和预测流程
        """
        from my_spectral_framework.core.engine import SklearnExecutionEngine
        
        X, y = sample_data
        
        # 创建执行引擎并构建模型
        engine = SklearnExecutionEngine()
        model = engine.build(sample_config)
        
        # 分割数据
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 进行预测
        predictions = model.predict(X_test)
        probabilities = model.predict_proba(X_test)
        
        # 验证预测结果
        assert predictions.shape == (X_test.shape[0],)
        assert probabilities.shape == (X_test.shape[0], 3)  # 3 classes
        
        # 验证预测准确性
        from sklearn.metrics import accuracy_score
        accuracy = accuracy_score(y_test, predictions)
        assert accuracy > 0.3  # 基本的准确性检查
        
        print(f"✅ 完整训练预测流程测试通过，准确率: {accuracy:.3f}")
    
    def test_different_model_types(self, sample_config, sample_data):
        """
        测试用例5：测试不同模型类型的构建
        """
        from my_spectral_framework.core.engine import SklearnExecutionEngine
        
        X, y = sample_data
        engine = SklearnExecutionEngine()
        
        # 测试不同的模型
        model_configs = [
            {"name": "RF", "params": {"n_estimators": 5, "random_state": 42}},
            {"name": "SVM", "params": {"C": 1.0, "kernel": "rbf", "random_state": 42}},
            {"name": "ANN", "params": {"hidden_layer_sizes": (10,), "max_iter": 100, "random_state": 42}},
        ]
        
        for model_config in model_configs:
            # 更新配置
            sample_config.ml_model_config.name = model_config["name"]
            sample_config.ml_model_config.params = model_config["params"]
            
            # 构建模型
            model = engine.build(sample_config)
            
            # 验证模型构建成功
            assert model is not None
            assert hasattr(model, 'fit')
            assert hasattr(model, 'predict')
            
            print(f"✅ {model_config['name']} 模型构建成功")
    
    def test_preprocessing_pipeline_integration(self, sample_config, sample_data):
        """
        测试用例6：测试不同预处理管道的集成
        """
        from my_spectral_framework.core.engine import SklearnExecutionEngine
        
        X, y = sample_data
        engine = SklearnExecutionEngine()
        
        # 测试不同的预处理配置
        preprocessing_configs = [
            # 无预处理
            {"steps": [{"method": "None", "params": {}}]},
            # 单步预处理
            {"steps": [{"method": "SNV", "params": {}}]},
            # 多步预处理
            {"steps": [
                {"method": "SNV", "params": {}},
                {"method": "SS", "params": {}}
            ]},
        ]
        
        for i, prep_config in enumerate(preprocessing_configs):
            # 更新预处理配置
            sample_config.preprocessing_config.steps = prep_config["steps"]
            
            # 构建模型
            model = engine.build(sample_config)
            
            # 简单训练测试
            X_train, X_test = X[:70], X[70:]
            y_train, y_test = y[:70], y[70:]
            
            model.fit(X_train, y_train)
            predictions = model.predict(X_test)
            
            assert predictions.shape == (X_test.shape[0],)
            
            print(f"✅ 预处理配置 {i+1} 测试通过")


def test_runner_api_basic_functionality():
    """基本功能测试（不依赖 pytest fixtures）"""
    
    print("🧪 开始 Runner API 基本功能测试...")
    
    # 创建基本配置
    config = ExperimentRootConfig(
        experiment_type=ExperimentType.SINGLE_RUN,
        experiment_config=ExperimentConfig(
            name="basic_test",
            description="基本功能测试"
        ),
        ml_model_config=ModelConfig(
            type="Classification",
            name="RF",
            params={"n_estimators": 5, "random_state": 42}
        )
    )
    
    try:
        # 测试 ExecutionEngine
        from my_spectral_framework.core.engine import SklearnExecutionEngine
        
        engine = SklearnExecutionEngine()
        model = engine.build(config)
        
        print(f"✅ ExecutionEngine 测试通过: {type(model).__name__}")
        
        # 创建模拟引擎
        from unittest.mock import Mock
        from my_spectral_framework.core.engine import BaseExecutionEngine
        mock_engine = Mock(spec=BaseExecutionEngine)
        mock_engine.build.return_value = Mock()

        # 测试 Runner 创建
        runner = SingleRunRunner(config, mock_engine)
        print(f"✅ SingleRunRunner 创建成功: {type(runner).__name__}")
        
        print("\n🎉 所有基本功能测试通过！新的编排层架构工作正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    test_runner_api_basic_functionality()
