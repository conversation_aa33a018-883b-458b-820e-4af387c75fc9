"""
OpenSA 测试数据源策略实现。

负责加载 OpenSA 测试数据集。
"""

from typing import Tuple, Optional, Dict, Any, List
from datetime import datetime
import numpy as np
from .base import BaseDataSource
from ..loaders import load_spectral_data  # 复用底层的OpenSA文件读取逻辑


class OpenSADataSource(BaseDataSource):
    """
    OpenSA 测试数据源策略。
    
    设计理念：封装 OpenSA 测试数据的加载逻辑，提供统一的数据源接口。
    """
    
    def load(self) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray], List[str], Dict[str, Any]]:
        """
        加载 OpenSA 测试数据。

        Returns:
            元组 (X, y, groups, ids, metadata)
        """
        self.logger.info("使用 OpenSA 测试数据源加载数据...")

        # 获取数据类型配置
        data_type = self.data_config.get("type", "Cls")

        # 调用底层的 OpenSA 数据加载函数
        X, y = load_spectral_data(data_type)

        # 为 OpenSA 数据生成ID
        spectrum_id_strategy = self.data_config.get("spectrum_id_strategy", "auto_generate")
        num_samples = X.shape[0]
        if spectrum_id_strategy == "auto_generate":
            ids = [f"opensa_{data_type.lower()}_{i+1:04d}" for i in range(num_samples)]
            self.logger.info(f"为 OpenSA 数据自动生成 {len(ids)} 个光谱ID。")
        else:
            # OpenSA 测试数据不支持从列读取ID，强制使用自动生成
            ids = [f"opensa_{data_type.lower()}_{i+1:04d}" for i in range(num_samples)]
            self.logger.warning("OpenSA 测试数据不支持从列读取ID，已自动生成ID。")

        # 创建元数据
        metadata = {
            "source_type": "opensa_test_data",
            "data_type": data_type,
            "n_samples": X.shape[0],
            "n_features": X.shape[1],
            "load_timestamp": datetime.now().isoformat(),
        }

        # OpenSA 测试数据没有分组信息
        groups = None

        self.logger.info(f"OpenSA 数据加载完成：{X.shape[0]} 个样本，{X.shape[1]} 个特征")

        return X, y, groups, ids, metadata
