"""
Analyze hyperparameter optimization results.
"""

"""
Hyperparameter Optimization Results Analysis Script

This script analyzes the results from hyperparameter optimization experiments.
"""

import sys
from pathlib import Path

# 项目路径设置（现在通过包安装自动处理）
project_root = Path(__file__).parent.parent

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from my_spectral_framework.core.utils import get_logger

logger = get_logger(__name__)
import numpy as np


def analyze_hyperopt_results(results_file: str = None):
    """
    Analyze hyperparameter optimization results.

    Args:
        results_file: Path to the detailed results CSV file
    """
    if results_file is None:
        # Try to find the most recent hyperopt results
        outputs_dir = project_root / "outputs"
        hyperopt_dirs = [d for d in outputs_dir.glob("*hyperopt*") if d.is_dir()]
        if not hyperopt_dirs:
            logger.error("No hyperparameter optimization experiment directories found")
            return

        # Sort by modification time and get the most recent
        hyperopt_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        results_file = hyperopt_dirs[0] / "results" / "cv_results_detailed.csv"

    if not Path(results_file).exists():
        logger.error(f"Results file not found: {results_file}")
        return

    # Load and analyze results
    df = pd.read_csv(results_file)

    print("=" * 60)
    print("HYPERPARAMETER OPTIMIZATION RESULTS ANALYSIS")
    print("=" * 60)

    print(f"Total parameter combinations tested: {len(df)}")
    if "rank" in df.columns:
        print(f"Unique ranks: {sorted(df['rank'].unique())}")
        print(f"Best combinations (rank 1): {len(df[df['rank']==1])}")

    print(f"\nScore distribution:")
    score_col = "mean_test_score" if "mean_test_score" in df.columns else "mean_cv_score"
    if score_col in df.columns:
        print(f"  Mean scores range: {df[score_col].min():.4f} - {df[score_col].max():.4f}")

    if "std_test_score" in df.columns:
        print(f"  Standard deviation range: {df['std_test_score'].min():.4f} - {df['std_test_score'].max():.4f}")

    print(f"\nTop 5 parameter combinations:")
    # Check which model parameters are available
    param_cols = [col for col in df.columns if col.startswith("param_")]
    display_cols = ["rank", score_col] + param_cols[:4] if "rank" in df.columns else [score_col] + param_cols[:4]

    if len(df) > 0:
        if "rank" in df.columns:
            top_5 = df.nsmallest(5, "rank")[display_cols]
        else:
            top_5 = df.nlargest(5, score_col)[display_cols]
        print(top_5.to_string(index=False))

    if "rank" in df.columns and len(df) > 5:
        print(f"\nWorst 5 parameter combinations:")
        worst_5 = df.nlargest(5, "rank")[display_cols]
        print(worst_5.to_string(index=False))

    print(f"\nParameter value analysis:")
    for param_col in param_cols:
        param_name = param_col.replace("param_", "").replace("model__", "")
        unique_values = df[param_col].unique()
        if len(unique_values) <= 10:  # Only show if not too many values
            print(f"  {param_name} tested: {sorted(unique_values)}")
        else:
            print(f"  {param_name} tested: {len(unique_values)} different values")

    if "mean_fit_time" in df.columns:
        print(f"\nTiming analysis:")
        print(f"  Mean fit time range: {df['mean_fit_time'].min():.4f} - {df['mean_fit_time'].max():.4f} seconds")
        if "mean_score_time" in df.columns:
            print(f"  Mean score time range: {df['mean_score_time'].min():.4f} - {df['mean_score_time'].max():.4f} seconds")

    # Check for best combination analysis
    if "is_best_combination" in df.columns:
        best_mask = df["is_best_combination"] == True
        if best_mask.any():
            print(f"\nFold-by-fold scores for best combination:")
            best_combo = df[best_mask].iloc[0]
            fold_cols = [col for col in df.columns if col.startswith("fold_") and col.endswith("_score")]
            if fold_cols:
                fold_scores = [best_combo[col] for col in fold_cols]
                print(f"  Fold scores: {[f'{score:.4f}' for score in fold_scores]}")
                print(f"  Mean: {np.mean(fold_scores):.4f}, Std: {np.std(fold_scores):.4f}")

    print("=" * 60)


if __name__ == "__main__":
    analyze_hyperopt_results()
