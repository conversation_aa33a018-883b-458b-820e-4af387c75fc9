"""
My Spectral Framework v3.2 - 模块化光谱数据分析框架。

本框架提供了一套全面的光谱数据分析工具，涵盖数据预处理、特征选择、
机器学习模型构建与评估等环节。通过增强的用户体验特性，包括健壮的
故障处理、可重现性保证以及基于插件的组件注册机制，旨在提升科研与
工程实践中的效率和可靠性。

作者: txy
"""

# 导入核心组件
from .core.config_models import *
from .core.context import ExperimentContext
from .core.utils import get_logger

# 导入运行器
from .runners import SingleRunRunner, CrossValidationRunner, HyperOptRunner

# 导入结果处理器
from .reporting.handlers import FileSystemResultHandler, MLflowResultHandler

# 导入数据加载器
from .data.loaders import load_and_process_data

# 导入模型工厂
from .models.model_factory import get_model_instance

# 确保所有模型组件被导入以触发注册装饰器
from .models.classic_ml import rf, svm, ann, pls  # 导入经典机器学习模型
from .optimization import sklearn_wrappers  # 导入深度学习包装器

# 导入预处理器
from .preprocessing.spectral_processors import get_transformer

# 导入特征选择器
from .feature_selection.spectral_selectors import get_selector

# 导入分析工具
from .analysis import load_artifact, list_artifacts, list_runs

# 设置默认日志级别
import logging
logging.basicConfig(level=logging.INFO)

# 自动发现和加载插件组件
def _discover_and_load_plugins():
    """
    自动发现并加载 components 目录下的插件组件。

    此函数会扫描 `components` 目录下的所有 `.py` 文件，并动态导入它们。
    由于组件文件中的装饰器（如 `@register_preprocessing`）在导入时就会执行，
    这将自动完成插件的注册，实现零配置的扩展能力。

    设计理念：
    - 零配置：用户只需将插件文件放入 `components` 目录即可，无需额外配置。
    - 自动发现：框架启动时自动扫描并加载所有符合条件的插件。
    - 错误隔离：单个插件的加载错误不会中断整个框架的启动，确保系统健壮性。
    - 日志记录：提供详细的插件加载过程日志，便于调试和监控。
    """
    import importlib
    import pkgutil
    from pathlib import Path

    logger = get_logger(__name__)

    try:
        # 获取 components 目录路径
        components_path = Path(__file__).parent / "components"

        if not components_path.exists():
            logger.debug("Components 目录不存在，跳过插件发现")
            return

        # 扫描 components 目录下的所有 .py 文件
        plugin_count = 0
        error_count = 0

        for file_path in components_path.glob("*.py"):
            # 跳过 __init__.py 和以 _ 开头的文件
            if file_path.name.startswith("__") or file_path.name.startswith("_"):
                continue

            try:
                # 构建模块名
                module_name = f"my_spectral_framework.components.{file_path.stem}"

                # 动态导入模块
                importlib.import_module(module_name)

                plugin_count += 1
                logger.debug(f"成功加载插件: {file_path.name}")

            except Exception as e:
                error_count += 1
                logger.warning(f"加载插件 {file_path.name} 失败: {e}")

        if plugin_count > 0:
            logger.info(f"✅ 插件发现完成: 成功加载 {plugin_count} 个插件")
            if error_count > 0:
                logger.warning(f"⚠️  {error_count} 个插件加载失败")
        else:
            logger.debug("未发现任何插件文件")

        # 显示当前注册的组件统计
        from .core.registry import list_registered_functions
        registered = list_registered_functions()

        total_components = (
            len(registered["preprocessing"]) +
            len(registered["feature_selection"]) +
            len(registered["models"])
        )

        if total_components > 0:
            logger.info(f"[STATS] 组件注册统计: "
                       f"预处理器 {len(registered['preprocessing'])} 个, "
                       f"特征选择器 {len(registered['feature_selection'])} 个, "
                       f"模型 {len(registered['models'])} 个")

    except Exception as e:
        logger.error(f"插件发现过程中发生错误: {e}")

# 执行插件发现
_discover_and_load_plugins()

__all__ = [
    # 核心组件
    "ExperimentContext",
    "get_logger",

    # 配置模型
    "ExperimentRootConfig",
    "ExperimentConfig",
    "DataConfig",
    "PreprocessingConfig",
    "FeatureSelectionConfig",
    "ModelConfig",
    "CVConfig",
    "HyperparameterOptimizationConfig",

    # 运行器
    "SingleRunRunner",
    "CrossValidationRunner",

    # 结果处理器
    "FileSystemResultHandler",
    "MLflowResultHandler",

    # 工厂函数
    "load_and_process_data",
    "get_model_instance",
    "get_transformer",
    "get_selector",

    # 分析工具
    "load_artifact",
    "list_artifacts",
    "list_runs",
]
