"""
插件组件目录。

这个目录用于存放用户自定义的组件插件，包括预处理器、特征选择器、模型等。
所有放在这个目录下的 .py 文件都会被自动发现和加载，其中的组件会自动注册到框架中。

使用方法：
1. 在此目录下创建 .py 文件
2. 在文件中定义组件类
3. 使用相应的装饰器注册组件：
   - @register_preprocessing("name") 用于预处理器
   - @register_feature_selection("name") 用于特征选择器
   - @register_model("name", "type") 用于模型

示例：
    # 在 components/my_preprocessor.py 中
    from my_spectral_framework.core.registry import register_preprocessing
    
    @register_preprocessing("my_custom_norm")
    def my_custom_normalizer(data, params=None):
        # 自定义预处理逻辑
        return normalized_data

设计理念：
- 插件化架构：支持动态扩展框架功能
- 自动发现：无需手动注册，放入文件即可使用
- 标准接口：遵循框架的组件接口规范
- 隔离性：插件之间相互独立，不会互相影响


License: Apache-2.0
"""

# 这个文件主要用于文档说明，实际的插件发现逻辑在框架的 __init__.py 中实现
