"""
数据源工厂模块。

实现工厂模式，根据配置动态创建相应的数据源策略实例。
"""

from typing import Dict, Any
from ..core.utils import get_project_root
from .sources.base import BaseDataSource
from .sources.file_source import FileDataSource
from .sources.opensa_source import OpenSADataSource


def get_data_source(config: Dict[str, Any]) -> BaseDataSource:
    """
    数据源工厂函数。
    
    根据配置中的数据源类型，创建并返回相应的数据源策略实例。
    
    设计理念：采用工厂模式，将数据源的创建逻辑集中管理，
    支持动态选择和实例化不同的数据源策略。
    
    Args:
        config: 完整的实验配置字典
        
    Returns:
        相应的数据源策略实例
        
    Raises:
        ValueError: 当数据源类型不支持或配置不完整时
    """
    data_cfg = config.get("data_config", {})
    source_type = data_cfg.get("source_type")
    
    if source_type in ["advanced_file", "simple_file", "file"]:
        # 文件类型的数据源需要构建完整的文件路径
        paths_cfg = config.get("paths_config", {})
        file_name = data_cfg.get("file_path")
        
        if not file_name:
            raise ValueError(f"对于数据源类型 '{source_type}'，必须在 data_config 中指定 'file_path'。")
        
        # 构建完整的数据文件路径
        data_root_dir = paths_cfg.get("data_dir", "data")
        full_data_path = get_project_root() / data_root_dir / file_name
        
        return FileDataSource(config, str(full_data_path))
    
    elif source_type == "opensa_test":
        # OpenSA 测试数据源不需要文件路径
        return OpenSADataSource(config)
    
    else:
        raise ValueError(f"不支持的数据源类型：{source_type}")
