"""
Partial Least Squares models for spectral analysis.

This module provides PLS implementations for both classification (PLS-DA) and
regression tasks, adapted from OpenSA's ClassicCls.py and ClassicRgs.py.

Author: txy
License: Apache-2.0
"""

from typing import Any, Dict, Optional

import numpy as np
from numpy.typing import NDArray
import pandas as pd
from sklearn.cross_decomposition import PLSRegression
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler

from ...core.utils import get_logger
from ...core.registry import register_model, register_model_params

logger = get_logger(__name__)








# 旧的训练和预测函数已被 SpectralModelWrapper 和 PLSDAAdapter 取代
# 这些函数的功能现在由统一的包装器提供，消除了代码重复


@register_model_params(name="PLS_DA", model_type="classification")
def get_pls_da_params() -> Dict[str, Any]:
    """
    Get default parameters for PLS-DA classifier.

    Returns:
        Dictionary of default parameters
    """
    return {"n_components": 228, "scale": True, "max_iter": 500, "tol": 1e-06, "copy": True, "use_scaler": False}


@register_model_params(name="PLS", model_type="regression")
def get_pls_regressor_params() -> Dict[str, Any]:
    """
    Get default parameters for PLS regressor.

    Returns:
        Dictionary of default parameters
    """
    return {"n_components": 8, "scale": True, "max_iter": 500, "tol": 1e-06, "copy": True, "use_scaler": False}


def optimize_pls_components(X_train: np.ndarray, y_train: np.ndarray, max_components: int = 20, cv_folds: int = 5) -> int:
    """
    Optimize number of PLS components using cross-validation.

    Args:
        X_train: Training features
        y_train: Training targets
        max_components: Maximum number of components to test
        cv_folds: Number of cross-validation folds

    Returns:
        Optimal number of components
    """
    from sklearn.metrics import make_scorer, r2_score
    from sklearn.model_selection import cross_val_score

    scores = []
    components_range = range(1, min(max_components + 1, X_train.shape[1], X_train.shape[0]))

    for n_comp in components_range:
        model = PLSRegression(n_components=n_comp)
        cv_scores = cross_val_score(model, X_train, y_train, cv=cv_folds, scoring=make_scorer(r2_score))
        scores.append(np.mean(cv_scores))

    optimal_components = components_range[np.argmax(scores)]
    logger.info(f"Optimal number of PLS components: {optimal_components}")

    return optimal_components


def get_pls_loadings(model) -> np.ndarray:
    """
    Get PLS loadings from trained model.

    Args:
        model: Trained PLS model

    Returns:
        PLS loadings array
    """
    if hasattr(model, "x_loadings_"):
        return model.x_loadings_
    else:
        raise ValueError("Model does not have x_loadings_ attribute")


def get_pls_scores(model, X: np.ndarray) -> np.ndarray:
    """
    Get PLS scores for given data.

    Args:
        model: Trained PLS model
        X: Input features

    Returns:
        PLS scores array
    """
    # Apply same preprocessing as training
    X_processed = X.copy()

    if hasattr(model, "scaler_") and model.scaler_ is not None:
        X_processed = model.scaler_.transform(X)

    scores = model.transform(X_processed)
    return scores


# ===== 注册表驱动的工厂函数 =====
# 以下函数使用 @register_model 装饰器注册到框架的模型注册表中
# 这些工厂函数负责创建具体的模型实例，符合"完全由注册表驱动"的设计原则


from ._base import PLSDAAdapter


@register_model(name="PLS_DA", model_type="classification")
def create_pls_da_classifier(**params) -> PLSDAAdapter:
    """
    创建 PLS-DA 分类器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 PLSDAAdapter 实例。
    PLS-DA 是将 PLSRegression 用于分类任务的特殊适配器。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 PLSRegression 的参数（会自动过滤掉 use_scaler）

    Returns:
        PLSDAAdapter: 配置好的 PLS-DA 分类器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("PLS_DA", "classification")
        >>> classifier = factory_func(n_components=5)
    """
    # 移除 use_scaler，因为它不属于 PLSRegression 的参数
    pls_params = {k: v for k, v in params.items() if k != 'use_scaler'}
    logger.info(f"创建 PLS-DA 分类器，参数: {pls_params}")

    # 创建 PLSRegression 并用 PLSDAAdapter 包装
    pls_model = PLSRegression(**pls_params)
    return PLSDAAdapter(pls_model)


@register_model(name="PLS", model_type="regression")
def create_pls_regressor(**params) -> PLSRegression:
    """
    创建 PLS 回归器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 PLSRegression 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 PLSRegression 的参数（会自动过滤掉 use_scaler）

    Returns:
        PLSRegression: 配置好的偏最小二乘回归器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("PLS", "regression")
        >>> regressor = factory_func(n_components=8)
    """
    # 移除 use_scaler，因为它不属于 PLSRegression 的参数
    pls_params = {k: v for k, v in params.items() if k != 'use_scaler'}
    logger.info(f"创建 PLS 回归器，参数: {pls_params}")
    return PLSRegression(**pls_params)
