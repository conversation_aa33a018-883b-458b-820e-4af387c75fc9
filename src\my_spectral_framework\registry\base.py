"""
产物注册表抽象基类模块。

本模块定义了产物注册表的抽象接口，用于管理实验产物的元数据信息。
注册表记录产物的位置、格式、创建时间等信息，支持快速查询和检索。

设计理念：
1. 策略模式：不同的注册表后端实现相同的接口
2. 元数据管理：统一管理产物的元数据信息
3. 查询优化：支持高效的产物查询和检索
4. 可扩展性：支持不同类型的存储后端（SQLite、PostgreSQL、MongoDB等）


License: Apache-2.0
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from ..core.utils import get_logger

logger = get_logger(__name__)


class BaseArtifactRegistry(ABC):
    """
    产物注册表的抽象基类。
    
    定义了所有产物注册表必须实现的核心接口。注册表的职责是管理
    实验产物的元数据信息，包括存储位置、格式、创建时间等。
    
    设计理念：
    - 抽象化：定义统一的注册表接口，隐藏具体实现细节
    - 可扩展性：支持不同类型的注册表后端
    - 查询优化：提供高效的查询和检索功能
    - 元数据完整性：确保产物元数据的完整性和一致性
    """
    
    def __init__(self, connection_string: str):
        """
        初始化产物注册表。
        
        Args:
            connection_string: 连接字符串或配置信息
        """
        self.connection_string = connection_string
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
    @abstractmethod
    def register_artifact(
        self,
        run_id: str,
        artifact_name: str,
        uri: str,
        format: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        注册一个产物到注册表。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            uri: 产物的存储位置 URI
            format: 产物格式
            metadata: 可选的元数据信息
            
        Returns:
            bool: 注册是否成功
            
        Raises:
            RegistryError: 当注册操作失败时
        """
        pass
        
    @abstractmethod
    def get_artifact_uri(self, run_id: str, artifact_name: str) -> Optional[str]:
        """
        获取指定产物的存储 URI。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            Optional[str]: 产物的 URI，如果不存在则返回 None
            
        Raises:
            RegistryError: 当查询操作失败时
        """
        pass
        
    @abstractmethod
    def get_artifact_metadata(self, run_id: str, artifact_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定产物的完整元数据。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            Optional[Dict[str, Any]]: 产物的元数据，如果不存在则返回 None
        """
        pass
        
    @abstractmethod
    def list_artifacts(self, run_id: str) -> List[Dict[str, Any]]:
        """
        列出指定实验运行的所有产物。
        
        Args:
            run_id: 实验运行 ID
            
        Returns:
            List[Dict[str, Any]]: 产物信息列表
        """
        pass
        
    @abstractmethod
    def list_runs(self) -> List[str]:
        """
        列出所有实验运行 ID。
        
        Returns:
            List[str]: 实验运行 ID 列表
        """
        pass
        
    @abstractmethod
    def delete_artifact(self, run_id: str, artifact_name: str) -> bool:
        """
        从注册表中删除指定产物。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            bool: 删除是否成功
        """
        pass
        
    @abstractmethod
    def delete_run(self, run_id: str) -> bool:
        """
        从注册表中删除指定实验运行的所有产物。
        
        Args:
            run_id: 实验运行 ID
            
        Returns:
            bool: 删除是否成功
        """
        pass
        
    @abstractmethod
    def search_artifacts(
        self,
        name_pattern: Optional[str] = None,
        format: Optional[str] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索符合条件的产物。
        
        Args:
            name_pattern: 产物名称模式（支持通配符）
            format: 产物格式
            created_after: 创建时间下限
            created_before: 创建时间上限
            
        Returns:
            List[Dict[str, Any]]: 符合条件的产物信息列表
        """
        pass
        
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取注册表统计信息。
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        pass
        
    def validate_artifact_data(
        self,
        run_id: str,
        artifact_name: str,
        uri: str,
        format: str
    ) -> bool:
        """
        验证产物数据的有效性。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            uri: 产物 URI
            format: 产物格式
            
        Returns:
            bool: 数据是否有效
        """
        # 基本验证逻辑
        if not run_id or not artifact_name or not uri or not format:
            return False
            
        # 检查名称是否包含非法字符
        invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        if any(char in artifact_name for char in invalid_chars):
            return False
            
        return True


class RegistryError(Exception):
    """注册表操作相关的异常类。"""
    pass


class ArtifactNotFoundError(RegistryError):
    """产物不存在异常。"""
    pass


class DuplicateArtifactError(RegistryError):
    """重复产物异常。"""
    pass
