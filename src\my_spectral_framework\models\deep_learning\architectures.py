"""
深度学习模型架构定义模块。

本模块包含了所有深度学习模型的 PyTorch nn.Module 架构定义，
为分类和回归任务提供统一的模型架构。这种设计支持：
- 架构与训练逻辑的解耦
- 未来的神经架构搜索（NAS）扩展
- 复杂训练机制的集成

Author: txy
License: Apache-2.0
"""

import logging
import warnings
from typing import Optional

import torch
import torch.nn as nn
import torch.nn.functional as F

from ...core.utils import get_logger

logger = get_logger(__name__)


class CNN1DNet(nn.Module):
    """
    统一的1D CNN网络架构，支持分类和回归任务。
    
    该架构采用三层卷积网络设计，每层包含：
    - 1D卷积层
    - 批归一化
    - ReLU激活
    - 最大池化
    
    最后通过全连接层输出预测结果。根据任务类型自动调整输出层：
    - 分类任务：输出 n_classes 个类别的 logits
    - 回归任务：输出连续值
    """

    def __init__(
        self,
        input_size: int,
        task_type: str = "classification",
        n_outputs: int = 2,
        n_filters_1: int = 32,
        n_filters_2: int = 64,
        n_filters_3: int = 128,
        kernel_size_1: int = 5,
        kernel_size_2: int = 3,
        kernel_size_3: int = 3,
        dropout_rate: float = 0.5,
        **kwargs
    ):
        """
        初始化CNN1D网络。
        
        Args:
            input_size: 输入特征维度
            task_type: 任务类型，"classification" 或 "regression"
            n_outputs: 输出维度（分类任务为类别数，回归任务通常为1）
            n_filters_1: 第一层卷积核数量
            n_filters_2: 第二层卷积核数量
            n_filters_3: 第三层卷积核数量
            kernel_size_1: 第一层卷积核大小
            kernel_size_2: 第二层卷积核大小
            kernel_size_3: 第三层卷积核大小
            dropout_rate: Dropout比率
            **kwargs: 其他参数（为未来扩展预留）
        """
        super(CNN1DNet, self).__init__()
        
        # 验证参数
        if task_type not in ["classification", "regression"]:
            raise ValueError(f"task_type must be 'classification' or 'regression', got {task_type}")
        
        if n_outputs < 1:
            raise ValueError(f"n_outputs must be >= 1, got {n_outputs}")
            
        self.task_type = task_type
        self.n_outputs = n_outputs
        self.input_size = input_size
        
        # 卷积层定义
        self.conv1 = nn.Sequential(
            nn.Conv1d(1, n_filters_1, kernel_size_1, 1),
            nn.BatchNorm1d(n_filters_1),
            nn.ReLU(),
            nn.MaxPool1d(3, 3)
        )

        self.conv2 = nn.Sequential(
            nn.Conv1d(n_filters_1, n_filters_2, kernel_size_2, 1),
            nn.BatchNorm1d(n_filters_2),
            nn.ReLU(),
            nn.MaxPool1d(3, 3)
        )

        self.conv3 = nn.Sequential(
            nn.Conv1d(n_filters_2, n_filters_3, kernel_size_3, 1),
            nn.BatchNorm1d(n_filters_3),
            nn.ReLU(),
            nn.MaxPool1d(3, 3)
        )

        # 计算全连接层输入尺寸
        self._calculate_fc_size(input_size)

        # 全连接层定义
        self.fc = nn.Sequential(
            nn.Linear(self.fc_input_size, 256),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(256, n_outputs)
        )
        
        logger.debug(f"CNN1DNet initialized: task_type={task_type}, n_outputs={n_outputs}, "
                    f"input_size={input_size}, fc_input_size={self.fc_input_size}")

    def _calculate_fc_size(self, input_size: int) -> None:
        """
        动态计算全连接层的输入尺寸。
        
        通过前向传播一个虚拟张量来计算卷积层输出的展平尺寸。
        如果计算失败，使用启发式默认值并发出警告。
        
        Args:
            input_size: 输入特征维度
        """
        try:
            # 创建虚拟输入张量 (batch_size=1, channels=1, features=input_size)
            x = torch.randn(1, 1, input_size)
            
            # 通过卷积层前向传播
            x = self.conv1(x)
            x = self.conv2(x)
            x = self.conv3(x)
            
            # 计算展平后的尺寸
            self.fc_input_size = x.view(1, -1).size(1)
            
            logger.debug(f"FC input size calculated: {self.fc_input_size}")
            
        except RuntimeError as e:
            # 如果动态计算失败，使用启发式默认值
            # 这通常发生在输入尺寸太小，导致卷积和池化后尺寸为0的情况
            heuristic_size = max(128, input_size // 32)  # 启发式计算
            self.fc_input_size = heuristic_size
            
            warnings.warn(
                f"Failed to calculate FC input size dynamically (error: {e}). "
                f"Using heuristic default: {heuristic_size}. "
                f"This may indicate that the input size ({input_size}) is too small for the current "
                f"architecture, or more sophisticated shape inference is needed.",
                UserWarning
            )
            
            logger.warning(f"FC size calculation failed, using heuristic: {heuristic_size}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播。
        
        Args:
            x: 输入张量，形状为 (batch_size, 1, input_size)
            
        Returns:
            输出张量，形状为 (batch_size, n_outputs)
        """
        # 卷积特征提取
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        
        # 展平并通过全连接层
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        
        return x

    def get_architecture_info(self) -> dict:
        """
        获取架构信息，用于调试和日志记录。
        
        Returns:
            包含架构信息的字典
        """
        return {
            "model_type": "CNN1DNet",
            "task_type": self.task_type,
            "n_outputs": self.n_outputs,
            "input_size": self.input_size,
            "fc_input_size": self.fc_input_size,
            "total_parameters": sum(p.numel() for p in self.parameters()),
            "trainable_parameters": sum(p.numel() for p in self.parameters() if p.requires_grad)
        }
