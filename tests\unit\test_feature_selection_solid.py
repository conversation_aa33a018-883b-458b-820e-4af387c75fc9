"""
SOLID原则驱动的特征选择测试 - 函数级精度验证
"""
import numpy as np
import pytest
from sklearn.decomposition import PCA

# 导入被测试的函数和类
from my_spectral_framework.feature_selection.spectral_selectors import (
    LarsSelector,
    CarsSelector,
    apply_pca,
    get_selector,
)

# 导入新的装饰器注册系统
from tests.core.test_registry import unit_test

class TestSOLIDFeatureSelectionPrinciples:
    """
    SOLID原则驱动的特征选择测试 - 函数级精度验证
    """

    @unit_test(
        description="[SRP] 验证PCA的状态仅由训练集决定，防止数据泄露",
        tags=["feature_selection", "data_leakage", "critical", "pca"],
        priority="critical"
    )
    def test_apply_pca_prevents_data_leakage(self, mock_spectral_data):
        """职责：确保PCA变换不会从测试集泄露信息到训练过程。"""
        X, _, _ = mock_spectral_data
        X_train, X_test = X[:80], X[80:]

        _, pca_clean = apply_pca(X_train, n_components=5)
        _, pca_leaked = apply_pca(np.vstack((X_train, X_test)), n_components=5)

        assert not np.allclose(pca_clean.components_, pca_leaked.components_), \
            "PCA状态不应该受到测试集影响"

    @unit_test(
        description="[SRP] 验证 LarsSelector 精确控制选定的特征数量",
        tags=["feature_selection", "dimension", "lars"],
        priority="high"
    )
    def test_LarsSelector_adheres_to_nums_parameter(self, mock_spectral_data):
        """职责：验证LarsSelector输出的特征数量严格等于'nums'参数。"""
        X, y, _ = mock_spectral_data
        for n_features in [5, 10, 20]:
            selector = LarsSelector(nums=n_features)
            selector.fit(X, y)
            transformed = selector.transform(X)
            assert transformed.shape[1] == n_features, \
                f"期望{n_features}个特征，得到{transformed.shape[1]}个"

    @unit_test(
        description="[SRP] 验证所有Selector包装器在未fit时调用transform会抛出错误",
        tags=["feature_selection", "sklearn_compat", "error_handling"],
        priority="normal"
    )
    def test_selector_wrappers_require_fit_before_transform(self):
        """职责：验证所有包装器都遵循scikit-learn的fit-then-transform模式。"""
        X = np.random.rand(10, 20)
        # CarsSelector可能需要y，但此处测试的是调用transform前的状态
        selectors = [LarsSelector(nums=5), CarsSelector(nums=5)]
        for selector in selectors:
            with pytest.raises(ValueError, match="must be fitted"):
                selector.transform(X)

    @unit_test(
        description="[SRP] 验证 get_selector 工厂函数能根据方法名正确分发",
        tags=["feature_selection", "factory"],
        priority="high"
    )
    def test_get_selector_dispatches_correctly(self, mock_spectral_data):
        """职责：测试工厂函数分发机制的正确性。"""
        X, y, _ = mock_spectral_data

        # 测试PCA分发
        selector_pca = get_selector("Pca", {"n_components": 5})
        assert isinstance(selector_pca, PCA) or hasattr(selector_pca, 'pca_')

        # 测试LARS分发
        selector_lars = get_selector("Lars", {"nums": 3})
        assert isinstance(selector_lars, LarsSelector)

    @unit_test(
        description="[SRP] 验证 get_selector 对未知方法抛出清晰的 ValueError",
        tags=["feature_selection", "factory", "error_handling"],
        priority="normal"
    )
    def test_get_selector_raises_for_invalid_method(self):
        """职责：验证工厂函数的错误处理机制。"""
        with pytest.raises(ValueError, match="Unknown feature selection method"):
            get_selector("INVALID_METHOD")

    @unit_test(
        description="[SRP] 验证所有特征选择方法都保持样本数量不变",
        tags=["feature_selection", "dimension"],
        priority="critical"
    )
    def test_feature_selection_maintains_sample_count(self, mock_spectral_data):
        """职责：验证所有特征选择方法在降维的同时不改变样本数。"""
        X, y, _ = mock_spectral_data
        original_n_samples = X.shape[0]
        methods_and_params = [("Pca", {"n_components": 5}), ("Lars", {"nums": 3})]
        for method, params in methods_and_params:
            try:
                selector = get_selector(method, params)
                selector.fit(X, y)
                result = selector.transform(X)
                assert result.shape[0] == original_n_samples, f"{method}不应该改变样本数量"
            except Exception as e:
                pytest.skip(f"{method}方法在当前数据上失败: {e}")
