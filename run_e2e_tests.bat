@echo off
REM Comprehensive End-to-End Configuration Test Suite Runner (Windows)
REM This script runs all E2E tests to validate configuration stability

echo ==================================================================
echo   Comprehensive E2E Configuration Test Suite
echo ==================================================================
echo   Testing all core configurations for stability and reliability
echo   This includes: Classification, CV, Hyperopt, PyTorch, and more
echo ==================================================================

echo Starting test execution...
python -m unittest tests.e2e.test_e2e_config_runner -v

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ==================================================================
    echo   ALL E2E TESTS PASSED SUCCESSFULLY!
    echo   Your configuration system is stable and reliable.
    echo ==================================================================
) else (
    echo.
    echo ==================================================================
    echo   E2E TESTS FAILED!
    echo   Please check the output above for detailed error information.
    echo ==================================================================
    exit /b 1
)
