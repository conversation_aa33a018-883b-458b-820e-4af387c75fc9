# 日志配置版本
version: 1

# 禁用现有的日志记录器
disable_existing_loggers: false

# 格式化器定义
formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  simple:
    format: '%(levelname)s - %(message)s'

# 处理器定义
handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/spectral_framework.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/errors.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

# 日志记录器定义
loggers:
  my_spectral_framework:
    level: DEBUG
    handlers: [console, file, error_file]
    propagate: false
  
  src.my_spectral_framework:
    level: DEBUG
    handlers: [console, file, error_file]
    propagate: false

# 根日志记录器
root:
  level: WARNING
  handlers: [console]
