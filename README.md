# My Spectral Framework v3.2

一个现代化、高度模块化的光谱数据分析框架，专为交互式研究和自动化实验设计。

## 核心特性

- **模块化设计**: 预处理、特征选择、模型等组件完全解耦，可自由组合成分析管道。
- **双核引擎**: 同时支持 **Sklearn** 传统机器学习和 **PyTorch** 深度学习模型，满足不同研究需求。
- **插件化扩展**: 只需将自定义组件放入 `components` 目录，框架即可自动发现并注册，无需修改核心代码。
- **全面的可追溯性**:
    - **环境快照**: 自动记录依赖版本和硬件环境，确保实验可复现。
    - **调试产物**: 可选的 `basic` 或 `full` 级别溯源，用于保存数据划分ID或每一步的中间数据矩阵。
    - **失败归档**: 实验失败时自动保存错误日志、堆栈和配置，便于调试。
- **命令行接口 (CLI)**: 提供强大的 `scripts/run_experiment.py` 脚本，用于批量执行和自动化实验。
- **配置优先**: 所有实验流程由配置文件驱动，代码与配置分离。

## 快速开始

### 安装

建议使用可编辑模式安装，便于开发和调试。

```bash
# 安装核心依赖，并允许直接修改代码
pip install -e .

# 安装所有开发和测试相关的额外依赖
pip install ".[dev]"
```

### 运行一个Scikit-learn实验

以下示例展示了如何在Jupyter Notebook或脚本中，通过API运行一个完整的随机森林分类实验。

```python
# 1. 导入框架核心组件
from my_spectral_framework.runners import SingleRunRunner
from my_spectral_framework.core.config_models import *

# 2. 定义实验配置
config = ExperimentRootConfig(
    experiment_type=ExperimentType.SINGLE_RUN,
    data_config=DataConfig(
        source_type="opensa_test",  # 使用内置测试数据
        test_size=0.3,
        random_seed=42
    ),
    preprocessing_config=PreprocessingConfig(
        steps=[{"method": "SNV", "params": {}}] # 标准正态变量变换
    ),
    feature_selection_config=FeatureSelectionConfig(
        method="Pca",
        params={"n_components": 20}
    ),
    engine_config=SklearnEngineConfig(
        engine="sklearn",
        ml_model_config=ModelConfig(
            type="Classification",
            name="RF",  # 随机森林
            params={"n_estimators": 100, "random_state": 42}
        )
    ),
    experiment_config=ExperimentConfig(name="sklearn_rf_notebook_example"),
    evaluation_config=EvaluationConfig(),
    paths_config=Paths_Config()
)

# 3. 创建并运行实验
# 框架会根据配置中的 "engine": "sklearn" 自动选择合适的执行引擎
engine = create_engine(config.engine_config.engine)
runner = SingleRunRunner(config, engine)
results = runner.run()

# 4. 查看结果
print(f"实验状态: {results.get('status')}")
if results.get('status') == 'success':
    print(f"模型准确率: {results.get('metrics', {}).get('accuracy', {}).get('value', 'N/A'):.4f}")
    print(f"实验结果已保存至: {results.get('experiment_directory_name')}")

```

### 通过命令行运行

1.  在 `conf/runs/` 目录下创建你的配置文件，例如 `my_experiment.py`。
2.  执行以下命令：

```bash
# 运行实验
python scripts/run_experiment.py --config my_experiment

# 仅验证配置而不运行
python scripts/run_experiment.py --config my_experiment --validate-only

# 列出所有可用的实验配置
python scripts/run_experiment.py --list-configs
```

## 项目结构

```
my_spectral_framework_v3.2/
├── src/my_spectral_framework/    # 核心框架源码
│   ├── core/                     # 核心架构 (引擎, 上下文, 配置模型)
│   ├── data/                     # 数据加载与分割
│   ├── preprocessing/            # 预处理组件
│   ├── feature_selection/        # 特征选择组件
│   ├── models/                   # 模型组件 (包含classic_ml和deep_learning)
│   ├── reporting/                # 结果处理与产物保存
│   ├── runners.py                # 实验运行器 (策略模式)
│   └── components/               # 用户自定义插件目录
├── tests/                        # 自动化测试套件
│   ├── unit/                     # 单元测试
│   ├── integration/              # 集成测试
│   └── e2e/                      # 端到端测试
├── scripts/                      # 命令行工具
│   ├── run_experiment.py         # CLI 实验运行器
│   └── run_solid_tests.py        # 智能测试运行器
├── notebooks/                    # Jupyer Notebook 示例
├── conf/                         # 实验配置文件
│   ├── base/                     # 基础配置与预设
│   └── runs/                     # 具体的实验运行配置
├── data/                         # 数据目录 (gitignore已配置忽略data/real/)
└── outputs/                      # 实验输出 (gitignore已配置忽略此目录内容)
```

## 贡献

我们欢迎任何形式的贡献！请参考 `CONTRIBUTING.md` 获取详细指南。

## 许可证

本项遵循 [Apache-2.0](https://www.apache.org/licenses/LICENSE-2.0) 许可证。
