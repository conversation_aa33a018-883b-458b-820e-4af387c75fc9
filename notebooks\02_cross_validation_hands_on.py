# notebooks/02_cross_validation_hands_on.py

# %% [markdown]
# # 2. 动手实践：交叉验证的正确实现
# 
# **这是一个完全拆解式的教程！** 我们将：
# - 🔧 **直接调用框架的底层组件**（不使用高级封装）
# - 📊 **每一步都展示数据的变化**（shape、head()等）
# - 🛡️ **手动构建sklearn Pipeline防止数据泄漏**
# - 📈 **随时可以抽离数据进行深度分析**
# 
# **学习目标：**
# - 理解交叉验证中数据泄漏的危险性
# - 掌握sklearn Pipeline的正确使用方法
# - 学会手动调用框架的各个组件
# - 能够在任何步骤抽离数据进行自定义分析
# 
# **核心原则：透明化每一个步骤，拒绝黑盒操作！**

# %%
# 导入必要的模块 - 我们将直接调用底层组件！
import sys
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.model_selection import StratifiedKFold, cross_validate
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

# 智能路径检测和设置
def setup_project_paths():
    """智能检测项目根目录并设置Python路径"""
    current_dir = Path('.').resolve()
    
    # 尝试找到项目根目录（包含conf和src目录的目录）
    project_root = None
    search_dir = current_dir
    
    # 向上搜索最多3级目录
    for _ in range(3):
        if (search_dir / 'conf').exists() and (search_dir / 'src').exists():
            project_root = search_dir
            break
        search_dir = search_dir.parent
    
    if project_root is None:
        # 如果没找到，假设当前目录就是项目根目录
        project_root = current_dir
    
    # 添加必要的路径
    paths_to_add = [
        str(project_root),           # 项目根目录
        str(project_root / 'src'),   # src目录
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    return project_root

# 设置路径
project_root = setup_project_paths()

try:
    # 导入框架的底层组件 - 这些是我们要直接调用的！
    from src.my_spectral_framework.data.sources.opensa_source import OpenSADataSource
    from src.my_spectral_framework.preprocessing.spectral_processors import get_transformer
    from src.my_spectral_framework.feature_selection.spectral_selectors import get_selector
    from src.my_spectral_framework.models.model_factory import get_model_instance
    from src.my_spectral_framework.evaluation.metrics import calculate_metrics
    from notebooks.utils import plot_cv_results
    print("✅ 底层组件导入成功！我们将直接调用这些组件。")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("💡 请确保您在项目根目录中运行此脚本")
    raise

# %% [markdown]
# ## 步骤 1: 直接加载数据 - 拒绝黑盒！
# 
# 我们不使用高级封装，而是直接调用数据源来加载数据。
# 这样我们可以清楚地看到数据的每一个细节。

# %%
# 直接创建数据源并加载数据
print("🔍 步骤 1: 直接加载数据")
print("=" * 60)

# 创建一个简单的配置来加载 OpenSA 测试数据
data_config = {
    "data_config": {
        "source_type": "opensa_test",
        "type": "Cls"  # 分类任务
    }
}

# 直接实例化数据源
data_source = OpenSADataSource(data_config)

# 加载数据 - 这里我们可以看到数据的原始形态
X_raw, y_raw, groups, metadata = data_source.load()

print(f"📊 原始数据信息:")
print(f"  特征矩阵 X: {X_raw.shape}")
print(f"  标签向量 y: {y_raw.shape}")
print(f"  分组信息: {groups.shape if groups is not None else 'None'}")
print(f"  类别数量: {len(np.unique(y_raw))}")
print(f"  类别分布: {np.bincount(y_raw.astype(int))}")

# 让我们看看数据的前几行
print(f"\n🔍 数据预览:")
print(f"  前3个样本的前5个特征:")
print(X_raw[:3, :5])
print(f"  前10个标签: {y_raw[:10]}")

# %% [markdown]
# ## 步骤 2: 手动创建预处理器 - 透明化每一步！
# 
# 我们将直接调用框架的预处理器工厂函数，创建一个SNV标准化器。
# 注意：我们创建的是sklearn兼容的转换器，可以用于Pipeline。

# %%
print("🔧 步骤 2: 创建预处理器")
print("=" * 60)

# 直接调用工厂函数创建预处理器
preprocessor = get_transformer("SNV", params={})

print(f"✅ 创建的预处理器: {type(preprocessor).__name__}")
print(f"   这是一个sklearn兼容的转换器: {hasattr(preprocessor, 'fit_transform')}")

# 让我们看看预处理前后的数据变化
print(f"\n📊 预处理效果预览:")
print(f"  原始数据前3个样本的统计:")
for i in range(3):
    sample = X_raw[i]
    print(f"    样本 {i}: 均值={sample.mean():.4f}, 标准差={sample.std():.4f}")

# 对前几个样本进行预处理看效果
X_preview = preprocessor.fit_transform(X_raw[:5])
print(f"\n  SNV预处理后前3个样本的统计:")
for i in range(3):
    sample = X_preview[i]
    print(f"    样本 {i}: 均值={sample.mean():.4f}, 标准差={sample.std():.4f}")

print(f"\n💡 观察: SNV将每个样本标准化为均值≈0，标准差≈1")

# %% [markdown]
# ## 步骤 3: 手动创建特征选择器 - 看见降维过程！
# 
# 我们将创建一个PCA特征选择器，并观察它如何降低数据维度。

# %%
print("🎯 步骤 3: 创建特征选择器")
print("=" * 60)

# 直接调用工厂函数创建特征选择器
feature_selector = get_selector("Pca", params={"n_components": 30})

print(f"✅ 创建的特征选择器: {type(feature_selector).__name__}")
print(f"   目标特征数: 30 (从 {X_raw.shape[1]} 降到 30)")

# 让我们看看特征选择的效果
print(f"\n📊 特征选择效果预览:")
print(f"  原始特征数: {X_raw.shape[1]}")

# 先预处理再特征选择（这是正确的顺序）
X_preprocessed = preprocessor.fit_transform(X_raw)
X_selected = feature_selector.fit_transform(X_preprocessed, y_raw)

print(f"  预处理后特征数: {X_preprocessed.shape[1]}")
print(f"  特征选择后特征数: {X_selected.shape[1]}")
print(f"  数据压缩比: {X_selected.shape[1] / X_raw.shape[1]:.2%}")

# 如果是PCA，我们可以看看解释的方差比
if hasattr(feature_selector, 'explained_variance_ratio_'):
    total_variance = feature_selector.explained_variance_ratio_.sum()
    print(f"  保留的方差比例: {total_variance:.2%}")

# %% [markdown]
# ## 步骤 4: 手动创建模型 - 了解模型参数！
# 
# 我们将直接创建一个随机森林模型，并查看它的参数设置。

# %%
print("🤖 步骤 4: 创建模型")
print("=" * 60)

# 直接调用工厂函数创建模型
model_params = {
    "n_estimators": 50,
    "max_depth": 8,
    "random_state": 42
}

model = get_model_instance("Classification", "RF", model_params)

print(f"✅ 创建的模型: {type(model).__name__}")
print(f"   模型参数:")
for param, value in model_params.items():
    print(f"     {param}: {value}")

# 检查模型是否sklearn兼容
print(f"\n🔍 模型接口检查:")
print(f"   有fit方法: {hasattr(model, 'fit')}")
print(f"   有predict方法: {hasattr(model, 'predict')}")
print(f"   有predict_proba方法: {hasattr(model, 'predict_proba')}")

# %% [markdown]
# ## 🚨 关键步骤 5: 构建Pipeline防止数据泄漏！
# 
# **这是最重要的部分！** 我们将把所有组件组装成一个Pipeline。
# 这确保了在交叉验证的每一折中，预处理和特征选择都只在训练数据上进行拟合。

# %%
print("🛡️ 步骤 5: 构建防泄漏Pipeline")
print("=" * 60)

# 创建完整的处理管道
pipeline = Pipeline([
    ('preprocessor', preprocessor),      # 第1步：预处理
    ('feature_selector', feature_selector),  # 第2步：特征选择
    ('model', model)                     # 第3步：模型
])

print(f"✅ Pipeline构建完成!")
print(f"   管道步骤:")
for i, (name, component) in enumerate(pipeline.steps, 1):
    print(f"     {i}. {name}: {type(component).__name__}")

print(f"\n🛡️ 防泄漏原理:")
print(f"   在每一折交叉验证中:")
print(f"   1. 预处理器只在训练集上fit，然后transform训练集和测试集")
print(f"   2. 特征选择器只在训练集上fit，然后transform训练集和测试集")
print(f"   3. 模型只在处理后的训练集上训练")
print(f"   4. 这样确保测试集信息不会泄漏到训练过程中")

# %% [markdown]
# ## 步骤 6: 执行真正的交叉验证 - 看见每一折的结果！
# 
# 现在我们使用sklearn的cross_validate来执行交叉验证，
# 并详细分析每一折的结果。

# %%
print("📊 步骤 6: 执行交叉验证")
print("=" * 60)

# 创建交叉验证分割器
cv_splitter = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

print(f"🔄 交叉验证设置:")
print(f"   分割器: {type(cv_splitter).__name__}")
print(f"   折数: 5")
print(f"   是否打乱: True")
print(f"   随机种子: 42")

# 定义要计算的指标
scoring = ['accuracy', 'precision_weighted', 'recall_weighted', 'f1_weighted']

print(f"\n🎯 评估指标: {scoring}")

# 执行交叉验证 - 这里是真正的计算！
print(f"\n🚀 开始执行交叉验证...")
cv_results = cross_validate(
    pipeline,           # 我们的完整管道
    X_raw,             # 原始特征（管道会自动处理）
    y_raw,             # 原始标签
    cv=cv_splitter,    # 交叉验证分割器
    scoring=scoring,   # 评估指标
    return_train_score=True,  # 同时返回训练集得分
    n_jobs=-1          # 使用所有CPU核心
)

print(f"✅ 交叉验证完成!")

# %% [markdown]
# ## 步骤 7: 深度分析交叉验证结果 - 数据随时可抽离！
# 
# 现在我们有了真实的交叉验证结果，让我们进行详细分析。
# 在这里，您可以随时抽离数据进行自定义分析！

# %%
print("📈 步骤 7: 深度分析结果")
print("=" * 60)

# 将结果转换为DataFrame便于分析
results_df = pd.DataFrame(cv_results)
print(f"🔍 交叉验证结果DataFrame:")
print(results_df.head())

print(f"\n📊 各指标的详细统计:")
for metric in scoring:
    test_scores = cv_results[f'test_{metric}']
    train_scores = cv_results[f'train_{metric}']
    
    print(f"\n🎯 {metric.upper()}:")
    print(f"   测试集: {test_scores.mean():.4f} ± {test_scores.std():.4f}")
    print(f"   训练集: {train_scores.mean():.4f} ± {train_scores.std():.4f}")
    print(f"   过拟合程度: {(train_scores.mean() - test_scores.mean()):.4f}")
    print(f"   各折结果: {[f'{score:.3f}' for score in test_scores]}")

# 🔥 这里您可以随时抽离数据进行自定义分析！
print(f"\n🔥 数据抽离示例 - 您可以随时这样做:")
print(f"   cv_results字典包含所有原始数据")
print(f"   可用的键: {list(cv_results.keys())}")

# 例如：分析模型稳定性
accuracy_scores = cv_results['test_accuracy']
stability_cv = accuracy_scores.std() / accuracy_scores.mean()
print(f"\n📊 模型稳定性分析:")
print(f"   准确率变异系数: {stability_cv:.4f}")
if stability_cv < 0.05:
    print(f"   ✅ 模型非常稳定")
elif stability_cv < 0.10:
    print(f"   ✅ 模型较为稳定")
else:
    print(f"   ⚠️ 模型稳定性有待提高")

# %% [markdown]
# ## 🎉 总结：我们完成了什么？
# 
# **恭喜！您已经完成了一个完全透明的交叉验证实验：**
# 
# ✅ **直接调用了框架的底层组件**（没有黑盒）
# ✅ **观察了数据在每个步骤的变化**（完全透明）
# ✅ **正确使用了Pipeline防止数据泄漏**（科学严谨）
# ✅ **获得了真实的交叉验证结果**（不是模拟数据）
# ✅ **可以随时抽离数据进行分析**（完全可控）
# 
# **关键收获：**
# - 理解了数据泄漏的危险性和Pipeline的重要性
# - 学会了如何手动组装机器学习管道
# - 掌握了交叉验证结果的深度分析方法
# - 获得了对框架底层组件的深入理解
# 
# **下一步建议：**
# - 尝试修改Pipeline中的组件参数
# - 实验不同的预处理方法组合
# - 比较不同模型的交叉验证结果
# - 使用真实的光谱数据进行实验
