# notebooks/utils.py
"""
公共工具函数，用于示例脚本中的结果可视化和分析。

这个模块提供了可复用的辅助函数，让主示例脚本更专注于展示核心工作流。
所有函数都设计为独立的，可以在不同的示例脚本中重复使用。


License: Apache-2.0
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
import seaborn as sns

# 设置绘图样式
plt.style.use('default')
sns.set_palette("husl")


def plot_cv_results(cv_results: Dict[str, Any], title: str = "交叉验证结果") -> None:
    """
    绘制交叉验证结果的可视化图表。
    
    Args:
        cv_results: 包含交叉验证结果的字典
        title: 图表标题
    """
    if not cv_results or 'fold_scores' not in cv_results:
        print("⚠️ 没有找到交叉验证结果数据")
        return
    
    fold_scores = cv_results['fold_scores']
    metrics = list(fold_scores.keys())
    
    fig, axes = plt.subplots(1, len(metrics), figsize=(5 * len(metrics), 4))
    if len(metrics) == 1:
        axes = [axes]
    
    for i, metric in enumerate(metrics):
        scores = fold_scores[metric]
        
        # 箱线图
        axes[i].boxplot(scores, labels=[metric.upper()])
        axes[i].scatter([1] * len(scores), scores, alpha=0.6, color='red')
        
        # 添加统计信息
        mean_score = np.mean(scores)
        std_score = np.std(scores)
        axes[i].axhline(y=mean_score, color='blue', linestyle='--', alpha=0.7)
        axes[i].text(1.1, mean_score, f'均值: {mean_score:.3f}\n标准差: {std_score:.3f}', 
                    verticalalignment='center')
        
        axes[i].set_title(f'{metric.upper()} 分布')
        axes[i].grid(True, alpha=0.3)
    
    plt.suptitle(title, fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.show()


def print_best_hyperparams(hyperopt_results: Dict[str, Any]) -> None:
    """
    打印超参数优化的最佳结果。
    
    Args:
        hyperopt_results: 包含超参数优化结果的字典
    """
    if not hyperopt_results:
        print("⚠️ 没有找到超参数优化结果")
        return
    
    print("🎯 超参数优化结果")
    print("=" * 50)
    
    if 'best_params' in hyperopt_results:
        print("📋 最佳参数组合:")
        for param, value in hyperopt_results['best_params'].items():
            print(f"  • {param}: {value}")
    
    if 'best_score' in hyperopt_results:
        print(f"\n🏆 最佳得分: {hyperopt_results['best_score']:.4f}")
    
    if 'n_trials' in hyperopt_results:
        print(f"🔄 总试验次数: {hyperopt_results['n_trials']}")
    
    print("=" * 50)


def plot_feature_importance(feature_importance: Dict[str, float], 
                          title: str = "特征重要性", 
                          top_n: int = 20) -> None:
    """
    绘制特征重要性图表。
    
    Args:
        feature_importance: 特征重要性字典
        title: 图表标题
        top_n: 显示前N个重要特征
    """
    if not feature_importance:
        print("⚠️ 没有找到特征重要性数据")
        return
    
    # 按重要性排序并取前N个
    sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:top_n]
    
    features, importance = zip(*sorted_features)
    
    plt.figure(figsize=(10, max(6, len(features) * 0.3)))
    bars = plt.barh(range(len(features)), importance)
    plt.yticks(range(len(features)), features)
    plt.xlabel('重要性得分')
    plt.title(title)
    plt.gca().invert_yaxis()  # 最重要的特征在顶部
    
    # 添加数值标签
    for i, (bar, imp) in enumerate(zip(bars, importance)):
        plt.text(bar.get_width() + max(importance) * 0.01, bar.get_y() + bar.get_height()/2, 
                f'{imp:.3f}', ha='left', va='center')
    
    plt.tight_layout()
    plt.show()


def print_experiment_summary(results: Dict[str, Any]) -> None:
    """
    打印实验结果的详细摘要。
    
    Args:
        results: 实验结果字典
    """
    print("📊 实验结果摘要")
    print("=" * 60)
    
    if 'experiment_name' in results:
        print(f"🔬 实验名称: {results['experiment_name']}")
    
    if 'model_name' in results:
        print(f"🤖 使用模型: {results['model_name']}")
    
    if 'execution_time' in results:
        print(f"⏱️ 执行时间: {results['execution_time']:.2f} 秒")
    
    if 'status' in results:
        status_emoji = "✅" if results['status'] == 'success' else "❌"
        print(f"{status_emoji} 执行状态: {results['status']}")
    
    if 'metrics' in results:
        print("\n📈 性能指标:")
        for metric, value in results['metrics'].items():
            # 处理不同格式的指标值
            if isinstance(value, dict):
                # 如果是字典格式，尝试提取 'value' 键
                if 'value' in value:
                    metric_value = value['value']
                    if isinstance(metric_value, (int, float)):
                        print(f"  • {metric}: {metric_value:.4f}")
                    else:
                        print(f"  • {metric}: {metric_value}")
                else:
                    print(f"  • {metric}: {value}")
            elif isinstance(value, (int, float)):
                # 如果是数值格式
                print(f"  • {metric}: {value:.4f}")
            else:
                # 其他格式直接打印
                print(f"  • {metric}: {value}")
    
    print("=" * 60)


def compare_experiments(results_list: List[Dict[str, Any]], 
                       experiment_names: Optional[List[str]] = None) -> None:
    """
    比较多个实验的结果。
    
    Args:
        results_list: 实验结果列表
        experiment_names: 实验名称列表
    """
    if not results_list:
        print("⚠️ 没有提供实验结果")
        return
    
    if experiment_names is None:
        experiment_names = [f"实验 {i+1}" for i in range(len(results_list))]
    
    print("🔍 实验对比分析")
    print("=" * 80)
    
    # 收集所有指标
    all_metrics = set()
    for result in results_list:
        if 'metrics' in result:
            all_metrics.update(result['metrics'].keys())
    
    if not all_metrics:
        print("⚠️ 没有找到可比较的指标")
        return
    
    # 创建比较表格
    comparison_data = []
    for i, (result, name) in enumerate(zip(results_list, experiment_names)):
        row = {'实验': name}
        if 'metrics' in result:
            for metric in all_metrics:
                metric_value = result['metrics'].get(metric, 'N/A')
                # 处理不同格式的指标值
                if isinstance(metric_value, dict) and 'value' in metric_value:
                    value = metric_value['value']
                    if isinstance(value, (int, float)):
                        row[metric] = f"{value:.4f}"
                    else:
                        row[metric] = str(value)
                elif isinstance(metric_value, (int, float)):
                    row[metric] = f"{metric_value:.4f}"
                else:
                    row[metric] = str(metric_value)
        comparison_data.append(row)
    
    df = pd.DataFrame(comparison_data)
    print(df.to_string(index=False))
    print("=" * 80)
