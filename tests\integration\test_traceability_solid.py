# tests/integration/test_traceability_solid.py
"""
溯源功能集成测试模块。

此模块测试框架的溯源功能，确保在不同级别下能正确保存调试产物。
设计理念：通过端到端的集成测试验证溯源功能的完整性和正确性。


License: Apache-2.0
"""

import pytest
import json
import pandas as pd
from pathlib import Path

from my_spectral_framework.core.config_models import ExperimentRootConfig
from my_spectral_framework.runners import SingleRunRunner
from my_spectral_framework.core.engine import SklearnExecutionEngine
from tests.core.test_registry import integration_test


@pytest.fixture(scope="module")
def traceability_test_runner():
    """一个辅助函数，用于根据给定的溯源级别运行一个完整的实验。"""
    def run_with_level(level: str, tmp_path: Path):
        config_dict = {
            "experiment_type": "single_run",
            "data_config": {
                "source_type": "opensa_test",
                "type": "Cls",
                "test_size": 0.2,
                "random_seed": 42
            },
            "preprocessing_config": {
                "steps": [{"method": "SNV"}]
            },
            "feature_selection_config": {
                "method": "Pca",
                "params": {"n_components": 5}
            },
            "engine_config": {
                "engine": "sklearn",
                "ml_model_config": {
                    "type": "Classification",
                    "name": "RF",
                    "params": {"n_estimators": 5}
                }
            },
            "traceability_config": {
                "level": level
            },
            "experiment_config": {
                "name": f"traceability_{level}_test"
            },
            "evaluation_config": {
                "metrics": {
                    "classification": ["accuracy", "precision", "recall", "f1"],
                    "regression": ["rmse", "r2", "mae"]
                },
                "save_predictions": True,
                "save_model": True
            },
            "paths_config": {
                "data_dir": "data",
                "models_dir": "models",
                "results_dir": str(tmp_path),  # 使用临时目录
                "logs_dir": "logs"
            }
        }
        config_obj = ExperimentRootConfig.model_validate(config_dict)
        engine = SklearnExecutionEngine()
        runner = SingleRunRunner(config_obj, engine)
        runner.run()
        return runner.handler.experiment_dir

    return run_with_level


@integration_test(
    description="[TRACEABILITY] 验证 'basic' 溯源级别能正确保存ID划分",
    tags=["traceability", "basic"],
    priority="critical"
)
def test_traceability_level_basic(traceability_test_runner, tmp_path):
    """职责: 验证 'basic' 级别只保存ID划分，不保存完整数据。"""
    exp_dir = traceability_test_runner("basic", tmp_path)
    
    debug_dir = exp_dir / "debug_artifacts"
    assert debug_dir.exists(), "'debug_artifacts' 目录应被创建"
    
    split_file = debug_dir / "split_indices.json"
    assert split_file.exists(), "'split_indices.json' 文件应存在"
    
    assert not (debug_dir / "X_train_raw.csv").exists(), "在'basic'级别不应保存完整数据矩阵"
    
    with open(split_file, 'r') as f:
        indices = json.load(f)
    assert "train_ids" in indices
    assert "test_ids" in indices
    assert len(indices["train_ids"]) > 0


@integration_test(
    description="[TRACEABILITY] 验证 'full' 溯源级别能正确保存中间数据",
    tags=["traceability", "full"],
    priority="critical"
)
def test_traceability_level_full(traceability_test_runner, tmp_path):
    """职责: 验证 'full' 级别保存了带ID的中间数据CSV文件。"""
    exp_dir = traceability_test_runner("full", tmp_path)
    
    debug_dir = exp_dir / "debug_artifacts"
    assert debug_dir.exists()
    
    # 检查原始数据文件
    raw_train_file = debug_dir / "X_train_raw.csv"
    assert raw_train_file.exists(), "应保存原始训练数据"
    
    # 检查一个关键的中间文件
    processed_file = debug_dir / "X_train_after_preprocessor.csv"
    assert processed_file.exists(), "应保存预处理后的数据"
    
    df = pd.read_csv(processed_file)
    assert "spectrum_id" in df.columns, "保存的数据应包含 'spectrum_id' 列"
    assert df.shape[1] > 1  # 确保除了ID还有特征数据


@integration_test(
    description="[TRACEABILITY] 验证 'none' 级别不保存调试产物",
    tags=["traceability", "none"],
    priority="normal"
)
def test_traceability_level_none(traceability_test_runner, tmp_path):
    """职责: 验证 'none' 级别不保存任何调试产物。"""
    exp_dir = traceability_test_runner("none", tmp_path)
    
    debug_dir = exp_dir / "debug_artifacts"
    # 在 'none' 级别，debug_artifacts 目录可能不存在，或者存在但为空
    if debug_dir.exists():
        # 如果目录存在，应该没有溯源相关的文件
        assert not (debug_dir / "split_indices.json").exists()
        assert not (debug_dir / "X_train_raw.csv").exists()
        assert not (debug_dir / "X_train_after_preprocessor.csv").exists()


@integration_test(
    description="[TRACEABILITY] 验证ID一致性：同一样本在不同步骤中保持相同ID",
    tags=["traceability", "consistency"],
    priority="high"
)
def test_id_consistency_across_steps(traceability_test_runner, tmp_path):
    """职责: 验证同一样本在数据处理的不同步骤中保持相同的ID。"""
    exp_dir = traceability_test_runner("full", tmp_path)
    
    debug_dir = exp_dir / "debug_artifacts"
    
    # 加载原始数据和处理后的数据
    df_raw = pd.read_csv(debug_dir / "X_train_raw.csv")
    df_processed = pd.read_csv(debug_dir / "X_train_after_preprocessor.csv")
    
    # 验证ID一致性
    assert len(df_raw) == len(df_processed), "原始数据和处理后数据的样本数应相同"
    assert list(df_raw["spectrum_id"]) == list(df_processed["spectrum_id"]), "ID顺序应保持一致"
    
    # 验证ID的唯一性
    assert len(set(df_raw["spectrum_id"])) == len(df_raw), "所有ID应该是唯一的"
