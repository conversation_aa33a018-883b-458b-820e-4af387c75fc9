"""
预处理模块的性能测试

使用SOLID装饰器系统进行性能基准测试，确保预处理操作在可接受的时间内完成。

Author: txy
License: Apache-2.0
"""

import sys
import time
from pathlib import Path

import numpy as np
import pytest

# 项目路径设置（现在通过包安装自动处理）
project_root = Path(__file__).parent.parent.parent

from my_spectral_framework.preprocessing.spectral_processors import CT, MMS, MSC, SNV, SS, get_transformer

# 导入SOLID装饰器注册系统
from tests.core.test_registry import performance_test


class TestPreprocessingPerformance:
    """
    预处理模块的性能测试 - 使用SOLID装饰器系统
    """

    def create_large_dataset(self, n_samples=1000, n_features=2000):
        """创建大型数据集用于性能测试"""
        return np.random.rand(n_samples, n_features).astype(np.float32)

    @performance_test(description="[性能] 验证SNV预处理在大数据集上的性能", tags=["snv", "large_dataset"], priority="high")
    def test_snv_performance_large_dataset(self, benchmark):
        """测试SNV在大数据集上的性能"""
        X = self.create_large_dataset(1000, 2000)

        # 使用pytest-benchmark进行基准测试
        result = benchmark(SNV, X)

        # 验证结果正确性
        assert result.shape == X.shape
        assert not np.isnan(result).any()

    @performance_test(description="[性能] 验证MSC预处理在大数据集上的性能", tags=["msc", "large_dataset"], priority="high")
    def test_msc_performance_large_dataset(self, benchmark):
        """测试MSC在大数据集上的性能"""
        X = self.create_large_dataset(1000, 2000)

        # 使用pytest-benchmark进行基准测试
        result = benchmark(MSC, X)

        # 验证结果正确性
        assert result.shape == X.shape
        assert not np.isnan(result).any()

    @performance_test(description="[性能] 验证工厂函数的性能开销", tags=["factory", "overhead"], priority="normal")
    def test_transformer_factory_performance(self, benchmark):
        """测试工厂函数的性能开销"""
        X = self.create_large_dataset(500, 1000)

        def factory_transform():
            transformer = get_transformer("SNV")
            return transformer.fit_transform(X)

        # 基准测试工厂函数
        result = benchmark(factory_transform)

        # 验证结果正确性
        assert result.shape == X.shape
        assert not np.isnan(result).any()

    @performance_test(
        description="[性能] 验证多种预处理方法的相对性能", tags=["comparison", "multiple_methods"], priority="normal"
    )
    def test_multiple_methods_performance_comparison(self):
        """比较多种预处理方法的性能"""
        X = self.create_large_dataset(500, 1000)
        methods = ["SNV", "MSC", "CT", "MMS", "SS"]

        performance_results = {}

        for method in methods:
            transformer = get_transformer(method)

            # 测量执行时间
            start_time = time.time()
            result = transformer.fit_transform(X)
            end_time = time.time()

            duration = end_time - start_time
            performance_results[method] = duration

            # 验证结果正确性
            assert result.shape == X.shape
            assert not np.isnan(result).any()

            # 性能断言：所有方法应在合理时间内完成
            assert duration < 5.0, f"{method} 耗时过长: {duration:.2f}秒"

        # 记录性能结果
        print(f"\n性能比较结果:")
        for method, duration in sorted(performance_results.items(), key=lambda x: x[1]):
            print(f"  {method}: {duration:.3f}秒")

    @performance_test(description="[性能] 验证内存使用效率", tags=["memory", "efficiency"], priority="normal")
    def test_memory_efficiency(self):
        """测试预处理操作的内存效率"""
        import os

        import psutil

        # 获取当前进程
        process = psutil.Process(os.getpid())

        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 创建大数据集并进行预处理
        X = self.create_large_dataset(2000, 3000)  # 约48MB的数据

        # 执行多种预处理
        methods = ["SNV", "MSC", "CT"]
        results = []

        for method in methods:
            transformer = get_transformer(method)
            result = transformer.fit_transform(X)
            results.append(result)

        # 记录峰值内存使用
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory

        # 内存使用断言：增长应该合理（不超过数据大小的5倍）
        data_size_mb = X.nbytes / 1024 / 1024
        max_acceptable_increase = data_size_mb * 5

        assert (
            memory_increase < max_acceptable_increase
        ), f"内存使用过多: 增长{memory_increase:.1f}MB，数据大小{data_size_mb:.1f}MB"

        print(f"\n内存使用统计:")
        print(f"  初始内存: {initial_memory:.1f}MB")
        print(f"  峰值内存: {peak_memory:.1f}MB")
        print(f"  内存增长: {memory_increase:.1f}MB")
        print(f"  数据大小: {data_size_mb:.1f}MB")

    @performance_test(description="[性能] 验证并发处理能力", tags=["concurrent", "threading"], priority="low")
    def test_concurrent_processing(self):
        """测试并发预处理的性能"""
        import concurrent.futures
        import threading

        X = self.create_large_dataset(200, 500)
        n_threads = 4

        def process_data(thread_id):
            """单线程处理函数"""
            transformer = get_transformer("SNV")
            result = transformer.fit_transform(X)
            return thread_id, result.shape, time.time()

        # 测量串行处理时间
        start_time = time.time()
        for i in range(n_threads):
            process_data(i)
        serial_time = time.time() - start_time

        # 测量并行处理时间
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=n_threads) as executor:
            futures = [executor.submit(process_data, i) for i in range(n_threads)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        parallel_time = time.time() - start_time

        # 验证结果
        assert len(results) == n_threads
        for thread_id, shape, timestamp in results:
            assert shape == X.shape

        # 性能断言：并行处理应该更快（至少快20%）
        speedup = serial_time / parallel_time
        assert speedup > 1.2, f"并行处理效果不明显: 加速比{speedup:.2f}"

        print(f"\n并发处理性能:")
        print(f"  串行时间: {serial_time:.3f}秒")
        print(f"  并行时间: {parallel_time:.3f}秒")
        print(f"  加速比: {speedup:.2f}x")
