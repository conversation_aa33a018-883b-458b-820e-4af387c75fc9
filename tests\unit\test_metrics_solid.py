"""
SOLID原则驱动的评估指标测试 - 函数级精度验证
"""
import numpy as np
import pytest
from sklearn.metrics import (
    accuracy_score, f1_score, mean_absolute_error,
    mean_squared_error, precision_score, r2_score, recall_score
)

# 导入被测试的函数
from my_spectral_framework.evaluation.metrics import (
    calculate_classification_metrics,
    calculate_metrics,
    calculate_regression_metrics,
)

# 导入新的装饰器注册系统
from tests.core.test_registry import unit_test

class TestClassificationMetrics:
    """测试分类指标计算函数"""

    @unit_test(
        description="[SRP] 验证完美分类时所有指标都为1.0",
        tags=["metrics", "classification", "perfect_case"],
        priority="high"
    )
    def test_calculate_classification_metrics_perfect_score(self):
        y_true = np.array([0, 1, 1, 0, 1, 0])
        y_pred = np.array([0, 1, 1, 0, 1, 0])
        metrics = calculate_classification_metrics(y_true, y_pred)
        assert metrics["accuracy"] == 1.0
        assert metrics["f1_score"] == 1.0
        assert metrics["precision"] == 1.0
        assert metrics["recall"] == 1.0

    @unit_test(
        description="[SRP] 验证分类指标与sklearn的已知值一致",
        tags=["metrics", "classification", "correctness"],
        priority="critical"
    )
    def test_calculate_classification_metrics_known_values(self):
        y_true = np.array([0, 0, 1, 1])
        y_pred = np.array([0, 1, 1, 1])
        metrics = calculate_classification_metrics(y_true, y_pred)
        assert np.isclose(metrics["accuracy"], accuracy_score(y_true, y_pred))
        assert np.isclose(metrics["f1_score"], f1_score(y_true, y_pred, average="weighted"))

    @unit_test(
        description="[SRP] 验证多分类指标计算的有效性",
        tags=["metrics", "classification", "multiclass"],
        priority="normal"
    )
    def test_calculate_classification_metrics_multiclass(self):
        y_true = np.array([0, 1, 2, 0, 1, 2])
        y_pred = np.array([0, 1, 1, 0, 2, 2])
        metrics = calculate_classification_metrics(y_true, y_pred)
        assert 0.0 <= metrics["accuracy"] <= 1.0
        assert np.isclose(metrics["accuracy"], accuracy_score(y_true, y_pred))

class TestRegressionMetrics:
    """测试回归指标计算函数"""

    @unit_test(
        description="[SRP] 验证完美回归预测时指标的正确性",
        tags=["metrics", "regression", "perfect_case"],
        priority="high"
    )
    def test_calculate_regression_metrics_perfect_prediction(self):
        y_true = np.array([1.0, 2.0, 3.0, 4.0])
        y_pred = np.array([1.0, 2.0, 3.0, 4.0])
        metrics = calculate_regression_metrics(y_true, y_pred)
        assert np.isclose(metrics["rmse"], 0.0)
        assert np.isclose(metrics["mae"], 0.0)
        assert np.isclose(metrics["r2"], 1.0)

    @unit_test(
        description="[SRP] 验证回归指标与sklearn的已知值一致",
        tags=["metrics", "regression", "correctness"],
        priority="critical"
    )
    def test_calculate_regression_metrics_known_values(self):
        y_true = np.array([1.0, 2.0, 3.0])
        y_pred = np.array([1.5, 2.5, 3.5])
        metrics = calculate_regression_metrics(y_true, y_pred)
        assert np.isclose(metrics["rmse"], np.sqrt(mean_squared_error(y_true, y_pred)))
        assert np.isclose(metrics["mae"], mean_absolute_error(y_true, y_pred))
        assert np.isclose(metrics["r2"], r2_score(y_true, y_pred))

class TestMetricsDispatcher:
    """测试指标计算分发器"""

    @unit_test(
        description="[SRP] 验证指标分发器能为'classification'任务调用正确的函数",
        tags=["metrics", "dispatcher"],
        priority="high"
    )
    def test_calculate_metrics_dispatches_to_classification(self):
        y_true = np.array([0, 1, 1, 0])
        y_pred = np.array([0, 1, 0, 0])
        metrics = calculate_metrics(y_true, y_pred, task_type="classification")
        assert "accuracy" in metrics
        assert "rmse" not in metrics

    @unit_test(
        description="[SRP] 验证指标分发器能为'regression'任务调用正确的函数",
        tags=["metrics", "dispatcher"],
        priority="high"
    )
    def test_calculate_metrics_dispatches_to_regression(self):
        y_true = np.array([1.0, 2.0, 3.0])
        y_pred = np.array([1.1, 2.1, 3.1])
        metrics = calculate_metrics(y_true, y_pred, task_type="regression")
        assert "rmse" in metrics
        assert "accuracy" not in metrics

    @unit_test(
        description="[SRP] 验证指标分发器对无效任务类型抛出ValueError",
        tags=["metrics", "dispatcher", "error_handling"],
        priority="normal"
    )
    def test_calculate_metrics_handles_invalid_task_type(self):
        with pytest.raises(ValueError, match="不支持的任务类型"):
            calculate_metrics(np.array([0]), np.array([0]), task_type="invalid_task")
