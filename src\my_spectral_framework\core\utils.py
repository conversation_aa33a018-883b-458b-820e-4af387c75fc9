"""
光谱分析框架的核心工具函数。

本模块提供了一系列核心工具函数，包括日志记录、模型保存/加载、
结果格式化以及项目路径管理。这些函数旨在支持光谱数据分析工作流中的常见操作，
确保代码的模块化、可重用性和易于维护性。

作者: txy
许可证: Apache-2.0
"""

import json
import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import TYPE_CHECKING, Any, Dict, Optional, Union

import joblib
import numpy as np
import pandas as pd
import torch

if TYPE_CHECKING:
    from .context import ExperimentContext


def json_custom_serializer(obj):
    """
    增强型 JSON 序列化器，用于处理 NumPy 数组、Pandas DataFrames 和其他不可直接序列化的对象。

    当需要将包含 NumPy 数据类型（如数组、标量）、Pandas DataFrames、Path、datetime 对象的
    Python 字典/列表转换为 JSON 格式时，Python 内置的 json 模块可能无法直接处理。
    此函数作为 `json.dump` 的 `default` 参数，提供自定义的序列化逻辑，
    将这些特殊类型转换为 JSON 可识别的格式。

    参数:
        obj: 待序列化的 Python 对象。

    返回:
        obj 的 JSON 可序列化表示。
        - 如果 obj 是 NumPy 数组 (`np.ndarray`)，则转换为 Python 列表。
        - 如果 obj 是 Pandas DataFrame (`pd.DataFrame`)，则转换为记录列表格式。
        - 如果 obj 是 NumPy 标量 (`np.generic`)，则转换为 Python 原生类型。
        - 如果 obj 是 Path 对象 (`pathlib.Path`)，则转换为其字符串表示。
        - 如果 obj 具有 `isoformat` 方法（如 `datetime` 对象），则调用该方法转换为 ISO 格式字符串。
        - 对于其他所有不可直接序列化的对象，尝试转换为其字符串表示。
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()  # 将 NumPy 数组转换为 Python 列表
    elif isinstance(obj, pd.DataFrame):
        return obj.to_dict(orient='records')  # 将 DataFrame 转换为记录列表格式
    elif isinstance(obj, np.generic):
        return obj.item()  # 将 NumPy 标量转换为 Python 原生类型
    elif isinstance(obj, Path):
        return str(obj)  # 将 Path 对象转换为字符串
    elif hasattr(obj, "isoformat"):  # 检查对象是否有 isoformat 方法（通常用于 datetime 对象）
        return obj.isoformat()  # 将 datetime 对象转换为 ISO 格式的字符串
    else:
        return str(obj)  # 对于其他无法直接序列化的对象，尝试转换为字符串


def get_logger(name: str) -> logging.Logger:
    """
    获取一个以模块名命名的日志记录器。

    真正的配置（如级别、处理器等）将由 logging.config.dictConfig 统一加载。
    这个函数简化为只获取记录器实例，确保了命名的一致性。

    参数:
        name (str): 日志记录器的名称，通常使用 __name__ 来获取模块名。

    返回:
        logging.Logger: 日志记录器实例。

    示例:
        >>> logger = get_logger(__name__)
        >>> logger.info("这是一条信息日志。")
        >>> logger.debug("这是一条调试日志。")
    """
    return logging.getLogger(name)


def save_model(model: Any, path: str, model_type: str = "sklearn") -> None:
    """
    将训练好的模型保存到磁盘。

    此函数根据指定的模型类型（scikit-learn 或 PyTorch）选择不同的序列化方法。
    在保存模型之前，它会确保目标目录存在。

    参数:
        model (Any): 训练好的模型实例。可以是 scikit-learn 模型对象或 PyTorch 模型对象。
        path (str): 模型保存的完整文件路径，包括文件名和扩展名（例如："./models/my_model.joblib"）。
        model_type (str): 模型的类型。支持 "sklearn"（scikit-learn 模型）和 "pytorch"（PyTorch 模型）。
                          默认为 "sklearn"。

    抛出:
        ValueError: 如果 `model_type` 不受支持，则抛出此异常。

    示例:
        >>> from sklearn.linear_model import LogisticRegression
        >>> model_sklearn = LogisticRegression()
        >>> # ... 训练 model_sklearn ...
        >>> save_model(model_sklearn, "./models/logistic_regression.joblib", "sklearn")

        >>> import torch.nn as nn
        >>> class SimpleNN(nn.Module):
        ...     def __init__(self):
        ...         super().__init__()
        ...         self.linear = nn.Linear(10, 1)
        ...     def forward(self, x):
        ...         return self.linear(x)
        >>> model_pytorch = SimpleNN()
        >>> # ... 训练 model_pytorch ...
        >>> save_model(model_pytorch, "./models/simple_nn.pt", "pytorch")
    """
    # 确保模型保存的目录存在。如果目录不存在，os.makedirs 会创建它。
    # exist_ok=True 意味着如果目录已经存在，则不会引发错误。
    os.makedirs(os.path.dirname(path), exist_ok=True)

    if model_type.lower() == "sklearn":
        # 对于 scikit-learn 模型，使用 joblib.dump 进行序列化。
        # joblib 适用于大型 NumPy 数组，效率高。
        joblib.dump(model, path)
    elif model_type.lower() == "pytorch":
        # 对于 PyTorch 模型，保存其状态字典 (state_dict)。
        # 状态字典包含了模型的所有可学习参数（权重和偏置）。
        # 这种方式更推荐，因为它允许在加载时灵活地重新构建模型。
        torch.save(model.state_dict(), path)
    else:
        # 如果模型类型不受支持，则抛出 ValueError。
        raise ValueError(f"不支持的模型类型: {model_type}")


def load_model(path: str, model_class: Any = None, model_type: str = "sklearn") -> Any:
    """
    从磁盘加载训练好的模型。

    此函数根据指定的模型类型（scikit-learn 或 PyTorch）选择不同的反序列化方法。
    对于 PyTorch 模型，需要提供模型的类定义，以便能够重新实例化模型结构。

    参数:
        path (str): 已保存模型的完整文件路径。
        model_class (Any, optional): 模型的类定义。对于 PyTorch 模型，这是必需的，
                                     因为 `torch.load` 只加载状态字典，不加载模型结构。
                                     对于 scikit-learn 模型，此参数可以省略。默认为 None。
        model_type (str): 模型的类型。支持 "sklearn"（scikit-learn 模型）和 "pytorch"（PyTorch 模型）。
                          默认为 "sklearn"。

    返回:
        Any: 加载后的模型实例。

    抛出:
        ValueError: 如果 `model_type` 不受支持，或者对于 PyTorch 模型未提供 `model_class`，
                    则抛出此异常。

    示例:
        >>> from sklearn.linear_model import LogisticRegression
        >>> loaded_sklearn_model = load_model("./models/logistic_regression.joblib", model_type="sklearn")

        >>> import torch.nn as nn
        >>> class SimpleNN(nn.Module):
        ...     def __init__(self):
        ...         super().__init__()
        ...         self.linear = nn.Linear(10, 1)
        ...     def forward(self, x):
        ...         return self.linear(x)
        >>> loaded_pytorch_model = load_model("./models/simple_nn.pt", SimpleNN, "pytorch")
    """
    if model_type.lower() == "sklearn":
        # 对于 scikit-learn 模型，使用 joblib.load 直接加载模型对象。
        return joblib.load(path)
    elif model_type.lower() == "pytorch":
        # 对于 PyTorch 模型：
        if model_class is None:
            # 如果未提供模型类，则无法重建模型结构，抛出错误。
            raise ValueError("对于 PyTorch 模型，必须提供 model_class 参数。")
        # 实例化模型类，创建一个空的模型结构。
        model = model_class()
        # 从保存的路径加载状态字典，并将其加载到新创建的模型实例中。
        model.load_state_dict(torch.load(path))
        return model
    else:
        # 如果模型类型不受支持，则抛出 ValueError。
        raise ValueError(f"不支持的模型类型: {model_type}")


def print_experiment_results(metrics_dict: Dict[str, Any], logger: Optional[logging.Logger] = None) -> None:
    """
    格式化并打印实验结果。

    此函数接收一个包含实验指标和元数据的字典，并使用提供的日志记录器（如果未提供则获取默认日志记录器）
    以结构化和易读的方式输出这些信息。它会打印实验名称、时间戳、数据信息、模型配置和性能指标。

    参数:
        metrics_dict (Dict[str, Any]): 包含实验指标和元数据的字典。
                                      期望包含以下键（如果存在）：
                                      - "experiment_name" (str): 实验的名称。
                                      - "timestamp" (str): 实验的时间戳。
                                      - "data_info" (Dict[str, Any]): 包含数据相关信息的字典。
                                      - "model_config" (Dict[str, Any]): 包含模型配置的字典，
                                                                        可能包含 "type" 和 "name" 键。
                                      - "metrics" (Dict[str, Any]): 包含性能指标的字典。
        logger (logging.Logger, optional): 用于输出的日志记录器实例。如果为 None，则会自动获取一个默认日志记录器。
                                           默认为 None。

    示例:
        >>> results = {
        ...     "experiment_name": "光谱分类实验",
        ...     "timestamp": "2023-10-27T10:30:00",
        ...     "data_info": {"dataset_size": 1000, "features": 256},
        ...     "model_config": {"type": "CNN", "name": "ResNet18"},
        ...     "metrics": {"accuracy": 0.95, "precision": 0.92, "recall": 0.98}
        ... }
        >>> print_experiment_results(results)
    """
    if logger is None:
        # 如果没有提供日志记录器，则获取一个默认的日志记录器实例。
        logger = get_logger(__name__)

    # 打印实验结果的标题和分隔线，使其在日志中更显眼。
    logger.info("=" * 60)
    logger.info("实验结果")
    logger.info("=" * 60)

    # 打印实验元数据
    if "experiment_name" in metrics_dict:
        logger.info(f"实验名称: {metrics_dict['experiment_name']}")
    if "timestamp" in metrics_dict:
        logger.info(f"时间戳: {metrics_dict['timestamp']}")

    logger.info("-" * 40)  # 打印分隔线

    # 打印数据信息
    if "data_info" in metrics_dict:
        data_info = metrics_dict["data_info"]
        logger.info("数据信息:")
        for key, value in data_info.items():
            logger.info(f"  {key}: {value}")  # 遍历并打印数据信息的键值对
        logger.info("-" * 40)  # 打印分隔线

    # 打印模型配置
    if "model_config" in metrics_dict:
        model_config = metrics_dict["model_config"]
        logger.info("模型配置:")
        # 使用 .get() 方法安全地获取键值，如果键不存在则返回默认值 'Unknown'。
        logger.info(f"  类型: {model_config.get('type', '未知')}")
        logger.info(f"  名称: {model_config.get('name', '未知')}")
        logger.info("-" * 40)  # 打印分隔线

    # 打印性能指标
    if "metrics" in metrics_dict:
        metrics = metrics_dict["metrics"]
        logger.info("性能指标:")
        for metric_name, metric_value in metrics.items():
            if isinstance(metric_value, float):
                # 如果指标值是浮点数，则格式化为小数点后六位。
                logger.info(f"  {metric_name}: {metric_value:.6f}")
            else:
                logger.info(f"  {metric_name}: {metric_value}")

    logger.info("=" * 60)  # 打印底部分隔线


def get_project_root() -> Path:
    """
    获取项目的根目录。

    此函数通过从当前文件 (`utils.py`) 的位置开始，逐级向上查找，直到找到包含
    "src" 子目录的父目录。这个包含 "src" 目录的父目录被认为是项目的根目录。
    这种方法使得项目无论在文件系统中的哪个位置，都能正确地定位其根目录。

    返回:
        Path: 指向项目根目录的 Path 对象。

    示例:
        假设文件结构如下:
        /path/to/my_project/
        ├── src/
        │   └── my_spectral_framework/
        │       └── core/
        │           └── utils.py
        └── outputs/

        如果 `utils.py` 是当前文件，那么 `get_project_root()` 将返回 `/path/to/my_project/` 的 Path 对象。
    """
    # 获取当前文件 (utils.py) 的绝对路径，并将其解析为 Path 对象。
    current_path = Path(__file__).resolve()

    # 循环向上遍历父目录，直到找到项目根目录。
    # 循环条件 `current_path.parent != current_path` 防止在到达文件系统根目录时无限循环。
    while current_path.parent != current_path:
        # 检查当前路径下是否存在名为 "src" 的子目录。
        # 如果存在，则认为当前路径是项目的根目录。
        if (current_path / "src").exists():
            return current_path  # 返回找到的项目根目录

        # 如果没有找到 "src" 目录，则向上移动到当前路径的父目录，继续查找。
        current_path = current_path.parent

    # 如果循环结束仍未找到包含 "src" 目录的父目录，
    # 则返回当前文件所在目录的四级父目录作为备用（这通常意味着项目结构不符合预期，
    # 或者文件位于一个非常深的嵌套目录中，且其上层没有明确的“src”根）。
    # 这是一个回退机制，以防万一。
    return Path(__file__).resolve().parent.parent.parent.parent


def save_experiment_results(results: Dict[str, Any], experiment_name: str, results_dir: str = "results") -> str:
    """
    将实验结果保存到 JSON 文件。

    此函数负责将一个包含实验结果的字典序列化为 JSON 格式，并保存到指定目录下的文件中。
    文件名将包含实验名称和当前时间戳，以确保唯一性。

    参数:
        results (Dict[str, Any]): 包含实验结果的字典。
        experiment_name (str): 实验的名称，将用于生成文件名。
        results_dir (str): 结果文件保存的子目录名称，相对于项目根目录。默认为 "results"。

    返回:
        str: 保存结果文件的完整路径。

    示例:
        >>> experiment_data = {"accuracy": 0.9, "model": "SVM"}
        >>> saved_path = save_experiment_results(experiment_data, "my_first_experiment")
        >>> print(f"结果已保存到: {saved_path}")
    """
    # 获取项目根目录，并构建结果保存的完整路径。
    project_root = get_project_root()
    results_path = project_root / results_dir
    # 创建结果目录。parents=True 确保创建所有缺失的父目录；exist_ok=True 避免目录已存在时报错。
    results_path.mkdir(parents=True, exist_ok=True)

    # 向结果字典中添加当前时间戳和实验名称。
    results["timestamp"] = datetime.now().isoformat()  # 以 ISO 8601 格式添加当前时间
    results["experiment_name"] = experiment_name

    # 根据实验名称和当前时间戳生成唯一的文件名。
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")  # 格式化时间戳为 YYYYMMDD_HHMMSS
    filename = f"{experiment_name}_{timestamp}.json"  # 组合文件名
    filepath = results_path / filename  # 构建完整的文件路径

    # 将结果字典以 JSON 格式写入文件。
    # 'w' 模式表示写入（如果文件不存在则创建，存在则覆盖）。
    # encoding='utf-8' 确保支持中文等非 ASCII 字符。
    # indent=2 使 JSON 输出格式化，易于阅读。
    # ensure_ascii=False 允许直接写入非 ASCII 字符（如中文），而不是转义。
    # default=json_custom_serializer 用于处理字典中不可直接 JSON 序列化的对象。
    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=json_custom_serializer)

    return str(filepath)  # 返回保存文件的字符串路径


def generate_experiment_dirname(context: "ExperimentContext") -> str:
    """
    根据时间戳和实验配置生成人类可读的实验目录名称。

    此函数从 `ExperimentContext` 对象中提取关键配置信息，如实验类型、模型名称、
    预处理方法和特征选择方法，并结合当前时间戳，生成一个描述性强且唯一的目录名。
    这有助于组织和识别不同实验的输出结果。

    参数:
        context (ExperimentContext): 实验上下文实例，包含当前实验的所有配置和状态信息。

    返回:
        str: 生成的实验目录名称字符串。

    示例:
        假设 context.config 包含:
        {
            "experiment_type": "cross_validation",
            "model_config": {"name": "RandomForest"},
            "preprocessing_config": {"method": "SNV"},
            "feature_selection_config": {"method": "PCA"},
            "cv_splits": 10
        }
        则可能返回类似 "2023-10-27_10-30-00_10fold_cv_randomforest_snv_pca" 的字符串。
    """
    # 获取当前时间戳，格式化为 "YYYY-MM-DD_HH-MM-SS"。
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # 从 ExperimentContext 中获取实验配置。
    config = context.config
    # 获取实验类型，如果未指定则默认为 "single"（单次运行）。
    exp_type = config.get("experiment_type", "single")
    # 获取模型名称，如果未指定则默认为 "unknown"，并转换为小写。
    model_name = config.get("model_config", {}).get("name", "unknown").lower()
    # 获取预处理方法，如果未指定则默认为 "none"，并转换为小写。
    preprocessing = config.get("preprocessing_config", {}).get("method", "none").lower()
    # 获取特征选择方法，如果未指定则默认为 "none"，并转换为小写。
    feature_selection = config.get("feature_selection_config", {}).get("method", "none").lower()

    # 根据实验类型创建描述性名称。
    if exp_type == "cross_validation":
        # 如果是交叉验证实验，获取交叉验证的折数，默认为 5。
        cv_folds = config.get("cv_splits", 5)
        # 组合目录名，包含时间戳、折数、模型、预处理和特征选择信息。
        dirname = f"{timestamp}_{cv_folds}fold_cv_{model_name}_{preprocessing}_{feature_selection}"
    elif exp_type == "hyperparameter_optimization":
        # 如果是超参数优化实验，组合目录名，包含时间戳、"hyperopt"、模型、预处理和特征选择信息。
        dirname = f"{timestamp}_hyperopt_{model_name}_{preprocessing}_{feature_selection}"
    else:
        # 如果是单次运行实验，组合目录名，包含时间戳、"single"、模型、预处理和特征选择信息。
        dirname = f"{timestamp}_single_{model_name}_{preprocessing}_{feature_selection}"

    return dirname  # 返回生成的目录名称


def save_artifact(context: "ExperimentContext", artifact_name: str, data: Any, file_extension: str, subdir: str = "") -> str:
    """
    保存任何实验产物（artifact），并组织在人类可读的目录结构中。

    此函数根据实验上下文生成一个唯一的实验运行目录，并在该目录下创建子目录（如果指定），
    然后将数据保存为指定文件类型（如 joblib, pt, csv, npy, json）。
    它支持多种数据类型和文件格式的自动处理，并记录保存过程中的日志和错误。

    参数:
        context (ExperimentContext): 实验上下文实例，用于获取实验信息和记录产物路径。
        artifact_name (str): 产物的逻辑名称（例如："trained_model", "predictions", "feature_importance"）。
                             这将作为文件名的一部分。
        data (Any): 要保存的数据对象。其类型应与 `file_extension` 兼容。
        file_extension (str): 产物的文件扩展名（例如："joblib", "pt", "csv", "npy", "json"）。
        subdir (str, optional): 在实验运行目录下创建的子目录名称。如果为空字符串，则直接保存在运行目录下。
                                默认为空字符串。

    返回:
        str: 保存文件的完整路径。如果保存失败，则返回空字符串。

    抛出:
        ValueError: 如果 `file_extension` 不受支持，则在尝试保存时抛出。

    示例:
        >>> # 假设 context 是一个 ExperimentContext 实例
        >>> from sklearn.linear_model import LinearRegression
        >>> model = LinearRegression()
        >>> # ... 训练模型 ...
        >>> save_artifact(context, "my_linear_model", model, "joblib", subdir="models")
        >>> # 保存一个 NumPy 数组
        >>> import numpy as np
        >>> data_array = np.array([1, 2, 3])
        >>> save_artifact(context, "processed_data", data_array, "npy", subdir="data")
    """
    logger = get_logger(__name__)  # 获取当前模块的日志记录器

    # 生成人类可读的实验目录名称。如果 context 对象中还没有这个属性，则调用函数生成。
    if not hasattr(context, "_experiment_dirname"):
        context._experiment_dirname = generate_experiment_dirname(context)

    # 构建实验运行目录的完整路径：项目根目录 / 'outputs' / 实验目录名。
    run_dir = get_project_root() / "outputs" / context._experiment_dirname
    # 构建产物保存目录的完整路径：如果指定了 subdir，则为 run_dir / subdir，否则就是 run_dir。
    artifact_dir = run_dir / subdir if subdir else run_dir
    # 创建产物目录。parents=True 确保创建所有缺失的父目录；exist_ok=True 避免目录已存在时报错。
    artifact_dir.mkdir(parents=True, exist_ok=True)

    # 构建产物的完整文件路径：产物目录 / "产物名称.文件扩展名"。
    file_path = artifact_dir / f"{artifact_name}.{file_extension}"

    try:
        # 根据文件扩展名选择不同的保存方法。
        if file_extension == "joblib":
            # 使用 joblib 保存 Python 对象（常用于 scikit-learn 模型）。
            joblib.dump(data, file_path)
        elif file_extension == "pt":
            # 保存 PyTorch 模型或张量。
            # 如果数据对象有 state_dict 方法（通常是 PyTorch 模型），则保存状态字典以获得更好的兼容性。
            if hasattr(data, "state_dict"):
                torch.save(data.state_dict(), file_path)
                logger.info(f"已保存 PyTorch 模型状态字典")
            else:
                # 否则，直接保存数据（例如 PyTorch 张量）。
                torch.save(data, file_path)
                logger.info(f"已保存 PyTorch 张量/对象")
        elif file_extension == "csv":
            # 保存为 CSV 文件。
            # 如果数据对象有 to_csv 方法（通常是 pandas DataFrame），则直接调用。
            if hasattr(data, "to_csv"):
                data.to_csv(file_path, index=False)  # index=False 不保存 DataFrame 索引
            else:
                # 否则，尝试将数据转换为 pandas DataFrame 再保存。
                pd.DataFrame(data).to_csv(file_path, index=False)
        elif file_extension == "npy":
            # 保存为 NumPy .npy 格式文件（用于存储 NumPy 数组）。
            np.save(file_path, data)
        elif file_extension == "json":
            # 保存为 JSON 文件。
            with open(file_path, "w", encoding="utf-8") as f:
                # 使用 json.dump 写入 JSON 数据。
                # indent=4 使 JSON 输出格式化，易于阅读。
                # ensure_ascii=False 允许直接写入非 ASCII 字符（如中文）。
                # default=json_custom_serializer 使用自定义序列化器处理特殊对象。
                json.dump(data, f, indent=4, ensure_ascii=False, default=json_custom_serializer)
        else:
            # 如果文件扩展名不受支持，则抛出 ValueError。
            raise ValueError(f"不支持的产物文件扩展名: {file_extension}")

        # 将产物的相对路径存储到实验上下文中，以便后续追踪。
        relative_path = str(file_path.relative_to(get_project_root()))
        context.add_artifact(artifact_name, relative_path)
        logger.info(f"产物 '{artifact_name}' 已保存到: {file_path}")
        return str(file_path)  # 返回保存文件的完整路径

    except Exception as e:
        # 捕获保存过程中可能发生的任何异常，记录错误日志，并将错误信息添加到上下文的错误列表中。
        logger.error(f"保存产物 '{artifact_name}' 时出错: {e}")
        context.errors.append(f"保存产物 '{artifact_name}' 失败: {e}")
        return ""  # 保存失败时返回空字符串


def finalize_experiment_logging(context: "ExperimentContext") -> str:
    """
    将 ExperimentContext 中的所有信息序列化并保存到 JSON 文件。
    这将作为每次实验运行的核心记录。

    此函数负责在实验结束时，将 `ExperimentContext` 对象中收集的所有实验元数据、
    配置、结果和产物信息统一保存到一个名为 `experiment_metadata.json` 的文件中。
    这个文件是每次实验运行的完整摘要，对于后续分析和复现至关重要。

    参数:
        context (ExperimentContext): 实验上下文实例，包含所有需要保存的实验信息。

    返回:
        str: 保存的摘要文件的完整路径。如果保存失败，则返回空字符串。

    示例:
        >>> # 假设 context 是一个 ExperimentContext 实例，并且已经填充了实验数据
        >>> summary_file_path = finalize_experiment_logging(context)
        >>> print(f"实验摘要已保存到: {summary_file_path}")
    """
    logger = get_logger(__name__)  # 获取当前模块的日志记录器

    # 使用人类可读的目录名称。如果 context 对象中还没有这个属性，则调用函数生成。
    if not hasattr(context, "_experiment_dirname"):
        context._experiment_dirname = generate_experiment_dirname(context)

    # 构建实验运行目录的完整路径：项目根目录 / 'outputs' / 实验目录名。
    run_dir = get_project_root() / "outputs" / context._experiment_dirname
    # 创建运行目录。parents=True 确保创建所有缺失的父目录；exist_ok=True 避免目录已存在时报错。
    run_dir.mkdir(parents=True, exist_ok=True)

    # 定义摘要文件的完整路径。
    summary_path = run_dir / "experiment_metadata.json"

    try:
        # 以写入模式打开文件，并使用 UTF-8 编码。
        with open(summary_path, "w", encoding="utf-8") as f:
            # 将 context 对象的字典表示序列化为 JSON 格式并写入文件。
            # context.to_dict() 方法负责将 ExperimentContext 实例转换为可序列化的字典。
            # indent=4 使 JSON 输出格式化，易于阅读。
            # ensure_ascii=False 允许直接写入非 ASCII 字符（如中文）。
            # default=json_custom_serializer 用于处理字典中不可直接 JSON 序列化的对象。
            json.dump(context.to_dict(), f, indent=4, ensure_ascii=False, default=json_custom_serializer)

        logger.info(f"实验摘要已保存到: {summary_path}")

        # 将摘要文件作为产物添加到上下文中，记录其相对路径。
        relative_path = str(summary_path.relative_to(get_project_root()))
        context.add_artifact("run_summary", relative_path)

        return str(summary_path)  # 返回保存的摘要文件的完整路径

    except Exception as e:
        # 捕获保存过程中可能发生的任何异常，记录错误日志，并将错误信息添加到上下文的错误列表中。
        logger.error(f"保存实验摘要时出错: {e}")
        context.errors.append(f"保存 run_summary.json 失败: {e}")
        return ""  # 保存失败时返回空字符串


def save_detailed_results_csv(context: "ExperimentContext") -> str:
    """
    将详细的实验结果保存为 CSV 文件，其中每一行代表一次计算。

    此函数用于将 `ExperimentContext` 中收集的详细结果（通常是每次交叉验证折叠或每次独立运行的指标）
    保存到一个 CSV 文件中。如果实验是交叉验证且包含多条结果，它还会生成一个汇总统计的 CSV 文件。

    参数:
        context (ExperimentContext): 实验上下文实例，包含详细结果列表。

    返回:
        str: 保存的详细结果 CSV 文件的完整路径。如果保存失败或没有详细结果，则返回空字符串。

    示例:
        >>> # 假设 context 是一个 ExperimentContext 实例，并且 context.detailed_results 已填充
        >>> csv_file_path = save_detailed_results_csv(context)
        >>> print(f"详细结果已保存到: {csv_file_path}")
    """
    logger = get_logger(__name__)  # 获取当前模块的日志记录器

    if not context.detailed_results:
        # 如果没有详细结果可保存，则记录警告并返回空字符串。
        logger.warning("没有详细结果可保存")
        return ""

    # 使用人类可读的目录名称。如果 context 对象中还没有这个属性，则调用函数生成。
    if not hasattr(context, "_experiment_dirname"):
        context._experiment_dirname = generate_experiment_dirname(context)

    # 构建结果保存目录的完整路径：项目根目录 / 'outputs' / 实验目录名 / 'results'。
    results_dir = get_project_root() / "outputs" / context._experiment_dirname / "results"
    # 创建结果目录。parents=True 确保创建所有缺失的父目录；exist_ok=True 避免目录已存在时报错。
    results_dir.mkdir(parents=True, exist_ok=True)

    # 将详细结果列表转换为 pandas DataFrame 并定义 CSV 文件路径。
    df = pd.DataFrame(context.detailed_results)
    csv_path = results_dir / "detailed_results.csv"

    try:
        # 将 DataFrame 保存为 CSV 文件。
        # index=False 不保存 DataFrame 索引列。
        # encoding='utf-8' 确保支持中文等非 ASCII 字符。
        df.to_csv(csv_path, index=False, encoding="utf-8")
        logger.info(f"详细结果已保存到: {csv_path}")

        # 如果是交叉验证实验且结果数量大于 1，则保存一个汇总结果。
        if context.is_cross_validation and len(context.detailed_results) > 1:
            # 按模型名称、预处理方法和特征选择方法进行分组，并计算指定指标的均值和标准差。
            summary_df = (
                df.groupby(["model_name", "preprocessing_method", "feature_selection_method"])
                .agg(
                    {
                        "accuracy": ["mean", "std"],
                        "precision": ["mean", "std"],
                        "recall": ["mean", "std"],
                        "f1_score": ["mean", "std"],
                        "training_time_sec": ["mean", "std"],
                    }
                )
                .round(6)
            )  # 将结果四舍五入到小数点后六位

            # 扁平化多级列名，例如 ('accuracy', 'mean') 变为 'accuracy_mean'。
            summary_df.columns = ["_".join(col).strip() for col in summary_df.columns]
            # 定义汇总结果 CSV 文件的路径。
            summary_path = results_dir / "summary_results.csv"
            # 保存汇总 DataFrame 到 CSV 文件。
            summary_df.to_csv(summary_path, encoding="utf-8")
            logger.info(f"汇总结果已保存到: {summary_path}")

        return str(csv_path)  # 返回保存的详细结果 CSV 文件的完整路径

    except Exception as e:
        # 捕获保存过程中可能发生的任何异常，记录错误日志，并将错误信息添加到上下文的错误列表中。
        logger.error(f"保存详细结果 CSV 时出错: {e}")
        context.errors.append(f"保存详细结果 CSV 失败: {e}")
        return ""  # 保存失败时返回空字符串


def create_directory_structure(base_path: Union[str, Path], directories: list) -> None:
    """
    为项目创建指定的目录结构。

    此函数接收一个基础路径和一系列目录名称，然后在此基础路径下创建这些目录。
    它会确保所有必要的父目录都被创建，并且如果目录已经存在，则不会引发错误。

    参数:
        base_path (Union[str, Path]): 创建目录的基础路径。可以是字符串或 Path 对象。
        directories (list): 一个包含要创建的目录名称（字符串）的列表。
                            例如：`["data", "models", "outputs/results"]`。

    示例:
        >>> # 在当前工作目录下创建 'data' 和 'logs' 目录
        >>> create_directory_structure(".", ["data", "logs"])
        >>> # 在指定路径下创建嵌套目录
        >>> create_directory_structure("/tmp/my_project", ["raw_data", "processed_data", "models/v1"])
    """
    # 将基础路径转换为 Path 对象，以便使用 pathlib 的便利功能。
    base_path = Path(base_path)

    # 遍历要创建的每个目录名称。
    for directory in directories:
        # 构建完整的目标目录路径：基础路径 / 目录名称。
        dir_path = base_path / directory
        # 创建目录。
        # parents=True 确保创建所有缺失的父目录（例如，如果创建 "models/v1" 且 "models" 不存在，它也会被创建）。
        # exist_ok=True 意味着如果目录已经存在，则不会引发 FileExistsError 错误。
        dir_path.mkdir(parents=True, exist_ok=True)


def validate_config(config: Dict[str, Any]) -> None:
    """
    遗留的配置验证函数。

    注意: 此函数已弃用。请改用 Pydantic 配置模型进行更健壮和可维护的配置验证。

    此函数用于检查实验配置字典 `config` 是否包含所有必需的节和键，
    并对数据配置和模型配置进行基本的有效性检查。如果配置无效，则会抛出 `ValueError`。

    参数:
        config (Dict[str, Any]): 实验配置字典。

    抛出:
        ValueError: 如果配置无效或缺少必需的字段，则抛出此异常。

    示例:
        >>> valid_config = {
        ...     "data_config": {"source_type": "opensa_test_data", "type": "Cls"},
        ...     "preprocessing_config": {},
        ...     "feature_selection_config": {},
        ...     "model_config": {"type": "SVM", "name": "SVC"}
        ... }
        >>> validate_config(valid_config) # 不会抛出错误

        >>> invalid_config = {"data_config": {}}
        >>> validate_config(invalid_config) # 会抛出 ValueError: Missing required configuration section: preprocessing_config
    """
    logger = get_logger(__name__)  # 获取当前模块的日志记录器

    # 定义所有必需的配置节。
    required_sections = ["data_config", "preprocessing_config", "feature_selection_config", "model_config"]

    # 检查所有必需的配置节是否存在于 `config` 字典中。
    for section in required_sections:
        if section not in config:
            raise ValueError(f"缺少必需的配置节: {section}")

    # 验证数据配置 (data_config)
    data_config = config["data_config"]
    # 获取数据源类型，如果未指定则默认为 "opensa_test_data"。
    source_type = data_config.get("source_type", "opensa_test_data")

    if source_type == "opensa_test_data":
        # 如果数据源是 OpenSA 测试数据，则必须指定 'type'。
        if "type" not in data_config:
            raise ValueError("对于 OpenSA 测试数据，data_config 必须指定 'type'。")

        # 检查 'type' 的值是否为 "Cls"（分类）或 "Rgs"（回归）。
        if data_config["type"] not in ["Cls", "Rgs"]:
            raise ValueError("对于 OpenSA 测试数据，data_config 中的 'type' 必须是 'Cls' 或 'Rgs'。")

    elif source_type in ["file", "advanced_file"]:
        # 如果数据源是文件或高级文件模式，则必须指定 'file_path'。
        if "file_path" not in data_config:
            raise ValueError("对于基于文件的数据，data_config 必须指定 'file_path'。")

        # 对高级文件模式进行额外验证。
        if source_type == "advanced_file":
            # 定义高级文件模式所需的列名。
            required_cols = ["label_col_name", "spectrum_unique_id_col_name"]
            # 找出缺失的必需列。
            missing_cols = [col for col in required_cols if col not in data_config]
            if missing_cols:
                raise ValueError(f"advanced_file 数据源类型需要以下字段: {missing_cols}")
    else:
        # 如果数据源类型未被识别，则默认按 OpenSA 测试数据处理，并进行相应验证。
        if "type" not in data_config:
            raise ValueError("对于默认的 OpenSA 测试数据，data_config 必须指定 'type'。")

    # 验证模型配置 (model_config)
    model_config = config["model_config"]
    # 模型配置必须指定 'type' 和 'name'。
    if "type" not in model_config or "name" not in model_config:
        raise ValueError("model_config 必须指定 'type' 和 'name'。")

    logger.info("配置验证通过")  # 如果所有检查都通过，则记录配置验证成功。


import collections.abc

def deep_update(d, u):
    """
    Recursively update a dictionary.
    The d dictionary is updated in-place.
    """
    for k, v in u.items():
        if isinstance(v, collections.abc.Mapping):
            d[k] = deep_update(d.get(k, {}), v)
        else:
            d[k] = v
    return d


def set_global_seed(seed: int) -> None:
    """
    设置全局随机种子以确保实验的可复现性。

    这个函数统一设置 Python 内置 random 模块、NumPy 和 PyTorch 的随机种子，
    确保在相同的种子下，实验结果是完全可复现的。

    Args:
        seed: 随机种子值，建议使用整数

    设计理念：
    - 可复现性是科学研究的基石
    - 统一管理所有随机数生成器的种子
    - 支持深度学习和传统机器学习框架
    - 提供清晰的日志记录

    Example:
        >>> set_global_seed(42)
        >>> # 现在所有随机操作都是可复现的
    """
    logger = get_logger(__name__)

    try:
        # 设置 Python 内置 random 模块的种子
        import random
        random.seed(seed)
        logger.debug(f"设置 Python random 种子: {seed}")

        # 设置 NumPy 随机种子
        np.random.seed(seed)
        logger.debug(f"设置 NumPy random 种子: {seed}")

        # 设置 PyTorch 随机种子（如果可用）
        try:
            torch.manual_seed(seed)
            logger.debug(f"设置 PyTorch CPU 种子: {seed}")

            # 如果有 CUDA，也设置 CUDA 种子
            if torch.cuda.is_available():
                torch.cuda.manual_seed(seed)
                torch.cuda.manual_seed_all(seed)  # 多GPU情况
                logger.debug(f"设置 PyTorch CUDA 种子: {seed}")

                # 设置 CUDA 的确定性行为（可选，可能影响性能）
                torch.backends.cudnn.deterministic = True
                torch.backends.cudnn.benchmark = False
                logger.debug("启用 CUDA 确定性模式")

        except ImportError:
            logger.debug("PyTorch 不可用，跳过 PyTorch 种子设置")

        # 设置 scikit-learn 相关的随机种子（通过环境变量）
        os.environ['PYTHONHASHSEED'] = str(seed)
        logger.debug(f"设置 PYTHONHASHSEED: {seed}")

        logger.info(f"[SUCCESS] 全局随机种子已设置为: {seed}")

    except Exception as e:
        logger.error(f"设置全局种子时发生错误: {e}")
        raise


def get_environment_snapshot() -> Dict[str, Any]:
    """
    获取当前环境的详细快照，用于实验的可复现性记录。

    这个函数收集关键库的版本信息、系统信息和环境配置，
    帮助确保实验结果的可复现性和调试问题。

    Returns:
        Dict[str, Any]: 包含环境信息的字典

    设计理念：
    - 记录所有影响实验结果的关键信息
    - 提供足够的细节用于问题诊断
    - 自动处理库不可用的情况
    - 结构化的信息便于后续分析

    Example:
        >>> env_info = get_environment_snapshot()
        >>> print(f"Python 版本: {env_info['python']['version']}")
        >>> print(f"NumPy 版本: {env_info['packages']['numpy']}")
    """
    logger = get_logger(__name__)

    try:
        import platform
        import sys
        from datetime import datetime

        # 基础系统信息
        snapshot = {
            "timestamp": datetime.now().isoformat(),
            "python": {
                "version": sys.version,
                "version_info": {
                    "major": sys.version_info.major,
                    "minor": sys.version_info.minor,
                    "micro": sys.version_info.micro
                },
                "executable": sys.executable,
                "platform": sys.platform
            },
            "system": {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "architecture": platform.architecture()
            },
            "packages": {},
            "environment": {},
            "hardware": {}
        }

        # 关键包版本信息
        key_packages = [
            'numpy', 'pandas', 'scikit-learn', 'scipy', 'matplotlib',
            'seaborn', 'joblib', 'torch', 'torchvision', 'tensorflow',
            'xgboost', 'lightgbm', 'catboost', 'optuna', 'mlflow',
            'pydantic', 'fastapi', 'uvicorn'
        ]

        for package_name in key_packages:
            try:
                if package_name == 'scikit-learn':
                    import sklearn
                    snapshot["packages"][package_name] = sklearn.__version__
                elif package_name == 'torch':
                    import torch
                    snapshot["packages"][package_name] = torch.__version__
                elif package_name == 'torchvision':
                    import torchvision
                    snapshot["packages"][package_name] = torchvision.__version__
                elif package_name == 'tensorflow':
                    import tensorflow as tf
                    snapshot["packages"][package_name] = tf.__version__
                else:
                    module = __import__(package_name)
                    snapshot["packages"][package_name] = getattr(module, '__version__', 'unknown')
            except ImportError:
                snapshot["packages"][package_name] = "not_installed"
            except Exception as e:
                snapshot["packages"][package_name] = f"error: {str(e)}"

        # 环境变量（选择性记录）
        env_vars_to_record = [
            'PYTHONPATH', 'PYTHONHASHSEED', 'CUDA_VISIBLE_DEVICES',
            'OMP_NUM_THREADS', 'MKL_NUM_THREADS', 'OPENBLAS_NUM_THREADS'
        ]

        for var in env_vars_to_record:
            snapshot["environment"][var] = os.environ.get(var, "not_set")

        # 硬件信息
        try:
            import psutil
            snapshot["hardware"]["cpu_count"] = psutil.cpu_count()
            snapshot["hardware"]["cpu_count_logical"] = psutil.cpu_count(logical=True)
            memory = psutil.virtual_memory()
            snapshot["hardware"]["memory_total_gb"] = round(memory.total / (1024**3), 2)
            snapshot["hardware"]["memory_available_gb"] = round(memory.available / (1024**3), 2)
        except ImportError:
            snapshot["hardware"]["psutil"] = "not_installed"

        # GPU 信息
        try:
            if torch.cuda.is_available():
                snapshot["hardware"]["cuda_available"] = True
                snapshot["hardware"]["cuda_version"] = torch.version.cuda
                snapshot["hardware"]["gpu_count"] = torch.cuda.device_count()
                snapshot["hardware"]["gpu_names"] = [
                    torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())
                ]
            else:
                snapshot["hardware"]["cuda_available"] = False
        except:
            snapshot["hardware"]["cuda_info"] = "error_getting_cuda_info"

        logger.info("[SUCCESS] 环境快照获取成功")
        return snapshot

    except Exception as e:
        logger.error(f"获取环境快照时发生错误: {e}")
        # 返回基础信息，即使出错也不应该完全失败
        return {
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "python_version": sys.version,
            "platform": platform.platform() if 'platform' in locals() else "unknown"
        }
