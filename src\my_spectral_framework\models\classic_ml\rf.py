"""
Random Forest models for spectral analysis.

This module provides Random Forest implementations for classification tasks,
adapted from OpenSA's ClassicCls.py.

Author: txy
License: Apache-2.0
"""

from typing import Any, Dict, Optional, Union

import numpy as np
from numpy.typing import NDArray
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
from scipy.stats import randint

from ...core.utils import get_logger
from ...core.registry import register_model, register_model_params, register_model_hyperparams

logger = get_logger(__name__)





# 旧的训练和预测函数已被 SpectralModelWrapper 取代
# 这些函数的功能现在由统一的包装器提供，消除了代码重复


def get_feature_importance(model: Union[RandomForestClassifier, RandomForestRegressor]) -> NDArray[np.floating]:
    """
    Get feature importance from trained Random Forest model.

    Args:
        model: Trained Random Forest model

    Returns:
        Feature importance array
    """
    if hasattr(model, "feature_importances_"):
        return model.feature_importances_
    else:
        raise ValueError("Model does not have feature_importances_ attribute")


@register_model_params(name="RF", model_type="classification")
def get_rf_classifier_params() -> Dict[str, Any]:
    """
    Get default parameters for Random Forest classifier.

    Returns:
        Dictionary of default parameters
    """
    return {
        "n_estimators": 15,
        "max_depth": 3,
        "min_samples_split": 3,
        "min_samples_leaf": 3,
        "random_state": 42,
        "n_jobs": -1,
        "use_scaler": False,
    }


@register_model_params(name="RF", model_type="regression")
def get_rf_regressor_params() -> Dict[str, Any]:
    """
    Get default parameters for Random Forest regressor.

    Returns:
        Dictionary of default parameters
    """
    return {
        "n_estimators": 100,
        "max_depth": None,
        "min_samples_split": 2,
        "min_samples_leaf": 1,
        "random_state": 42,
        "n_jobs": -1,
        "use_scaler": False,
    }


def tune_rf_hyperparameters(X_train: NDArray[np.floating], y_train: NDArray[np.integer], model_type: str = "classifier") -> Dict[str, Any]:
    """
    Simple hyperparameter tuning for Random Forest models.

    Args:
        X_train: Training features
        y_train: Training targets
        model_type: "classifier" or "regressor"

    Returns:
        Dictionary of tuned parameters
    """
    from sklearn.model_selection import GridSearchCV

    param_grid = {
        "n_estimators": [50, 100, 200],
        "max_depth": [3, 5, 10, None],
        "min_samples_split": [2, 5, 10],
        "min_samples_leaf": [1, 2, 4],
    }

    if model_type == "classifier":
        model = RandomForestClassifier(random_state=42, n_jobs=-1)
        scoring = "accuracy"
    else:  # regressor
        model = RandomForestRegressor(random_state=42, n_jobs=-1)
        scoring = "r2"

    # Grid search
    grid_search = GridSearchCV(model, param_grid, cv=5, scoring=scoring, n_jobs=-1)
    grid_search.fit(X_train, y_train.ravel())

    logger.info(f"Best Random Forest {model_type} parameters: {grid_search.best_params_}")
    return grid_search.best_params_


# ===== 注册表驱动的工厂函数 =====
# 以下函数使用 @register_model 装饰器注册到框架的模型注册表中
# 这些工厂函数负责创建具体的模型实例，符合"完全由注册表驱动"的设计原则




@register_model(name="RF", model_type="classification")
def create_rf_classifier(**params) -> RandomForestClassifier:
    """
    创建 Random Forest 分类器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 RandomForestClassifier 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 RandomForestClassifier 的参数

    Returns:
        RandomForestClassifier: 配置好的随机森林分类器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("RF", "classification")
        >>> classifier = factory_func(n_estimators=100, random_state=42)
    """
    logger.info(f"创建 Random Forest 分类器，参数: {params}")
    return RandomForestClassifier(**params)


@register_model(name="RF", model_type="regression")
def create_rf_regressor(**params) -> RandomForestRegressor:
    """
    创建 Random Forest 回归器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 RandomForestRegressor 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 RandomForestRegressor 的参数

    Returns:
        RandomForestRegressor: 配置好的随机森林回归器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("RF", "regression")
        >>> regressor = factory_func(n_estimators=100, random_state=42)
    """
    logger.info(f"创建 Random Forest 回归器，参数: {params}")
    return RandomForestRegressor(**params)


# ============================================================================
# 超参数空间定义 - 实现模型"自包含"原则
# ============================================================================

@register_model_hyperparams("RF", "classification")
def get_rf_classification_hyperparams(strategy: str = "grid_search", complexity: str = "medium") -> Dict[str, Any]:
    """
    获取随机森林分类器的超参数搜索空间。

    这个函数定义了RF分类器的超参数优化空间，实现了模型"自包含"原则。
    超参数空间的定义权交还给模型自身，而不是集中在外部配置文件中。

    Args:
        strategy (str): 优化策略，"grid_search" 或 "random_search"
        complexity (str): 复杂度级别，"simple", "medium", 或 "comprehensive"

    Returns:
        Dict[str, Any]: 超参数搜索空间字典

    Example:
        >>> hyperparams = get_rf_classification_hyperparams("grid_search", "medium")
        >>> print(hyperparams["model__n_estimators"])
        [50, 100, 200]
    """
    if complexity == "simple":
        if strategy == "grid_search":
            return {
                "model__n_estimators": [50, 100],
                "model__max_depth": [10, None],
                "model__min_samples_split": [2, 5]
            }
        else:  # random_search
            return {
                "model__n_estimators": randint(50, 200),
                "model__max_depth": [5, 10, 15, 20, None],
                "model__min_samples_split": randint(2, 10),
            }

    elif complexity == "medium":
        if strategy == "grid_search":
            return {
                "model__n_estimators": [50, 100, 200],
                "model__max_depth": [10, 20, None],
                "model__min_samples_split": [2, 5],
                "model__min_samples_leaf": [1, 2],
            }
        else:  # random_search
            return {
                "model__n_estimators": randint(50, 300),
                "model__max_depth": [5, 10, 15, 20, 25, None],
                "model__min_samples_split": randint(2, 15),
                "model__min_samples_leaf": randint(1, 5),
            }

    else:  # comprehensive
        if strategy == "grid_search":
            return {
                "model__n_estimators": [50, 100, 200, 300],
                "model__max_depth": [5, 10, 15, 20, None],
                "model__min_samples_split": [2, 5, 10],
                "model__min_samples_leaf": [1, 2, 4],
                "model__max_features": ["sqrt", "log2", None],
            }
        else:  # random_search
            return {
                "model__n_estimators": randint(50, 500),
                "model__max_depth": [3, 5, 10, 15, 20, 25, None],
                "model__min_samples_split": randint(2, 20),
                "model__min_samples_leaf": randint(1, 10),
                "model__max_features": ["sqrt", "log2", None],
            }


@register_model_hyperparams("RF", "regression")
def get_rf_regression_hyperparams(strategy: str = "grid_search", complexity: str = "medium") -> Dict[str, Any]:
    """
    获取随机森林回归器的超参数搜索空间。

    回归任务的超参数空间与分类任务基本相同，但可能在某些参数上有细微差别。

    Args:
        strategy (str): 优化策略，"grid_search" 或 "random_search"
        complexity (str): 复杂度级别，"simple", "medium", 或 "comprehensive"

    Returns:
        Dict[str, Any]: 超参数搜索空间字典
    """
    # 对于RF，分类和回归的超参数空间基本相同
    return get_rf_classification_hyperparams(strategy, complexity)
