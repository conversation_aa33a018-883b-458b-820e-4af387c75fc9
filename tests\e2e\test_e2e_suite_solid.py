"""
SOLID原则驱动的端到端(E2E)测试套件
"""
import json
import subprocess
import sys
from pathlib import Path
import pytest
import pandas as pd

# 导入新的装饰器注册系统
from tests.core.test_registry import e2e_test

# --- Fixtures for E2E Tests ---
@pytest.fixture(scope="module")
def project_root():
    return Path(__file__).resolve().parent.parent.parent

@pytest.fixture(scope="module")
def e2e_config_files(project_root):
    """创建所有需要的E2E测试配置文件"""
    configs_to_create = {
        "sklearn_rf_single_run.py": """

EXPERIMENT_CONFIG = {
    "experiment_type": "single_run",
    "data_config": {"source_type": "opensa_test", "type": "Cls", "test_size": 0.3, "random_seed": 42},
    "preprocessing_config": {"steps": [{"method": "SNV"}]},
    "feature_selection_config": {"method": "Pca", "params": {"n_components": 5}},
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {"type": "Classification", "name": "RF", "params": {"n_estimators": 10, "max_depth": 3}}
    },
    "experiment_config": {"name": "e2e_sklearn_rf_test"},
    "evaluation_config": {}, "paths_config": {}
}
""",
        "pytorch_cnn_single_run.py": """
EXPERIMENT_CONFIG = {
    "experiment_type": "single_run",
    "data_config": {"source_type": "opensa_test", "type": "Cls", "test_size": 0.3, "random_seed": 42},
    "preprocessing_config": {"steps": [{"method": "SNV"}]},
    "feature_selection_config": {"method": "Pca", "params": {"n_components": 10}},
    "engine_config": {
        "engine": "pytorch",
        "pytorch_model_config": {"architecture": "CNN1D", "params": {"epochs": 2, "batch_size": 8, "device": "cpu"}}
    },
    "experiment_config": {"name": "e2e_pytorch_cnn_test"},
    "evaluation_config": {}, "paths_config": {}
}
""",
        "invalid_config.py": """
EXPERIMENT_CONFIG = {"invalid_key": "some_value"}
"""
    }

    config_dir = project_root / "conf" / "runs" / "e2e_generated_tests"
    config_dir.mkdir(exist_ok=True, parents=True)
    
    created_paths = {}
    for filename, content in configs_to_create.items():
        config_file = config_dir / filename
        with open(config_file, "w", encoding="utf-8") as f:
            f.write(content)
        created_paths[filename.split('.')[0]] = f"e2e_generated_tests/{filename.split('.')[0]}"

    yield created_paths

    # Cleanup
    import shutil
    if config_dir.exists():
        shutil.rmtree(config_dir)

# --- Test Class ---

class TestE2ESuite:
    """端到端测试套件"""

    @e2e_test(
        description="[E2E] 验证Sklearn单次运行工作流能成功执行并生成产物", 
        tags=["e2e", "cli", "sklearn", "workflow", "critical"], 
        priority="critical"
    )
    def test_cli_sklearn_single_run_workflow(self, project_root, e2e_config_files):
        """职责：从用户视角验证一个完整的Sklearn实验流程。"""
        config_name = e2e_config_files["sklearn_rf_single_run"]
        cmd = [sys.executable, "scripts/run_experiment.py", "--config", config_name]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, timeout=180)
        
        assert result.returncode == 0, f"Sklearn E2E测试失败: {result.stderr}"
        assert "实验成功完成!" in result.stdout

        # 验证产物（简化验证逻辑）
        outputs_dir = project_root / "outputs"
        if outputs_dir.exists():
            experiment_dirs = list(outputs_dir.glob("*e2e_sklearn_rf_test*"))
            if experiment_dirs:
                latest_dir = max(experiment_dirs, key=lambda p: p.stat().st_mtime)
                # 验证基本目录结构存在
                assert latest_dir.exists(), "实验输出目录应该存在"

    @e2e_test(
        description="[E2E] 验证PyTorch单次运行工作流能成功执行并生成产物", 
        tags=["e2e", "cli", "pytorch", "workflow", "critical"], 
        priority="critical"
    )
    def test_cli_pytorch_single_run_workflow(self, project_root, e2e_config_files):
        """职责：从用户视角验证一个完整的PyTorch实验流程。"""
        config_name = e2e_config_files["pytorch_cnn_single_run"]
        cmd = [sys.executable, "scripts/run_experiment.py", "--config", config_name]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, timeout=180)

        assert result.returncode == 0, f"PyTorch E2E测试失败: {result.stderr}"
        assert "实验成功完成!" in result.stdout
        assert "PytorchExecutionEngine" in result.stdout

    @e2e_test(
        description="[E2E] 验证CLI对无效配置文件能优雅地失败并提供错误信息", 
        tags=["e2e", "cli", "error_handling"], 
        priority="high"
    )
    def test_cli_handles_invalid_config_gracefully(self, project_root, e2e_config_files):
        """职责：验证用户输入错误时的框架鲁棒性。"""
        config_name = e2e_config_files["invalid_config"]
        cmd = [sys.executable, "scripts/run_experiment.py", "--config", config_name]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, timeout=60)

        assert result.returncode != 0, "使用无效配置的实验应该失败"
        output = result.stdout + result.stderr
        assert "配置验证失败" in output or "validation error" in output.lower()
        
    @e2e_test(
        description="[E2E] 验证CLI的--list-configs命令能正常工作", 
        tags=["e2e", "cli", "usability"], 
        priority="normal"
    )
    def test_cli_list_configs_command(self, project_root, e2e_config_files):
        """职责：验证CLI辅助功能的可用性。"""
        cmd = [sys.executable, "scripts/run_experiment.py", "--list-configs"]
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, timeout=30)

        assert result.returncode == 0
        # 检查我们刚刚创建的配置文件是否被列出
        for config_path in e2e_config_files.values():
            assert config_path in result.stdout
