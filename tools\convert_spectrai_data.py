"""
Convert spectrai .mat files to CSV format for testing the spectral framework.

This script loads all .mat files from spectrai test data and converts them
to a single CSV file that can be used with our framework.

Author: txy
License: Apache-2.0
"""

import argparse
import os
import sys
from pathlib import Path

import numpy as np
import pandas as pd
from scipy.io import loadmat

# 项目路径设置（现在通过包安装自动处理）
project_root = Path(__file__).parent.parent

# 临时添加 src 路径以确保导入工作（在包安装问题解决前）
src_path = project_root / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from my_spectral_framework.core.utils import get_logger, get_project_root

logger = get_logger(__name__)


def load_mat_file(file_path):
    """
    Load a .mat file and extract spectral data.

    Args:
        file_path: Path to the .mat file

    Returns:
        Spectral data as numpy array
    """
    try:
        mat_data = loadmat(file_path)

        # Print keys to understand structure
        print(f"Keys in {file_path}: {list(mat_data.keys())}")

        # Try to find the spectral data
        # Common keys might be 'data', 'spectrum', 'spectra', 'X', etc.
        data_keys = [k for k in mat_data.keys() if not k.startswith("__")]

        if len(data_keys) == 1:
            spectrum = mat_data[data_keys[0]]
        else:
            # Try common names
            for key in ["data", "spectrum", "spectra", "X", "y"]:
                if key in mat_data:
                    spectrum = mat_data[key]
                    break
            else:
                # Use the first non-metadata key
                spectrum = mat_data[data_keys[0]]

        # Ensure it's a 1D array
        if spectrum.ndim > 1:
            spectrum = spectrum.flatten()

        return spectrum

    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None


def convert_spectrai_data(input_dir, output_dir):
    """Convert spectrai test data to CSV format."""

    # Paths
    spectrai_data_path = Path(input_dir)
    output_path = get_project_root() / output_dir
    output_path.mkdir(parents=True, exist_ok=True)

    if not spectrai_data_path.exists():
        print(f"Error: spectrai data path not found: {spectrai_data_path}")
        return False

    print("Converting spectrai .mat files to CSV format...")

    all_spectra = []
    all_labels = []

    # Process class_1
    class_1_path = spectrai_data_path / "class_1"
    if class_1_path.exists():
        print(f"Processing class_1 files...")
        mat_files = list(class_1_path.glob("*.mat"))
        print(f"Found {len(mat_files)} files in class_1")

        # Load first file to check structure
        if mat_files:
            first_spectrum = load_mat_file(mat_files[0])
            if first_spectrum is not None:
                print(f"Spectrum shape: {first_spectrum.shape}")

        for i, mat_file in enumerate(mat_files[:50]):  # Limit to first 50 files for testing
            spectrum = load_mat_file(mat_file)
            if spectrum is not None:
                all_spectra.append(spectrum)
                all_labels.append(0)  # Class 1 = label 0

            if (i + 1) % 10 == 0:
                print(f"  Processed {i + 1} files from class_1")

    # Process class_2
    class_2_path = spectrai_data_path / "class_2"
    if class_2_path.exists():
        print(f"Processing class_2 files...")
        mat_files = list(class_2_path.glob("*.mat"))
        print(f"Found {len(mat_files)} files in class_2")

        for i, mat_file in enumerate(mat_files[:50]):  # Limit to first 50 files for testing
            spectrum = load_mat_file(mat_file)
            if spectrum is not None:
                all_spectra.append(spectrum)
                all_labels.append(1)  # Class 2 = label 1

            if (i + 1) % 10 == 0:
                print(f"  Processed {i + 1} files from class_2")

    if not all_spectra:
        print("Error: No spectral data loaded!")
        return False

    # Convert to numpy arrays
    print("Converting to numpy arrays...")
    X = np.array(all_spectra)
    y = np.array(all_labels)

    print(f"Final data shape: X={X.shape}, y={y.shape}")
    print(f"Class distribution: Class 0: {np.sum(y == 0)}, Class 1: {np.sum(y == 1)}")

    # Create DataFrame
    print("Creating DataFrame...")

    # Create column names for features
    feature_cols = [f"feature_{i}" for i in range(X.shape[1])]

    # Combine features and labels
    df = pd.DataFrame(X, columns=feature_cols)
    df["label"] = y

    # Save to CSV
    output_file = output_path / "spectrai_test_data.csv"
    print(f"Saving to {output_file}...")
    df.to_csv(output_file, index=False)

    print(f"✓ Successfully converted spectrai data to CSV!")
    print(f"  Output file: {output_file}")
    print(f"  Shape: {df.shape}")
    print(f"  Columns: {len(feature_cols)} features + 1 label")

    return True


def main():
    """Main function with argument parsing."""
    parser = argparse.ArgumentParser(description="Convert spectrai .mat files to CSV format.")
    parser.add_argument(
        "--input-dir",
        type=str,
        default=str(get_project_root().parent / "spectrai" / "data" / "test_classification_spectra"),
        help="Path to the input spectrai .mat data directory.",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="data/raw",
        help="Path to the output directory for CSV file (relative to project root).",
    )
    args = parser.parse_args()

    # Convert relative output path to absolute
    output_path = get_project_root() / args.output_dir
    output_path.mkdir(parents=True, exist_ok=True)

    success = convert_spectrai_data(args.input_dir, str(output_path))
    if success:
        print("\n🎉 Data conversion completed successfully!")
        print("You can now use this data with the spectral framework.")
    else:
        print("\n❌ Data conversion failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
