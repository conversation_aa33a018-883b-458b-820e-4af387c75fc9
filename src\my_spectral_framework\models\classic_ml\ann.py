"""
光谱分析的人工神经网络模型模块。

本模块为分类和回归任务提供 ANN 实现，改编自 OpenSA 的 ClassicCls.py 和 ClassicRgs.py。
设计理念：基于 sklearn 的 MLPClassifier 和 MLPRegressor，提供标准化的训练接口，
支持自动数据预处理和参数优化，确保模型的稳定性和可重现性。

Author: txy
License: Apache-2.0
"""

from typing import Any, Dict, Optional, Tuple, Union

import numpy as np
from numpy.typing import NDArray
from sklearn.metrics import accuracy_score
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.preprocessing import StandardScaler

from ...core.utils import get_logger
from ...core.registry import register_model, register_model_params

logger = get_logger(__name__)








# 旧的训练和预测函数已被 SpectralModelWrapper 取代
# 这些函数的功能现在由统一的包装器提供，消除了代码重复


@register_model_params(name="ANN", model_type="classification")
def get_ann_classifier_params() -> Dict[str, Any]:
    """
    Get default parameters for ANN classifier.

    Returns:
        Dictionary of default parameters
    """
    return {
        "hidden_layer_sizes": (10, 8),
        "activation": "relu",
        "solver": "lbfgs",
        "alpha": 1e-05,
        "max_iter": 200,
        "random_state": 1,
        "use_scaler": True,
    }


@register_model_params(name="ANN", model_type="regression")
def get_ann_regressor_params() -> Dict[str, Any]:
    """
    Get default parameters for ANN regressor.

    Returns:
        Dictionary of default parameters
    """
    return {
        "hidden_layer_sizes": (20, 20),
        "activation": "relu",
        "solver": "adam",
        "alpha": 0.0001,
        "max_iter": 400,
        "random_state": 1,
        "use_scaler": True,
    }


# ===== 注册表驱动的工厂函数 =====
# 以下函数使用 @register_model 装饰器注册到框架的模型注册表中
# 这些工厂函数负责创建具体的模型实例，符合"完全由注册表驱动"的设计原则




@register_model(name="ANN", model_type="classification")
def create_ann_classifier(**params) -> MLPClassifier:
    """
    创建 ANN 分类器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 MLPClassifier 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 MLPClassifier 的参数

    Returns:
        MLPClassifier: 配置好的多层感知机分类器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("ANN", "classification")
        >>> classifier = factory_func(hidden_layer_sizes=(100,), max_iter=500)
    """
    logger.info(f"创建 ANN 分类器，参数: {params}")
    return MLPClassifier(**params)


@register_model(name="ANN", model_type="regression")
def create_ann_regressor(**params) -> MLPRegressor:
    """
    创建 ANN 回归器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 MLPRegressor 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 MLPRegressor 的参数

    Returns:
        MLPRegressor: 配置好的多层感知机回归器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("ANN", "regression")
        >>> regressor = factory_func(hidden_layer_sizes=(20, 20), max_iter=400)
    """
    logger.info(f"创建 ANN 回归器，参数: {params}")
    return MLPRegressor(**params)
