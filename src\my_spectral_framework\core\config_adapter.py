"""
配置适配器模块。

提供统一的配置访问接口，隐藏 ExperimentRootConfig 和 dict 之间的差异，
为后续的训练日志、数据追踪和测试稳定性提供基础。

设计理念：
- 只读访问，不修改配置
- 统一接口，隐藏实现差异
- 安全默认值，避免 KeyError
- 最小依赖，不引入新的第三方库

Author: txy
License: Apache-2.0
"""
from typing import Any, Dict, List, Optional, Union

from .config_models import ExperimentRootConfig


class ConfigAdapter:
    """
    配置适配器类，提供统一的配置访问接口。
    
    支持 ExperimentRootConfig (Pydantic) 和 dict 两种配置格式，
    提供一致的只读访问方法。
    """
    
    def __init__(self, config: Union[ExperimentRootConfig, Dict[str, Any]]):
        """
        初始化配置适配器。
        
        Args:
            config: ExperimentRootConfig 对象或配置字典
        """
        self.config = config
        self._is_pydantic = isinstance(config, ExperimentRootConfig)
    
    def get_experiment_type(self) -> str:
        """获取实验类型。"""
        if self._is_pydantic:
            exp_type = self.config.experiment_type
            # 确保返回字符串值而不是枚举
            return exp_type.value if hasattr(exp_type, 'value') else str(exp_type)
        return self.config.get("experiment_type", "single_run")
    
    def get_engine_type(self) -> str:
        """获取引擎类型：'sklearn' 或 'pytorch'。"""
        if self._is_pydantic:
            engine_value = self.config.engine_config.engine
            # 确保返回字符串值而不是枚举
            return engine_value.value if hasattr(engine_value, 'value') else str(engine_value)

        engine_config = self.config.get("engine_config", {})
        return engine_config.get("engine", "sklearn")
    
    def get_task_type(self) -> str:
        """获取任务类型：'classification' 或 'regression'。"""
        if self._is_pydantic:
            engine_config = self.config.engine_config
            if hasattr(engine_config, 'ml_model_config'):
                # Sklearn 引擎
                return engine_config.ml_model_config.type.lower()
            elif hasattr(engine_config, 'pytorch_model_config'):
                # PyTorch 引擎
                return engine_config.pytorch_model_config.task_type.lower()
        
        # Dict 格式
        engine_config = self.config.get("engine_config", {})
        ml_model_config = engine_config.get("ml_model_config", {})
        pytorch_model_config = engine_config.get("pytorch_model_config", {})
        
        if ml_model_config:
            return ml_model_config.get("type", "classification").lower()
        elif pytorch_model_config:
            return pytorch_model_config.get("task_type", "classification").lower()
        
        return "classification"
    
    def get_model_name(self) -> str:
        """获取模型名称。"""
        if self._is_pydantic:
            engine_config = self.config.engine_config
            if hasattr(engine_config, 'ml_model_config'):
                return engine_config.ml_model_config.name
            elif hasattr(engine_config, 'pytorch_model_config'):
                return engine_config.pytorch_model_config.architecture
        
        # Dict 格式
        engine_config = self.config.get("engine_config", {})
        ml_model_config = engine_config.get("ml_model_config", {})
        pytorch_model_config = engine_config.get("pytorch_model_config", {})
        
        if ml_model_config:
            return ml_model_config.get("name", "unknown")
        elif pytorch_model_config:
            return pytorch_model_config.get("architecture", "unknown")
        
        return "unknown"
    
    def get_model_params(self) -> Dict[str, Any]:
        """获取模型参数。"""
        if self._is_pydantic:
            engine_config = self.config.engine_config
            if hasattr(engine_config, 'ml_model_config'):
                return engine_config.ml_model_config.params
            elif hasattr(engine_config, 'pytorch_model_config'):
                return engine_config.pytorch_model_config.params
        
        # Dict 格式
        engine_config = self.config.get("engine_config", {})
        ml_model_config = engine_config.get("ml_model_config", {})
        pytorch_model_config = engine_config.get("pytorch_model_config", {})
        
        if ml_model_config:
            return ml_model_config.get("params", {})
        elif pytorch_model_config:
            return pytorch_model_config.get("params", {})
        
        return {}
    
    def get_preprocessing_chain(self) -> str:
        """
        获取预处理链描述。
        
        Returns:
            预处理步骤链，如 "SNV->Pca" 或 "none"
        """
        if self._is_pydantic:
            steps = self.config.preprocessing_config.steps
        else:
            preprocessing_config = self.config.get("preprocessing_config", {})
            steps = preprocessing_config.get("steps", [])
        
        if not steps:
            return "none"
        
        # 提取方法名并连接
        methods = []
        for step in steps:
            if self._is_pydantic or isinstance(step, dict):
                method = step.method if self._is_pydantic else step.get("method", "unknown")
                methods.append(method)
        
        return "->".join(methods) if methods else "none"
    
    def get_cv_config(self) -> Optional[Dict[str, Any]]:
        """获取交叉验证配置。"""
        if self._is_pydantic:
            engine_config = self.config.engine_config
            cv_config = getattr(engine_config, 'cv_config', None)
            if cv_config:
                return {
                    "method": cv_config.method,
                    "n_splits": cv_config.n_splits,
                    "random_state": cv_config.random_state
                }
        else:
            engine_config = self.config.get("engine_config", {})
            cv_config = engine_config.get("cv_config")
            if cv_config:
                return {
                    "method": cv_config.get("method", "stratified"),
                    "n_splits": cv_config.get("n_splits", 5),
                    "random_state": cv_config.get("random_state", 42)
                }
        
        return None
    
    def get_hyperopt_config(self) -> Optional[Dict[str, Any]]:
        """获取超参数优化配置。"""
        if self._is_pydantic:
            engine_config = self.config.engine_config
            hyperopt_config = getattr(engine_config, 'hyperparameter_optimization_config', None)
            if hyperopt_config:
                return {
                    "strategy": hyperopt_config.strategy,
                    "cv_folds": hyperopt_config.cv_folds,
                    "scoring": hyperopt_config.scoring,
                    "param_grid": hyperopt_config.param_grid,
                    "param_space": getattr(hyperopt_config, 'param_space', None),
                    "n_trials": getattr(hyperopt_config, 'n_trials', 50),
                    "timeout": getattr(hyperopt_config, 'timeout', 3600)
                }
        else:
            engine_config = self.config.get("engine_config", {})
            hyperopt_config = engine_config.get("hyperparameter_optimization_config")
            if hyperopt_config:
                return {
                    "strategy": hyperopt_config.get("strategy", "grid_search"),
                    "cv_folds": hyperopt_config.get("cv_folds", 3),
                    "scoring": hyperopt_config.get("scoring", "accuracy"),
                    "param_grid": hyperopt_config.get("param_grid"),
                    "param_space": hyperopt_config.get("param_space"),
                    "n_trials": hyperopt_config.get("n_trials", 50),
                    "timeout": hyperopt_config.get("timeout", 3600)
                }
        
        return None
    
    def get_outputs_dir(self) -> str:
        """获取输出目录路径。"""
        if self._is_pydantic:
            paths_config = self.config.paths_config
            return getattr(paths_config, 'results_dir', None) or getattr(paths_config, 'outputs_dir', "outputs")
        
        # Dict 格式
        paths_config = self.config.get("paths_config", {})
        return paths_config.get("results_dir") or paths_config.get("outputs_dir", "outputs")
    
    def get_traceability_level(self) -> str:
        """获取可追溯性级别。"""
        if self._is_pydantic:
            traceability_config = getattr(self.config, 'traceability_config', None)
            if traceability_config:
                return traceability_config.level
        else:
            traceability_config = self.config.get("traceability_config", {})
            return traceability_config.get("level", "none")
        
        return "none"
