"""
数据源抽象基类模块。

定义了所有数据源策略必须遵循的统一接口。
"""

from abc import ABC, abstractmethod
from typing import Tuple, Optional, Dict, Any, List
import numpy as np
from ...core.utils import get_logger


class BaseDataSource(ABC):
    """
    所有数据源策略的抽象基类。
    
    设计理念：采用策略模式，为不同类型的数据源提供统一的接口，
    确保数据加载逻辑的解耦和可扩展性。
    """
    
    def __init__(self, config: Dict[str, Any], full_data_path: Optional[str] = None):
        """
        初始化数据源。
        
        Args:
            config: 完整的实验配置字典
            full_data_path: 完整的数据文件路径（对于文件类型的数据源）
        """
        self.data_config = config.get("data_config", {})
        self.paths_config = config.get("paths_config", {})
        self.full_data_path = full_data_path
        self.logger = get_logger(f"data_source.{self.__class__.__name__}")

    @abstractmethod
    def load(self) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray], List[str], Dict[str, Any]]:
        """
        加载数据并返回标准格式。

        Returns:
            元组 (X, y, groups, ids, metadata)：
            - X: 特征矩阵 (n_samples, n_features)
            - y: 目标向量 (n_samples,)
            - groups: 组标识符 (n_samples,) 或 None
            - ids: 光谱ID列表 (n_samples,)
            - metadata: 包含数据信息的字典
        """
        pass
