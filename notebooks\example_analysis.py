"""
Example Analysis Script

This script demonstrates how to properly set up imports and use the framework
from within the notebooks directory.

Author: txy
License: Apache-2.0
"""

# 项目路径设置（现在通过包安装自动处理）
import sys
from pathlib import Path

# Get project root (parent of notebooks directory)
project_root = Path(__file__).parent.parent

# 临时添加 src 路径以确保导入工作（在包安装问题解决前）
src_path = project_root / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

import matplotlib.pyplot as plt
import numpy as np

# Now we can import framework modules
import pandas as pd

from my_spectral_framework.core.utils import get_logger
from my_spectral_framework.data.factory import get_data_source

# Set up logging
logger = get_logger(__name__)


def analyze_experiment_results(experiment_dir: str):
    """
    Analyze results from a specific experiment directory.

    Args:
        experiment_dir: Path to experiment directory in outputs/
    """
    logger.info(f"Analyzing experiment: {experiment_dir}")

    # Load experiment summary
    outputs_dir = project_root / "outputs"
    exp_path = outputs_dir / experiment_dir

    if not exp_path.exists():
        logger.error(f"Experiment directory not found: {exp_path}")
        return

    # Load experiment summary
    summary_file = exp_path / "experiment_summary.json"
    if summary_file.exists():
        import json

        with open(summary_file, "r") as f:
            summary = json.load(f)

        print("=== Experiment Summary ===")
        print(f"Directory: {summary.get('experiment_info', {}).get('directory', 'Unknown')}")
        print(f"Duration: {summary.get('experiment_info', {}).get('duration_seconds', 'Unknown')} seconds")

        # Display metrics
        metrics = summary.get("metrics", {})
        if metrics:
            print("\n=== Metrics ===")
            for metric_name, metric_info in metrics.items():
                if isinstance(metric_info, dict) and "value" in metric_info:
                    print(f"{metric_name}: {metric_info['value']:.4f}")
                else:
                    print(f"{metric_name}: {metric_info}")

        # Display artifacts
        artifacts = summary.get("artifacts", [])
        if artifacts:
            print(f"\n=== Artifacts ({len(artifacts)}) ===")
            for artifact in artifacts:
                print(f"- {artifact['name']}: {artifact['path']}")

    # Load detailed results if available
    results_file = exp_path / "results" / "detailed_results.csv"
    if results_file.exists():
        df = pd.read_csv(results_file)
        print(f"\n=== Detailed Results ===")
        print(f"Shape: {df.shape}")
        print(df.head())

    # Load predictions if available
    predictions_file = exp_path / "predictions" / "test_predictions.csv"
    if predictions_file.exists():
        pred_df = pd.read_csv(predictions_file)
        print(f"\n=== Predictions ===")
        print(f"Shape: {pred_df.shape}")
        print(pred_df.head())

        # Simple visualization
        if "true" in pred_df.columns and "predicted" in pred_df.columns:
            plt.figure(figsize=(8, 6))
            plt.scatter(pred_df["true"], pred_df["predicted"], alpha=0.6)
            plt.plot(
                [pred_df["true"].min(), pred_df["true"].max()], [pred_df["true"].min(), pred_df["true"].max()], "r--", lw=2
            )
            plt.xlabel("True Values")
            plt.ylabel("Predicted Values")
            plt.title("Predictions vs True Values")
            plt.grid(True, alpha=0.3)

            # Save plot
            plot_path = exp_path / "analysis_plot.png"
            plt.savefig(plot_path, dpi=300, bbox_inches="tight")
            plt.show()
            logger.info(f"Plot saved to: {plot_path}")


def list_recent_experiments(n: int = 5):
    """
    List the most recent experiments.

    Args:
        n: Number of recent experiments to show
    """
    outputs_dir = project_root / "outputs"

    if not outputs_dir.exists():
        logger.warning("No outputs directory found")
        return []

    # Get all experiment directories
    exp_dirs = [d for d in outputs_dir.iterdir() if d.is_dir()]

    # Sort by modification time (most recent first)
    exp_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)

    print(f"=== Recent Experiments (Top {n}) ===")
    recent_experiments = []

    for i, exp_dir in enumerate(exp_dirs[:n]):
        print(f"{i+1}. {exp_dir.name}")
        recent_experiments.append(exp_dir.name)

    return recent_experiments


def main():
    """Main analysis function."""
    logger.info("Starting experiment analysis")

    # List recent experiments
    recent = list_recent_experiments()

    if recent:
        # Analyze the most recent experiment
        print(f"\nAnalyzing most recent experiment: {recent[0]}")
        analyze_experiment_results(recent[0])
    else:
        print("No experiments found. Run some experiments first!")

        # Demonstrate data loading
        print("\nDemonstrating data loading...")
        try:
            X, y = load_opensa_test_data("Cls")
            print(f"Loaded test data: X.shape={X.shape}, y.shape={y.shape}")
        except Exception as e:
            logger.error(f"Failed to load test data: {e}")


if __name__ == "__main__":
    main()
