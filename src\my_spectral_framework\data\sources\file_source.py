"""
文件数据源策略实现。

负责从文件加载光谱数据，支持高级文件和简单文件两种模式。
"""

from typing import Tuple, Optional, Dict, Any, List
import numpy as np
from .base import BaseDataSource
from ..loaders import load_and_process_data, load_spectral_data_from_file


class FileDataSource(BaseDataSource):
    """
    文件数据源策略。
    
    设计理念：封装文件数据的加载逻辑，支持高级文件（advanced_file）和
    简单文件（simple_file）两种加载模式，提供统一的数据源接口。
    """
    
    def load(self) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray], List[str], Dict[str, Any]]:
        """
        从文件加载光谱数据。

        Returns:
            元组 (X, y, groups, ids, metadata)
        """
        source_type = self.data_config.get("source_type")
        
        if source_type == "advanced_file":
            return self._load_advanced_file()
        else:  # simple_file or file
            return self._load_simple_file()
    
    def _load_advanced_file(self) -> <PERSON><PERSON>[np.ndarray, np.ndarray, Optional[np.ndarray], List[str], Dict[str, Any]]:
        """
        加载高级文件格式的数据。

        支持更多的配置选项，如波数范围过滤、编码优先级、NaN处理等。
        """
        self.logger.info("使用高级文件源加载数据...")

        return load_and_process_data(
            data_path=self.full_data_path,
            label_col_name=self.data_config["label_col_name"],
            spectrum_unique_id_col_name=self.data_config.get("spectrum_unique_id_col_name"),
            group_col_name=self.data_config.get("group_col_name"),
            wave_number_range=self.data_config.get("wave_number_range"),
            encoding_priority=self.data_config.get("encoding_priority", ["utf-8"]),
            nan_fill_value_spectra=self.data_config.get("nan_fill_value_spectra", 0.0),
            nan_handling_key_cols=self.data_config.get("nan_handling_key_cols", "remove_row"),
            spectrum_id_strategy=self.data_config.get("spectrum_id_strategy", "auto_generate"),
        )
    
    def _load_simple_file(self) -> Tuple[np.ndarray, np.ndarray, Optional[np.ndarray], List[str], Dict[str, Any]]:
        """
        加载简单文件格式的数据。

        使用基本的文件加载功能，适用于标准格式的光谱数据文件。
        """
        self.logger.info("使用简单文件源加载数据...")

        X, y, metadata = load_spectral_data_from_file(
            file_path=self.full_data_path,
            has_header=self.data_config.get("has_header", True),
            label_col=self.data_config.get("label_col", "last"),
            delimiter=self.data_config.get("delimiter", ","),
            wavelength_row=self.data_config.get("wavelength_row", False),
        )

        # 简单文件没有分组信息，但需要生成ID
        groups = None

        # 为简单文件生成ID
        spectrum_id_strategy = self.data_config.get("spectrum_id_strategy", "auto_generate")
        if spectrum_id_strategy == "auto_generate":
            num_samples = X.shape[0]
            ids = [f"auto_spec_{i+1:04d}" for i in range(num_samples)]
            self.logger.info(f"为简单文件自动生成 {len(ids)} 个光谱ID。")
        else:
            # 简单文件不支持从列读取ID，强制使用自动生成
            num_samples = X.shape[0]
            ids = [f"auto_spec_{i+1:04d}" for i in range(num_samples)]
            self.logger.warning("简单文件不支持从列读取ID，已自动生成ID。")

        return X, y, groups, ids, metadata
