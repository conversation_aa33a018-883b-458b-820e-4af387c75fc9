"""
实验上下文管理模块，用于光谱分析框架。

此模块提供了ExperimentContext类，作为存储和管理所有实验相关信息、数据和结果的中心枢纽。
此设计便于未来的MLOps集成。

作者: txy
许可证: Apache-2.0
"""

import copy
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np


class ExperimentContext:
    """
    实验执行的中央上下文管理器。

    此类存储所有实验相关信息，包括配置、各处理阶段的数据、模型实例和评估结果。
    它作为未来MLOps集成的“钩子”。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        使用配置初始化实验上下文。

        参数：
            config: 实验配置字典
        """
        # 深拷贝配置以确保原始配置不被修改
        self.config = copy.deepcopy(config)
        # 使用UUID确保全局唯一性
        self.run_id = str(uuid.uuid4())
        # 记录实验开始时间
        self.start_time = datetime.now().isoformat()
        # 记录实验结束时间（初始为空）
        self.end_time: Optional[str] = None
        # 用于存储实验运行目录的名称，以便在多个函数中重用和保持一致性
        self._experiment_dirname: Optional[str] = None

        # 各阶段详细元数据存储
        self.data_metadata: Dict[str, Any] = {}
        self.split_metadata: Dict[str, Any] = {}
        self.preprocessing_metadata: Dict[str, Any] = {}
        self.feature_selection_metadata: Dict[str, Any] = {}
        self.model_metadata: Dict[str, Any] = {}
        self.evaluation_results: Dict[str, Any] = {}
        self.artifacts: Dict[str, str] = {}  # artifact_name -> file_path
        self.errors: List[str] = []

        # 支持交叉验证
        self.is_cross_validation: bool = False
        self.fold_results: List[Dict[str, Any]] = []

        # CSV导出详细结果（每行 = 一次计算）
        self.detailed_results: List[Dict[str, Any]] = []

        # 高级交叉验证分组
        self.groups: Optional[np.ndarray] = None

        # 兼容性支持（用于向后兼容）
        self.experiment_id = self.run_id
        self.data_dimensions: Dict[str, Any] = {}
        self.processing_steps: List[str] = []
        self.model_parameters: Optional[Dict[str, Any]] = None
        self.execution_time: Optional[float] = None
        self.model_path: Optional[str] = None
        self.results_path: Optional[str] = None

    def add_data_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        添加关于加载数据的元数据。

        参数：
            metadata: 数据元数据字典
        """
        # 更新数据元数据并记录处理步骤
        self.data_metadata.update(metadata)
        self.processing_steps.append("data_loaded")

    def add_split_metadata(
        self, train_shape: Tuple[int, int], test_shape: Tuple[int, int], split_method: str, test_size: float, random_seed: int
    ) -> None:
        """
        添加关于数据分割的元数据。

        参数：
            train_shape: 训练集形状
            test_shape: 测试集形状
            split_method: 数据分割方法
            test_size: 测试集比例
            random_seed: 随机种子
        """
        # 存储分割元数据并记录处理步骤
        self.split_metadata = {
            "train_shape": train_shape,
            "test_shape": test_shape,
            "split_method": split_method,
            "test_size": test_size,
            "random_seed": random_seed,
        }
        self.processing_steps.append("data_split")

    def add_preprocessing_metadata(
        self, method: str, params: Dict[str, Any], initial_shape: Tuple[int, int], final_shape: Tuple[int, int]
    ) -> None:
        """
        添加关于数据预处理的元数据。

        参数：
            method: 预处理方法名称
            params: 预处理参数字典
            initial_shape: 数据初始形状
            final_shape: 数据最终形状
        """
        # 存储预处理元数据并记录处理步骤
        self.preprocessing_metadata = {
            "method": method,
            "params": params,
            "initial_shape": initial_shape,
            "final_shape": final_shape,
        }
        self.processing_steps.append(f"preprocessing_{method}")

    def add_feature_selection_metadata(
        self,
        method: str,
        params: Dict[str, Any],
        initial_shape: Tuple[int, int],
        final_shape: Tuple[int, int],
        selected_indices: Optional[List[int]] = None,
    ) -> None:
        """
        添加关于特征选择的元数据。

        参数：
            method: 特征选择方法名称
            params: 特征选择参数字典
            initial_shape: 数据初始形状
            final_shape: 数据最终形状
            selected_indices: 被选择的特征索引列表（可选）
        """
        # 存储特征选择元数据并记录处理步骤
        self.feature_selection_metadata = {
            "method": method,
            "params": params,
            "initial_shape": initial_shape,
            "final_shape": final_shape,
            "selected_indices": selected_indices,
        }
        self.processing_steps.append(f"feature_selection_{method}")

    def add_model_metadata(
        self, model_name: str, model_type: str, params: Dict[str, Any], training_duration_sec: Optional[float] = None
    ) -> None:
        """
        添加关于模型训练的元数据。

        参数：
            model_name: 模型名称
            model_type: 模型类型（例如分类器或回归器）
            params: 模型参数字典
            training_duration_sec: 模型训练时长（秒，可选）
        """
        # 存储模型元数据并记录处理步骤
        self.model_metadata = {
            "name": model_name,
            "type": model_type,
            "params": params,
            "training_duration_sec": training_duration_sec,
        }
        self.model_parameters = params  # 向后兼容
        self.processing_steps.append("model_training")

    def add_evaluation_results(self, results: Dict[str, Any]) -> None:
        """
        添加评估结果。

        参数：
            results: 评估结果字典
        """
        # 更新评估结果并记录处理步骤
        self.evaluation_results.update(results)
        self.processing_steps.append("evaluation")

    def add_artifact(self, name: str, path: str) -> None:
        """
        添加实验生成的工件文件路径。

        参数：
            name: 工件名称
            path: 工件文件路径
        """
        # 存储工件路径
        self.artifacts[name] = path

    def add_fold_result(self, fold_data: Dict[str, Any]) -> None:
        """
        添加交叉验证中单折的结果。

        参数：
            fold_data: 单折结果数据字典
        """
        # 标记为交叉验证并存储折结果
        self.is_cross_validation = True
        self.fold_results.append(fold_data)

    def add_detailed_result(self, result_data: Dict[str, Any]) -> None:
        """
        添加详细结果记录（最终CSV中的一行）。
        每次调用代表一次完整计算（模型 + 预处理 + 折）。

        参数：
            result_data: 单次计算的完整结果数据
        """
        # 添加通用实验元数据
        base_data = {
            "experiment_timestamp": self.start_time,
            "experiment_name": self.config.get("experiment_config", {}).get("name", "unknown"),
            "experiment_type": self.config.get("experiment_type", "single_run"),
            "run_id": self.run_id,
        }

        # 与具体结果数据合并
        complete_result = {**base_data, **result_data}
        self.detailed_results.append(complete_result)

    def finalize_experiment(self) -> None:
        """
        标记实验完成并计算执行时间。
        """
        # 记录实验结束时间并计算总执行时间
        self.end_time = datetime.now().isoformat()
        start_dt = datetime.fromisoformat(self.start_time)
        end_dt = datetime.fromisoformat(self.end_time)
        self.execution_time = (end_dt - start_dt).total_seconds()
        self.processing_steps.append("experiment_completed")

    def to_dict(self) -> Dict[str, Any]:
        """
        将上下文转换为可序列化的字典以进行JSON存储。

        返回：
            包含所有实验信息的字典
        """
        # 如果实验尚未完成，则先完成实验
        if self.end_time is None:
            self.finalize_experiment()

        summary = {
            "run_id": self.run_id,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "total_duration_sec": self.execution_time,
            "config": self.config,
            "data_metadata": self.data_metadata,
            "split_metadata": self.split_metadata,
            "preprocessing_metadata": self.preprocessing_metadata,
            "feature_selection_metadata": self.feature_selection_metadata,
            "model_metadata": self.model_metadata,
            "evaluation_results": self.evaluation_results,
            "artifacts": self.artifacts,
            "is_cross_validation": self.is_cross_validation,
            "fold_results": self.fold_results,
            "errors": self.errors,
            "processing_steps": self.processing_steps,
        }
        return summary

    # 向后兼容性支持的方法
    def set_raw_data(self, X: np.ndarray, y: np.ndarray) -> None:
        """存储原始数据（向后兼容）。"""
        metadata = {"n_samples": X.shape[0], "n_features": X.shape[1], "feature_shape": X.shape, "target_shape": y.shape}
        self.add_data_metadata(metadata)
        self.data_dimensions["raw"] = metadata

    def set_split_data(self, X_train: np.ndarray, X_test: np.ndarray, y_train: np.ndarray, y_test: np.ndarray) -> None:
        """存储分割数据（向后兼容）。"""
        self.add_split_metadata(
            X_train.shape,
            X_test.shape,
            self.config.get("data_config", {}).get("split_method", "unknown"),
            self.config.get("data_config", {}).get("test_size", 0.2),
            self.config.get("data_config", {}).get("random_seed", 42),
        )

        # 更新遗留的data_dimensions
        self.data_dimensions["split"] = {
            "train_samples": X_train.shape[0],
            "test_samples": X_test.shape[0],
            "train_features": X_train.shape[1],
            "test_features": X_test.shape[1],
            "split_method": self.split_metadata["split_method"],
            "test_ratio": self.split_metadata["test_size"],
        }

    def set_processed_data(self, X_train: np.ndarray, X_test: np.ndarray, processing_method: str) -> None:
        """存储处理后数据（向后兼容）。"""
        self.add_preprocessing_metadata(processing_method, {}, X_train.shape, X_train.shape)
        self.data_dimensions["processed"] = {
            "train_features": X_train.shape[1],
            "test_features": X_test.shape[1],
            "processing_method": processing_method,
        }

    def set_feature_selected_data(
        self, X_train: np.ndarray, X_test: np.ndarray, selection_method: str, selected_indices: Optional[np.ndarray] = None
    ) -> None:
        """存储特征选择后数据（向后兼容）。"""
        indices_list = selected_indices.tolist() if selected_indices is not None else None
        self.add_feature_selection_metadata(selection_method, {}, X_train.shape, X_train.shape, indices_list)
        self.data_dimensions["feature_selected"] = {
            "train_features": X_train.shape[1],
            "test_features": X_test.shape[1],
            "selection_method": selection_method,
            "selected_indices": indices_list,
        }

    def set_model(self, model: Any, model_parameters: Dict[str, Any]) -> None:
        """存储模型（向后兼容）。"""
        self.add_model_metadata("unknown", "unknown", model_parameters)

    def set_predictions(self, predictions: np.ndarray) -> None:
        """存储预测结果（向后兼容）。"""
        self.processing_steps.append("prediction")

    def set_evaluation_metrics(self, metrics: Dict[str, float]) -> None:
        """存储评估指标（向后兼容）。"""
        self.add_evaluation_results(metrics)
        self.evaluation_metrics = copy.deepcopy(metrics)  # 直接访问的兼容性

    def set_model_path(self, path: str) -> None:
        """存储模型路径（向后兼容）。"""
        self.model_path = path
        self.add_artifact("model", path)

    def set_results_path(self, path: str) -> None:
        """存储结果路径（向后兼容）。"""
        self.results_path = path
        self.add_artifact("results", path)

    def get_summary(self) -> Dict[str, Any]:
        """获取摘要（向后兼容）。"""
        return self.to_dict()

    def get_data_info(self) -> Dict[str, Any]:
        """获取数据相关信息以供结果报告使用。"""
        return {
            "data_type": self.config["data_config"]["type"],
            "original_features": self.data_dimensions.get("raw", {}).get("n_features", "Unknown"),
            "final_features": self.data_dimensions.get("feature_selected", {}).get(
                "train_features", self.data_dimensions.get("processed", {}).get("train_features", "Unknown")
            ),
            "train_samples": self.data_dimensions.get("split", {}).get("train_samples", "Unknown"),
            "test_samples": self.data_dimensions.get("split", {}).get("test_samples", "Unknown"),
            "preprocessing": self.data_dimensions.get("processed", {}).get("processing_method", "None"),
            "feature_selection": self.data_dimensions.get("feature_selected", {}).get("selection_method", "None"),
        }

    def add_hyperopt_results(self, results: Dict[str, Any]) -> None:
        """添加超参数优化结果。"""
        self.hyperopt_results = results
        self.processing_steps.append("hyperparameter_optimization")

    def add_cv_summary(self, summary: Dict[str, Any]) -> None:
        """添加交叉验证摘要。"""
        self.cv_summary = summary
        self.processing_steps.append("cross_validation")
