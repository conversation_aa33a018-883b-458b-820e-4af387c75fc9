"""
产物解析器模块。

本模块实现了产物解析器，用于从注册表中查询产物信息并从存储后端加载数据。
这是可追溯性基础设施的核心组件，提供了统一的产物访问接口。

设计理念：
1. 统一接口：提供简单易用的产物加载 API
2. 自动发现：自动查找和连接存储后端和注册表
3. 错误处理：提供详细的错误信息和异常处理
4. 缓存优化：支持产物缓存以提高访问性能


License: Apache-2.0
"""

from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from ..core.utils import get_logger
from ..storage import LocalStorageBackend, StorageError
from ..registry import SQLiteArtifactRegistry, RegistryError

logger = get_logger(__name__)


class ArtifactResolver:
    """
    产物解析器。
    
    这个类负责协调存储后端和注册表，提供统一的产物查询和加载接口。
    它自动处理产物的定位、格式识别和数据加载过程。
    """
    
    def __init__(
        self,
        storage_backend: Optional[LocalStorageBackend] = None,
        registry_backend: Optional[SQLiteArtifactRegistry] = None,
        auto_discover: bool = True
    ):
        """
        初始化产物解析器。
        
        Args:
            storage_backend: 存储后端实例，如果为 None 则自动发现
            registry_backend: 注册表后端实例，如果为 None 则自动发现
            auto_discover: 是否自动发现后端
        """
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        
        if auto_discover and (storage_backend is None or registry_backend is None):
            storage_backend, registry_backend = self._auto_discover_backends()
        
        self.storage = storage_backend
        self.registry = registry_backend
        
        if self.storage is None or self.registry is None:
            raise ValueError("无法初始化存储后端或注册表后端")
        
        self.logger.info(f"ArtifactResolver 初始化完成")
        self.logger.info(f"存储后端: {type(self.storage).__name__}")
        self.logger.info(f"注册表后端: {type(self.registry).__name__}")
    
    def resolve(self, run_id: str, artifact_name: str) -> Any:
        """
        解析并加载指定的产物。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            Any: 加载的产物数据
            
        Raises:
            ArtifactNotFoundError: 当产物不存在时
            StorageError: 当存储操作失败时
            RegistryError: 当注册表操作失败时
        """
        try:
            self.logger.info(f"开始解析产物: {run_id}/{artifact_name}")
            
            # 1. 从注册表获取产物元数据
            metadata = self.registry.get_artifact_metadata(run_id, artifact_name)
            if metadata is None:
                raise ArtifactNotFoundError(f"产物不存在: {run_id}/{artifact_name}")
            
            uri = metadata["uri"]
            format = metadata["format"]
            
            self.logger.info(f"找到产物元数据: {uri} ({format})")
            
            # 2. 从存储后端加载数据
            data = self.storage.load(uri, format)
            
            self.logger.info(f"成功加载产物: {run_id}/{artifact_name}")
            return data
            
        except Exception as e:
            error_msg = f"解析产物失败: {run_id}/{artifact_name}, 错误: {e}"
            self.logger.error(error_msg)
            raise
    
    def list_artifacts(self, run_id: str) -> List[Dict[str, Any]]:
        """
        列出指定实验运行的所有产物。
        
        Args:
            run_id: 实验运行 ID
            
        Returns:
            List[Dict[str, Any]]: 产物信息列表
        """
        try:
            return self.registry.list_artifacts(run_id)
        except Exception as e:
            error_msg = f"列出产物失败: {run_id}, 错误: {e}"
            self.logger.error(error_msg)
            raise
    
    def list_runs(self) -> List[str]:
        """
        列出所有实验运行 ID。
        
        Returns:
            List[str]: 实验运行 ID 列表
        """
        try:
            return self.registry.list_runs()
        except Exception as e:
            error_msg = f"列出实验运行失败: {e}"
            self.logger.error(error_msg)
            raise
    
    def get_artifact_metadata(self, run_id: str, artifact_name: str) -> Optional[Dict[str, Any]]:
        """
        获取产物的元数据信息。
        
        Args:
            run_id: 实验运行 ID
            artifact_name: 产物名称
            
        Returns:
            Optional[Dict[str, Any]]: 产物元数据，如果不存在则返回 None
        """
        try:
            return self.registry.get_artifact_metadata(run_id, artifact_name)
        except Exception as e:
            error_msg = f"获取产物元数据失败: {run_id}/{artifact_name}, 错误: {e}"
            self.logger.error(error_msg)
            raise
    
    def search_artifacts(
        self,
        name_pattern: Optional[str] = None,
        format: Optional[str] = None,
        run_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        搜索符合条件的产物。
        
        Args:
            name_pattern: 产物名称模式（支持通配符）
            format: 产物格式
            run_id: 实验运行 ID
            
        Returns:
            List[Dict[str, Any]]: 符合条件的产物信息列表
        """
        try:
            # 使用注册表的搜索功能
            results = self.registry.search_artifacts(
                name_pattern=name_pattern,
                format=format
            )
            
            # 如果指定了 run_id，进一步过滤
            if run_id:
                results = [r for r in results if r.get("run_id") == run_id]
            
            return results
            
        except Exception as e:
            error_msg = f"搜索产物失败: {e}"
            self.logger.error(error_msg)
            raise
    
    def _auto_discover_backends(self) -> tuple:
        """
        自动发现存储后端和注册表后端。
        
        Returns:
            tuple: (storage_backend, registry_backend)
        """
        self.logger.info("开始自动发现后端...")
        
        # 查找最近的实验目录
        current_dir = Path.cwd()
        outputs_dir = current_dir / "outputs"
        
        storage_backend = None
        registry_backend = None
        
        if outputs_dir.exists():
            # 查找最新的实验目录
            experiment_dirs = [d for d in outputs_dir.iterdir() if d.is_dir()]
            if experiment_dirs:
                # 按修改时间排序，选择最新的
                latest_dir = max(experiment_dirs, key=lambda d: d.stat().st_mtime)
                
                self.logger.info(f"发现实验目录: {latest_dir}")
                
                # 创建存储后端
                storage_backend = LocalStorageBackend(base_path=latest_dir)
                
                # 查找注册表数据库
                registry_db = latest_dir / "artifacts_registry.db"
                if registry_db.exists():
                    registry_backend = SQLiteArtifactRegistry(db_path=str(registry_db))
                    self.logger.info(f"发现注册表数据库: {registry_db}")
                else:
                    self.logger.warning(f"未找到注册表数据库: {registry_db}")
        
        if storage_backend is None:
            # 回退到当前目录
            self.logger.info("回退到当前目录作为存储后端")
            storage_backend = LocalStorageBackend(base_path=current_dir)
        
        if registry_backend is None:
            # 创建默认注册表
            default_registry_path = current_dir / "artifacts_registry.db"
            registry_backend = SQLiteArtifactRegistry(db_path=str(default_registry_path))
            self.logger.info(f"创建默认注册表: {default_registry_path}")
        
        return storage_backend, registry_backend


class ArtifactNotFoundError(Exception):
    """产物不存在异常。"""
    pass


# 为了向后兼容，提供一些异常的别名
from ..registry.base import ArtifactNotFoundError as RegistryArtifactNotFoundError
from ..storage.base import StorageNotFoundError

# 统一异常处理
ArtifactNotFoundError = RegistryArtifactNotFoundError
