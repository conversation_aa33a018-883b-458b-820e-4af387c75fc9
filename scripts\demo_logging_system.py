#!/usr/bin/env python3
"""
专业级日志系统演示脚本

展示新的SOLID原则驱动的日志系统的强大功能：
1. 集中配置管理
2. 分级日志记录
3. 多处理器支持（控制台+文件）
4. 命名日志记录器支持分模块调控
5. 可轮转的文件日志

Author: txy
License: Apache-2.0
"""

import logging
import logging.config
import sys
from pathlib import Path

import yaml

# 项目路径设置（现在通过包安装自动处理）
project_root = Path(__file__).parent.parent

from my_spectral_framework.core.utils import get_logger


def setup_logging():
    """从YAML文件加载日志配置。"""
    config_path = Path("conf/logging_config.yaml")
    if config_path.exists():
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            # 确保logs目录存在
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            # 应用配置
            logging.config.dictConfig(config)
            logging.info("日志系统已从 'conf/logging_config.yaml' 加载配置。")
        except Exception as e:
            # 如果配置文件有问题，则使用基础配置
            logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
            logging.error(f"加载日志配置失败: {e}，已切换到基础配置。")
    else:
        # 如果配置文件不存在，则使用一个基础配置
        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
        logging.warning("警告: 未找到 'conf/logging_config.yaml'，已使用基础日志配置。")


def demo_module_specific_logging():
    """演示分模块的日志控制"""
    print("\n" + "=" * 60)
    print("🎯 演示1: 分模块日志控制")
    print("=" * 60)

    # 获取不同模块的记录器
    preprocessing_logger = get_logger("my_spectral_framework.preprocessing")
    data_logger = get_logger("my_spectral_framework.data")
    models_logger = get_logger("my_spectral_framework.models.deep_learning")

    print("📝 预处理模块日志 (DEBUG级别):")
    preprocessing_logger.debug("这是预处理模块的DEBUG信息 - 详细的算法步骤")
    preprocessing_logger.info("SNV预处理完成，处理了1000个光谱")
    preprocessing_logger.warning("检测到3个异常光谱，已自动处理")

    print("\n📊 数据模块日志 (INFO级别):")
    data_logger.debug("这条DEBUG信息不会在控制台显示")
    data_logger.info("数据加载完成: 1000个样本, 1024个特征")
    data_logger.warning("数据集中发现缺失值，已使用均值填充")

    print("\n🧠 深度学习模块日志 (INFO级别):")
    models_logger.debug("这条DEBUG信息不会在控制台显示")
    models_logger.info("开始训练CNN模型，Epoch 1/10")
    models_logger.info("训练损失: 0.234, 验证准确率: 0.876")


def demo_log_levels():
    """演示不同日志级别"""
    print("\n" + "=" * 60)
    print("📊 演示2: 日志级别分层")
    print("=" * 60)

    logger = get_logger("demo.log_levels")

    print("🔍 所有级别的日志消息:")
    logger.debug("DEBUG: 详细的调试信息 - 变量值、函数调用等")
    logger.info("INFO: 一般信息 - 程序正常运行状态")
    logger.warning("WARNING: 警告信息 - 可能的问题，但程序继续运行")
    logger.error("ERROR: 错误信息 - 程序遇到错误，但可以继续")
    logger.critical("CRITICAL: 严重错误 - 程序可能无法继续运行")

    print("\n💡 注意: DEBUG级别的消息只会出现在日志文件中，不会在控制台显示")


def demo_file_logging():
    """演示文件日志功能"""
    print("\n" + "=" * 60)
    print("📁 演示3: 文件日志系统")
    print("=" * 60)

    logger = get_logger("demo.file_logging")

    print("📝 正在记录大量日志到文件...")
    for i in range(10):
        logger.debug(f"详细调试信息 #{i+1}: 处理第{i+1}个数据批次")
        logger.info(f"批次 {i+1}/10 处理完成")
        if i % 3 == 0:
            logger.warning(f"批次 {i+1} 处理时间较长: {0.5 + i*0.1:.2f}秒")

    logger.error("模拟一个错误: 内存使用率过高 (85%)")
    logger.critical("模拟严重错误: 磁盘空间不足")

    print(f"✅ 所有日志已记录到文件: logs/debug.log")
    print("💡 文件中包含详细的DEBUG信息，控制台只显示重要信息")


def demo_configuration_flexibility():
    """演示配置灵活性"""
    print("\n" + "=" * 60)
    print("⚙️ 演示4: 配置系统的灵活性")
    print("=" * 60)

    print("🎛️ 当前日志配置特性:")
    print("  ✅ 控制台处理器: INFO级别及以上")
    print("  ✅ 文件处理器: DEBUG级别及以上")
    print("  ✅ 预处理模块: DEBUG级别 (最详细)")
    print("  ✅ 深度学习模块: INFO级别")
    print("  ✅ 其他模块: WARNING级别 (默认)")
    print("  ✅ 文件轮转: 5MB/文件, 最多5个备份")
    print("  ✅ UTF-8编码支持中文")

    print("\n🔧 要调整日志行为，只需编辑 conf/logging_config.yaml:")
    print("  • 想看更多细节? 将控制台级别改为DEBUG")
    print("  • 嫌控制台太吵? 将控制台级别改为WARNING")
    print("  • 想关闭某个模块的详细日志? 调整该模块的级别")
    print("  • 想改变日志格式? 修改formatters部分")
    print("  • 无需重启程序，配置立即生效!")


def show_log_file_content():
    """显示日志文件内容摘要"""
    print("\n" + "=" * 60)
    print("📄 日志文件内容摘要")
    print("=" * 60)

    log_file = Path("logs/debug.log")
    if log_file.exists():
        try:
            with open(log_file, "r", encoding="utf-8") as f:
                lines = f.readlines()

            print(f"📊 日志文件统计:")
            print(f"  • 文件路径: {log_file}")
            print(f"  • 总行数: {len(lines)}")
            print(f"  • 文件大小: {log_file.stat().st_size} 字节")

            if lines:
                print(f"\n📝 最新的5条日志:")
                for line in lines[-5:]:
                    print(f"  {line.strip()}")

        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("❌ 日志文件不存在")


def main():
    """主演示函数"""
    print("🎉 专业级日志系统演示")
    print("=" * 60)
    print("基于SOLID原则的'逻辑与配置分离'设计")
    print("支持集中配置、分级管理、多模块精细调控")

    # 设置日志系统
    setup_logging()

    # 运行演示
    demo_module_specific_logging()
    demo_log_levels()
    demo_file_logging()
    demo_configuration_flexibility()
    show_log_file_content()

    print("\n" + "=" * 60)
    print("🎯 演示完成!")
    print("=" * 60)
    print("💡 提示:")
    print("  1. 查看 logs/debug.log 文件可以看到所有详细日志")
    print("  2. 编辑 conf/logging_config.yaml 可以调整日志行为")
    print("  3. 在实际项目中，每个模块都可以独立调控日志级别")
    print("  4. 这个系统完全遵循SOLID原则，易于扩展和维护")


if __name__ == "__main__":
    main()
