# conf/model_params/cnn.py
"""
卷积神经网络（CNN）的模型参数预设。

这些预设提供了适用于不同场景（如快速CPU测试、稳健GPU训练）的参数组合。
"""

# 用于在CPU上进行快速测试
FAST_PARAMS = {
    "epochs": 15,
    "batch_size": 32,
    "learning_rate": 0.001,
    "device": "cpu",
    "n_filters_1": 8,
    "n_filters_2": 16,
    "kernel_size_1": 3,
    "kernel_size_2": 3,
    "dropout_rate": 0.2,
    "random_state": 42
}

# 用于在GPU上进行稳健训练
ROBUST_PARAMS = {
    "n_filters_1": 32,
    "n_filters_2": 64,
    "n_filters_3": 128,
    "kernel_size_1": 5,
    "kernel_size_2": 3,
    "kernel_size_3": 3,
    "dropout_rate": 0.3,
    "epochs": 100,
    "batch_size": 16,
    "learning_rate": 0.001,
    "device": "cuda",
    "random_state": 42
}

# 默认参数（均衡配置）
DEFAULT_PARAMS = {
    "epochs": 50,
    "batch_size": 32,
    "learning_rate": 0.001,
    "device": "cpu",
    "n_filters_1": 16,
    "n_filters_2": 32,
    "kernel_size_1": 3,
    "kernel_size_2": 3,
    "dropout_rate": 0.2
}
