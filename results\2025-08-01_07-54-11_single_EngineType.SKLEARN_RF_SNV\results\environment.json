{"timestamp": "2025-08-01T07:54:11.960474", "python": {"version": "3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]", "version_info": {"major": 3, "minor": 11, "micro": 9}, "executable": "E:\\Python\\Envs\\cu128\\Scripts\\python.exe", "platform": "win32"}, "system": {"platform": "Windows-10-10.0.26100-SP0", "system": "Windows", "release": "10", "version": "10.0.26100", "machine": "AMD64", "processor": "Intel64 Family 6 Model 183 Stepping 1, GenuineIntel", "architecture": ["64bit", "WindowsPE"]}, "packages": {"numpy": "2.2.6", "pandas": "2.3.1", "scikit-learn": "1.7.0", "scipy": "1.16.0", "matplotlib": "3.10.3", "seaborn": "0.13.2", "joblib": "1.5.1", "torch": "2.9.0.dev20250714+cu128", "torchvision": "0.24.0.dev20250715+cu128", "tensorflow": "not_installed", "xgboost": "3.0.2", "lightgbm": "4.6.0", "catboost": "not_installed", "optuna": "4.4.0", "mlflow": "3.1.1", "pydantic": "2.11.7", "fastapi": "0.116.1", "uvicorn": "0.35.0"}, "environment": {"PYTHONPATH": "not_set", "PYTHONHASHSEED": "42", "CUDA_VISIBLE_DEVICES": "not_set", "OMP_NUM_THREADS": "not_set", "MKL_NUM_THREADS": "not_set", "OPENBLAS_NUM_THREADS": "not_set"}, "hardware": {"cpu_count": 20, "cpu_count_logical": 20, "memory_total_gb": 31.84, "memory_available_gb": 6.06, "cuda_available": true, "cuda_version": "12.8", "gpu_count": 1, "gpu_names": ["NVIDIA GeForce RTX 5070 Ti"]}}