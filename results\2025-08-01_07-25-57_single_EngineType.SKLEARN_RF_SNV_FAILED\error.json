{"error_type": "StorageError", "error_message": "保存产物 'final_model' 失败: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer", "error_traceback": "Traceback (most recent call last):\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\storage\\local.py\", line 119, in save\n    self._format_handlers[format](data, file_path)\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\storage\\local.py\", line 289, in _save_joblib\n    joblib.dump(data, file_path)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 600, in dump\n    NumpyPickler(f, protocol=protocol).dump(value)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 487, in dump\n    self.save(obj)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 603, in save\n    self.save_reduce(obj=obj, *rv)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 717, in save_reduce\n    save(state)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 972, in save_dict\n    self._batch_setitems(obj.items())\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 998, in _batch_setitems\n    save(v)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 932, in save_list\n    self._batch_appends(obj)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 956, in _batch_appends\n    save(x)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 887, in save_tuple\n    save(element)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 603, in save\n    self.save_reduce(obj=obj, *rv)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 687, in save_reduce\n    save(cls)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 1129, in save_type\n    return self.save_global(obj)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 1076, in save_global\n    raise PicklingError(\n_pickle.PicklingError: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\runners.py\", line 96, in run\n    results = evaluator.evaluate(model_pipeline, X_train, y_train, groups_train, ids_train)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\evaluation\\evaluators.py\", line 97, in evaluate\n    self.handler.log_artifact(\"final_model\", model, \"joblib\", subdir=\"models\")\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\reporting\\handlers.py\", line 276, in log_artifact\n    uri = self.storage.save(\n          ^^^^^^^^^^^^^^^^^^\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\storage\\local.py\", line 137, in save\n    raise StorageError(error_msg) from e\nmy_spectral_framework.storage.base.StorageError: 保存产物 'final_model' 失败: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer\n", "failure_time": "2025-08-01T07:26:00.478742", "run_id": "00c3a8b4-697f-4fb0-823b-537fe7a838fd", "experiment_directory": "results\\2025-08-01_07-25-57_single_EngineType.SKLEARN_RF_SNV", "failed_directory": "results\\2025-08-01_07-25-57_single_EngineType.SKLEARN_RF_SNV_FAILED", "config": {"experiment_type": "single_run", "data_config": {"source_type": "opensa_test", "spectrum_id_strategy": "auto_generate", "type": "Cls", "file_path": null, "label_col_name": "label", "group_col_name": null, "spectrum_unique_id_col_name": "spectrum_id", "wave_number_range": null, "test_size": 0.2, "random_seed": 42}, "preprocessing_config": {"steps": [{"method": "SNV", "params": {}}]}, "feature_selection_config": {"method": "Pca", "params": {"n_components": 20}}, "experiment_config": {"name": "sklearn_rf_cls_example", "description": "Sklearn随机森林分类示例", "tags": ["sklearn", "classification", "random_forest", "example"], "result_handler": "filesystem"}, "evaluation_config": {"metrics": {"classification": ["accuracy", "precision", "recall", "f1"], "regression": ["rmse", "r2", "mae"]}, "save_predictions": true, "save_model": true}, "paths_config": {"data_dir": "data", "models_dir": "models", "results_dir": "results", "logs_dir": "logs"}, "traceability_config": {"level": "none"}, "engine_config": {"engine": "sklearn", "ml_model_config": {"type": "Classification", "name": "RF", "params": {"n_estimators": 100, "max_depth": 5, "min_samples_split": 2, "min_samples_leaf": 1, "random_state": 42}}, "cv_config": null, "hyperparameter_optimization_config": null}}, "context_summary": {"data_dimensions": {}, "processing_steps": ["evaluation"], "evaluation_results": {"accuracy": 0.967741935483871, "precision": 0.9713261648745519, "recall": 0.967741935483871, "f1_score": 0.9676154332700824, "confusion_matrix": [[14, 0, 0, 0], [0, 16, 0, 0], [0, 0, 16, 0], [0, 0, 2, 14]], "precision_class_0.0": 1.0, "recall_class_0.0": 1.0, "f1_class_0.0": 1.0, "precision_class_1.0": 1.0, "recall_class_1.0": 1.0, "f1_class_1.0": 1.0, "precision_class_2.0": 0.8888888888888888, "recall_class_2.0": 1.0, "f1_class_2.0": 0.9411764705882353, "precision_class_3.0": 1.0, "recall_class_3.0": 0.875, "f1_class_3.0": 0.9333333333333333}, "fold_results": [], "errors": ["保存产物 'final_model' 失败: 保存产物 'final_model' 失败: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer"], "is_cross_validation": false}, "logged_metrics": {}, "logged_parameters": {}, "logged_artifacts": [{"name": "environment", "path": "E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\results\\2025-08-01_07-25-57_single_EngineType.SKLEARN_RF_SNV\\results\\environment.json", "format": "json", "timestamp": "2025-08-01T07:26:00.288645", "metadata": {"type": "environment_snapshot"}}]}