# src/my_spectral_framework/optimization/tuners.py

import time
from abc import ABC, abstractmethod
from typing import Any, Dict
from ..core.utils import get_logger

from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from ..core.config_models import ExperimentRootConfig
from ..core.registry import get_model_hyperparams_function

# 导入 Optuna 相关组件
try:
    import optuna
    from optuna.integration import OptunaSearchCV
    from optuna.distributions import (
        FloatDistribution,
        IntDistribution,
        CategoricalDistribution,
    )
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

logger = get_logger(__name__)

class BaseTuner(ABC):
    """所有超参数优化策略的抽象基类。"""
    def __init__(self, model: Any, config: ExperimentRootConfig, cv_splitter: Any, groups: Any = None):
        self.model = model
        self.config = config
        self.cv_splitter = cv_splitter
        self.groups = groups
        self.hyperopt_config = self.config.engine_config.hyperparameter_optimization_config

    def _get_param_space(self) -> Dict[str, Any]:
        """
        从注册表获取模型的超参数搜索空间。

        这个方法实现了优化器与具体参数空间实现的解耦，使其能够处理任何
        通过注册表提供了超参数空间的模型。

        Returns:
            Dict[str, Any]: 超参数搜索空间字典

        Raises:
            KeyError: 如果模型的超参数空间未在注册表中注册
        """
        # 从配置中获取模型信息
        engine_cfg = self.config.engine_config
        if engine_cfg.engine == "sklearn":
            model_cfg = engine_cfg.ml_model_config
            model_name = model_cfg.name
            model_type = model_cfg.type.value.lower()
        elif engine_cfg.engine == "pytorch":
            model_cfg = engine_cfg.pytorch_model_config
            model_name = model_cfg.architecture
            model_type = model_cfg.task_type.value.lower()
        else:
            raise ValueError(f"不支持的引擎类型: {engine_cfg.engine}")

        # 从注册表获取超参数空间函数
        hyperparams_func = get_model_hyperparams_function(model_name, model_type)

        # 获取优化策略和复杂度
        strategy = self.hyperopt_config.strategy
        complexity = getattr(self.hyperopt_config, 'complexity', 'medium')

        # 调用函数获取参数空间
        return hyperparams_func(strategy=strategy, complexity=complexity)

    @abstractmethod
    def tune(self, X, y) -> Any:
        """执行调优过程并返回已训练的搜索对象 (e.g., GridSearchCV instance)。"""
        pass

class GridSearchTuner(BaseTuner):
    """使用 GridSearchCV 进行调优的策略。"""
    def tune(self, X, y) -> Any:
        # 🚀 从注册表获取参数空间，而不是使用硬编码的配置
        param_grid = self._get_param_space()

        search = GridSearchCV(
            self.model,
            param_grid=param_grid,
            cv=self.cv_splitter,
            scoring=self.hyperopt_config.scoring,
            n_jobs=-1,
            verbose=1
        )
        search.fit(X, y, groups=self.groups)
        return search

class RandomSearchTuner(BaseTuner):
    """使用 RandomizedSearchCV 进行调优的策略。"""
    def tune(self, X, y) -> Any:
        # 🚀 从注册表获取参数空间，而不是使用硬编码的配置
        param_distributions = self._get_param_space()

        search = RandomizedSearchCV(
            self.model,
            param_distributions=param_distributions,
            n_iter=self.hyperopt_config.n_trials,  # 使用 n_trials 而不是 n_iter
            cv=self.cv_splitter,
            scoring=self.hyperopt_config.scoring,
            n_jobs=-1,
            verbose=1,
            random_state=self.config.data_config.random_seed
        )
        search.fit(X, y, groups=self.groups)
        return search

class OptunaTuner(BaseTuner):
    """使用 OptunaSearchCV 进行调优的策略。"""
    def tune(self, X, y) -> Any:
        if not OPTUNA_AVAILABLE:
            raise ImportError("Optuna 未安装。请运行 'pip install optuna'。")

        # 🚀 从注册表获取参数空间
        param_space = self._get_param_space()
        param_distributions = self._create_optuna_param_distribution(param_space)
        progress_callback = OptunaProgressCallback(self.hyperopt_config.n_trials, logger)

        search = OptunaSearchCV(
            self.model,
            param_distributions=param_distributions,
            n_trials=self.hyperopt_config.n_trials,
            timeout=self.hyperopt_config.timeout,
            cv=self.cv_splitter,
            scoring=self.hyperopt_config.scoring,
            random_state=self.config.data_config.random_seed,
            verbose=0,
            n_jobs=-1,
            callbacks=[progress_callback]
        )
        search.fit(X, y, groups=self.groups)
        return search

    def _create_optuna_param_distribution(self, param_space: Dict[str, Any]) -> Dict[str, Any]:
        distributions = {}
        for name, config in param_space.items():
            dist_type = config.get("type")
            if dist_type == "categorical":
                distributions[name] = CategoricalDistribution(config["choices"])
            elif dist_type == "int":
                distributions[name] = IntDistribution(config["low"], config["high"], step=config.get("step", 1))
            elif dist_type == "float":
                distributions[name] = FloatDistribution(config["low"], config["high"], log=config.get("log", False))
            else:
                raise ValueError(f"不支持的Optuna分布类型: '{dist_type}'")
        return distributions


class OptunaProgressCallback:
    """一个功能更强大且线程安全的回调类，提供精确计时和参数信息"""
    def __init__(self, n_trials, logger):
        self.n_trials = n_trials
        self.logger = logger
        # 🔻 移除: 不再需要手动计时，使用 Optuna 提供的精确时间
        from threading import Lock
        self._lock = Lock()

    def __call__(self, study, trial):
        with self._lock:
            state = trial.state

        if state == optuna.trial.TrialState.COMPLETE:
            # 🔧 修复: 使用 trial 对象自带的精确时间
            if trial.datetime_complete and trial.datetime_start:
                duration = (trial.datetime_complete - trial.datetime_start).total_seconds()
            else:
                duration = 0.0  # 备用方案

            best_score = study.best_value if study.best_value is not None else float('nan')

            # ✨ 增强: 格式化参数以便阅读，移除 model__ 前缀使其更简洁
            params_str = ", ".join(f"{key.replace('model__', '')}={value}" for key, value in trial.params.items())

            self.logger.info(f"✅ Trial {trial.number + 1}/{self.n_trials} 完成 | "
                             f"耗时: {duration:.1f}s | "
                             f"得分: {trial.value:.4f} | "
                             f"当前最佳: {best_score:.4f}")
            # ✨ 增强: 在下一行打印参数，让用户看到哪种参数组合表现更好
            self.logger.info(f"   📋 参数: {params_str}")

        elif state == optuna.trial.TrialState.PRUNED:
            self.logger.info(f"✂️ Trial {trial.number + 1}/{self.n_trials} 被剪枝")
        elif state == optuna.trial.TrialState.FAIL:
            self.logger.warning(f"❌ Trial {trial.number + 1}/{self.n_trials} 失败")


def create_tuner(strategy: str, model: Any, config: ExperimentRootConfig, cv_splitter: Any, groups: Any = None) -> BaseTuner:
    """根据策略名称创建Tuner实例的工厂函数。

    Args:
        strategy: 优化策略名称 ('grid_search', 'random_search', 'optuna')
        model: 要调优的模型
        config: 实验配置对象
        cv_splitter: 交叉验证分割器
        groups: 分组信息（可选）

    Returns:
        配置好的Tuner实例

    Raises:
        ValueError: 如果策略名称不受支持
    """
    strategy = strategy.lower()
    tuner_map = {
        "grid_search": GridSearchTuner,
        "random_search": RandomSearchTuner,
        "optuna": OptunaTuner
    }
    if strategy in tuner_map:
        tuner_class = tuner_map[strategy]
        return tuner_class(model, config, cv_splitter, groups)
    else:
        raise ValueError(f"不支持的优化策略: {strategy}. 可选项: {list(tuner_map.keys())}")
