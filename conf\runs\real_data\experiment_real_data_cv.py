"""
真实数据端到端交叉验证实验配置。

此配置展示了一个完整的真实世界场景实验，包括：
- 从 `data/real/spectral_data.csv` 加载真实光谱数据。
- 使用高级文件加载器并正确映射列。
- 采用分层分组K折交叉验证（Stratified Group K-Fold）以防止数据泄漏。
- 使用随机森林分类器。
"""

EXPERIMENT_CONFIG = {
    # 实验类型：交叉验证，用于更稳健的评估
    "experiment_type": "cross_validation",
    
    # 数据配置：使用高级文件加载器
    "data_config": {
        "source_type": "advanced_file",
        "file_path": "real/spectral_data.csv",  # 路径相对于 data/ 目录
        "type": "Cls",
        
        # 根据真实数据结构进行列映射
        "label_col_name": "main_sample_name",
        "spectrum_unique_id_col_name": "spectrum_index",
        "group_col_name": "sample_id",  # 关键：按 sample_id 分组以防泄漏
        
        # 数据处理选项
        "encoding_priority": ["utf-8", "gbk", "latin-1"],
        "nan_handling_key_cols": "remove_row",
        "nan_fill_value_spectra": 0.0,
        
        # "wave_number_range": [400.0, 4000.0],  # 可选：取消注释以筛选波长
        
        "test_size": 0.2,
        "random_seed": 42
    },
    
    # 预处理流程：针对真实光谱数据进行优化
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}},
            # {"method": "SG", "params": {"w": 11, "p": 2}}  # 可选：Savitzky-Golay平滑
        ]
    },
    
    # 特征选择：处理高维数据
    "feature_selection_config": {
        "method": "Pca",
        "params": {"n_components": 50}
    },
    
    # 引擎配置
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {
            "type": "Classification",
            "name": "RF",
            "params": {
                "n_estimators": 100,
                "max_depth": 10,
                "min_samples_split": 5,
                "min_samples_leaf": 2,
                "random_state": 42,
                "n_jobs": -1  # 使用所有CPU核心
            }
        },
        
        # 关键：使用分层分组K折交叉验证以防数据泄漏
        "cv_config": {
            "method": "stratified_group",
            "n_splits": 5,
            "random_state": 42
        }
    },
    
    # 实验元数据
    "experiment_config": {
        "name": "real_spectral_data_rf_group_cv",
        "description": "使用随机森林和分层分组K折交叉验证，对真实光谱数据进行端到端测试",
        "tags": ["real_data", "e2e_test", "random_forest", "stratified_group_kfold"]
    },
    
    # 评估配置
    "evaluation_config": {
        "metrics": {
            "classification": ["accuracy", "precision", "recall", "f1", "confusion_matrix"]
        },
        "save_predictions": True,
        "save_model": True
    },
    
    # 路径配置
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models", 
        "results_dir": "results",
        "logs_dir": "logs"
    }
}
