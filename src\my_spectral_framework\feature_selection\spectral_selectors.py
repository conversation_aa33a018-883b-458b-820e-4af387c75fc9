"""
光谱分析框架的光谱特征选择函数模块。

本模块包含从 OpenSA 改编的各种特征选择和降维方法，包括 LARS、UVE、CARS、SPA、PCA 和 GA。
设计理念：提供统一的 sklearn 兼容接口，支持管道化处理，确保特征选择方法的可重用性和可扩展性。

Author: txy
License: Apache-2.0
"""

import copy
from typing import Any, Dict, Optional, Tuple, Union

import numpy as np
from numpy.linalg import matrix_rank as rank
from scipy.linalg import inv, pinv, qr
from sklearn import linear_model
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.cross_decomposition import PLSRegression
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import KFold, ShuffleSplit, cross_val_score, train_test_split
from sklearn.utils import shuffle

from ..core.utils import get_logger
from ..core.registry import register_feature_selection, get_feature_selection_function

logger = get_logger(__name__)


def apply_lars(X: np.ndarray, y: np.ndarray, nums: int = 40) -> np.ndarray:
    """
    [内部辅助函数] 执行LARS (Least Angle Regression) 特征选择。
    此函数主要由 LarsSelector 包装器调用。外部不应直接调用此函数。

    Args:
        X: 特征矩阵，形状 (n_samples, n_features)
        y: 目标向量，形状 (n_samples,)
        nums: 要选择的特征数量

    Returns:
        选定特征的索引数组
    """
    lars = linear_model.Lars()
    lars.fit(X, y)
    coef_list = np.abs(lars.coef_)

    coef = np.asarray(coef_list)
    spectrum_list = coef.argsort()[-1 : -(nums + 1) : -1]
    spectrum_list = np.sort(spectrum_list)

    logger.info(f"LARS selected {len(spectrum_list)} features")
    return spectrum_list


class UVESelector:
    """
    [内部辅助类] 用于执行UVE (Uninformative Variable Elimination) 特征选择。
    此工具类主要由 UveSelector 包装器调用。外部不应直接实例化或调用此工具类。
    """

    def __init__(self, X: np.ndarray, y: np.ndarray, ncomp: int = 1, nrep: int = 500, test_size: float = 0.2):
        """
        初始化 UVE 选择器。

        Args:
            X: 特征矩阵
            y: 目标向量
            ncomp: 组件数量
            nrep: 重复次数
            test_size: PLS 的测试集大小
        """
        self.x = X
        self.y = y
        self.ncomp = min([ncomp, rank(X)])
        self.nrep = nrep
        self.test_size = test_size
        self.criteria = None
        self.feature_index = None
        self.feature_r2 = np.full(self.x.shape[1], np.nan)
        self.sel_feature = None

    def calc_criteria(self):
        """计算 UVE 准则。"""
        pls_coef = np.zeros((self.nrep, self.x.shape[1]))
        ss = ShuffleSplit(n_splits=self.nrep, test_size=self.test_size)
        step = 0

        for train, test in ss.split(self.x, self.y):
            x_train = self.x[train, :]
            y_train = self.y[train]
            pls_model = PLSRegression(min([self.ncomp, rank(x_train)]))
            pls_model.fit(x_train, y_train)
            pls_coef[step, :] = pls_model.coef_.flatten()
            step += 1

        mean_coef = np.mean(pls_coef, axis=0)
        std_coef = np.std(pls_coef, axis=0)
        self.criteria = mean_coef / std_coef

    def eval_criteria(self, cv: int = 3):
        """使用交叉验证评估准则。"""
        self.feature_index = np.argsort(-np.abs(self.criteria))

        for i in range(self.x.shape[1]):
            xi = self.x[:, self.feature_index[: i + 1]]
            if i < self.ncomp:
                reg_model = LinearRegression()
            else:
                reg_model = PLSRegression(min([self.ncomp, rank(xi)]))

            cv_score = cross_val_score(reg_model, xi, self.y, cv=cv)
            self.feature_r2[i] = np.mean(cv_score)

    def cut_feature(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于最优数量切割特征。

        Args:
            X: 输入特征矩阵

        Returns:
            元组 (选定特征, 选定索引)
        """
        cut_i = np.argmax(self.feature_r2)
        self.sel_feature = self.feature_index[: cut_i + 1]

        if X.shape[1] == self.x.shape[1]:
            return X[:, self.sel_feature], self.sel_feature
        else:
            raise ValueError("输入矩阵维度与训练数据不匹配")


def apply_uve(X: np.ndarray, y: np.ndarray, ncomp: int = 7) -> Tuple[np.ndarray, np.ndarray]:
    """
    [内部辅助函数] 执行UVE特征选择。
    此函数主要由 UveSelector 包装器调用。外部不应直接调用此函数。

    Args:
        X: 特征矩阵
        y: 目标向量
        ncomp: 组件数量

    Returns:
        元组 (选定特征, 选定索引)
    """
    uve = UVESelector(X, y, ncomp)
    uve.calc_criteria()
    uve.eval_criteria(cv=5)
    selected_features, selected_indices = uve.cut_feature(X)

    logger.info(f"UVE selected {len(selected_indices)} features")
    return selected_features, selected_indices


def apply_pca(X: np.ndarray, n_components: Union[int, float] = 20, fitted_pca: Optional[PCA] = None) -> Tuple[np.ndarray, PCA]:
    """
    [内部辅助函数] 执行PCA降维，具有适当的fit-transform分离。
    此函数主要由 PcaSelector 包装器调用。外部不应直接调用此函数。

    Args:
        X: 要变换的特征矩阵，形状 (n_samples, n_features)
        n_components: 要保留的组件数量或方差比例
        fitted_pca: 可选的预拟合PCA对象。如果为None，将在X上拟合PCA

    Returns:
        元组 (变换后的特征矩阵, 拟合的PCA对象)
    """
    if fitted_pca is None:
        # 在训练数据上拟合 PCA
        pca = PCA(n_components=n_components)
        pca.fit(X)
        logger.info(f"PCA 在形状为 {X.shape} 的数据上拟合，降维到 {pca.n_components_} 个组件")
        return pca.transform(X), pca
    else:
        # 使用预拟合的 PCA 变换测试数据
        logger.info(f"使用预拟合的 PCA 变换形状为 {X.shape} 的数据")
        return fitted_pca.transform(X), fitted_pca


def _pc_cross_validation(X: np.ndarray, y: np.ndarray, pc: int, cv: int) -> Tuple[list, int]:
    """PLS 组件的交叉验证。"""
    kf = KFold(n_splits=cv)
    rmsecv = []

    for i in range(pc):
        rmse = []
        for train_index, test_index in kf.split(X):
            x_train, x_test = X[train_index], X[test_index]
            y_train, y_test = y[train_index], y[test_index]
            pls = PLSRegression(n_components=i + 1)
            pls.fit(x_train, y_train)
            y_predict = pls.predict(x_test)
            rmse.append(np.sqrt(mean_squared_error(y_test, y_predict)))
        rmse_mean = np.mean(rmse)
        rmsecv.append(rmse_mean)

    rindex = np.argmin(rmsecv)
    return rmsecv, rindex


def _cross_validation(X: np.ndarray, y: np.ndarray, pc: int, cv: int) -> float:
    """Simple cross-validation."""
    kf = KFold(n_splits=cv)
    rmse = []

    for train_index, test_index in kf.split(X):
        x_train, x_test = X[train_index], X[test_index]
        y_train, y_test = y[train_index], y[test_index]
        pls = PLSRegression(n_components=pc)
        pls.fit(x_train, y_train)
        y_predict = pls.predict(x_test)
        rmse.append(np.sqrt(mean_squared_error(y_test, y_predict)))

    rmse_mean = np.mean(rmse)
    return rmse_mean


def apply_cars(X: np.ndarray, y: np.ndarray, N: int = 50, f: int = 20, cv: int = 10) -> np.ndarray:
    """
    [内部辅助函数] 执行CARS (Competitive Adaptive Reweighted Sampling) 特征选择。
    此函数主要由 CarsSelector 包装器调用。外部不应直接调用此函数。

    Args:
        X: 特征矩阵
        y: 目标向量
        N: 迭代次数
        f: PLS组件数量
        cv: 交叉验证折数

    Returns:
        选定特征的索引数组
    """
    p = 0.8
    m, n = X.shape
    u = np.power((n / 2), (1 / (N - 1)))
    k = (1 / (N - 1)) * np.log(n / 2)
    cal_num = np.round(m * p)

    b2 = np.arange(n)
    x = copy.deepcopy(X)
    D = np.vstack((np.array(b2).reshape(1, -1), X))
    wave_data = []
    wave_num = []
    rmsecv = []
    r = []

    for i in range(1, N + 1):
        r.append(u * np.exp(-1 * k * i))
        wave_num_i = int(np.round(r[i - 1] * n))
        wave_num = np.hstack((wave_num, wave_num_i)) if len(wave_num) > 0 else [wave_num_i]

        cal_index = np.random.choice(np.arange(m), size=int(cal_num), replace=False)
        wave_index = b2[:wave_num_i].reshape(1, -1)[0]
        xcal = x[np.ix_(list(cal_index), list(wave_index))]
        ycal = y[cal_index]
        x = x[:, wave_index]
        D = D[:, wave_index]
        d = D[0, :].reshape(1, -1)
        wnum = n - wave_num_i

        if wnum > 0:
            d = np.hstack((d, np.full((1, wnum), -1)))

        if len(wave_data) == 0:
            wave_data = d
        else:
            wave_data = np.vstack((wave_data, d.reshape(1, -1)))

        if wave_num_i < f:
            f = wave_num_i

        pls = PLSRegression(n_components=f)
        pls.fit(xcal, ycal)
        beta = pls.coef_
        b = np.abs(beta)
        b2 = np.argsort(-b, axis=0)

        rmsecv_i, rindex = _pc_cross_validation(xcal, ycal, f, cv)
        rmsecv.append(_cross_validation(xcal, ycal, rindex + 1, cv))

    # Find optimal wavelengths
    wave = []
    for i in range(wave_data.shape[0]):
        wd = wave_data[i, :]
        WD = np.ones((len(wd)))
        for j in range(len(wd)):
            ind = np.where(wd == j)
            if len(ind[0]) == 0:
                WD[j] = 0
            else:
                WD[j] = wd[ind[0]]

        if len(wave) == 0:
            wave = copy.deepcopy(WD)
        else:
            wave = np.vstack((wave, WD.reshape(1, -1)))

    min_index = np.argmin(rmsecv)
    optimal = wave[min_index, :]
    bo_index = np.where(optimal != 0)
    opt_wave = bo_index[0]

    logger.info(f"CARS selected {len(opt_wave)} features")
    return opt_wave


# apply_selector function has been removed as part of architecture unification.
# Use get_selector() factory function instead for sklearn-compatible selectors.


def get_available_methods() -> list:
    """
    Get list of available feature selection methods.

    Returns:
        List of available method names
    """
    return ["None", "Lars", "Uve", "Pca", "Cars"]


# =============================================================================
# Scikit-learn Compatible Transformer Wrappers
# =============================================================================


@register_feature_selection("Lars")
class LarsSelector(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for LARS feature selection.
    """

    def __init__(self, nums: int = 40):
        """
        Initialize LARS selector.

        Args:
            nums: Number of features to select
        """
        self.nums = nums
        self.selected_features_ = None

    def fit(self, X, y):
        """
        Fit LARS selector and identify important features.

        Args:
            X: Training data, shape (n_samples, n_features)
            y: Target values, shape (n_samples,)

        Returns:
            self
        """
        self.selected_features_ = apply_lars(X, y, self.nums)
        return self

    def transform(self, X, y=None):
        """
        Transform data by selecting important features.

        Args:
            X: Data to transform, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            Transformed data, shape (n_samples, n_selected_features)
        """
        if self.selected_features_ is None:
            raise ValueError("LarsSelector must be fitted before transform")

        return X[:, self.selected_features_]


@register_feature_selection("Uve")
class UveSelector(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for UVE feature selection.
    """

    def __init__(self, nums: int = 40):
        """
        Initialize UVE selector.

        Args:
            nums: Number of features to select
        """
        self.nums = nums
        self.selected_features_ = None

    def fit(self, X, y):
        """
        Fit UVE selector and identify important features.

        Args:
            X: Training data, shape (n_samples, n_features)
            y: Target values, shape (n_samples,)

        Returns:
            self
        """
        _, self.selected_features_ = apply_uve(X, y, self.nums)
        return self

    def transform(self, X, y=None):
        """
        Transform data by selecting important features.

        Args:
            X: Data to transform, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            Transformed data, shape (n_samples, n_selected_features)
        """
        if self.selected_features_ is None:
            raise ValueError("UveSelector must be fitted before transform")

        return X[:, self.selected_features_]


@register_feature_selection("Cars")
class CarsSelector(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for CARS feature selection.
    """

    def __init__(self, nums: int = 40):
        """
        Initialize CARS selector.

        Args:
            nums: Number of features to select
        """
        self.nums = nums
        self.selected_features_ = None

    def fit(self, X, y):
        """
        Fit CARS selector and identify important features.

        Args:
            X: Training data, shape (n_samples, n_features)
            y: Target values, shape (n_samples,)

        Returns:
            self
        """
        self.selected_features_ = apply_cars(X, y, self.nums)
        return self

    def transform(self, X, y=None):
        """
        Transform data by selecting important features.

        Args:
            X: Data to transform, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            Transformed data, shape (n_samples, n_selected_features)
        """
        if self.selected_features_ is None:
            raise ValueError("CarsSelector must be fitted before transform")

        return X[:, self.selected_features_]


@register_feature_selection("Pca")
class PcaSelector(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for PCA feature selection.

    This wrapper provides API consistency with other spectral selectors
    while using sklearn's PCA for the actual computation.
    """

    def __init__(self, n_components: Union[int, float] = 20):
        """
        Initialize the PCA selector.

        Args:
            n_components: Number of components to keep
        """
        self.n_components = n_components
        self.pca_ = None

    def fit(self, X, y=None):
        """
        Fit the PCA selector.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            self: Returns the instance itself
        """
        self.pca_ = PCA(n_components=self.n_components)
        self.pca_.fit(X)
        return self

    def transform(self, X, y=None):
        """
        Transform the input data using fitted PCA.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            np.ndarray: Transformed data, shape (n_samples, n_components)
        """
        if self.pca_ is None:
            raise ValueError("PcaSelector must be fitted before transform")

        return self.pca_.transform(X)


# =============================================================================
# Selector Factory Function
# =============================================================================


def get_selector(method: str, params: Optional[Dict[str, Any]] = None) -> BaseEstimator:
    """
    Factory function to create sklearn-compatible selectors for feature selection methods.

    This function is now completely registry-driven, supporting pluggable extensions. Users can
    add custom feature selectors through @register_feature_selection decorator without
    modifying this factory function.

    Args:
        method (str): Feature selection method name
        params (Dict[str, Any], optional): Parameters for the selector

    Returns:
        BaseEstimator: Sklearn-compatible selector instance

    Raises:
        KeyError: If method is not found in the registry

    Example:
        >>> selector = get_selector("Pca", {"n_components": 10})
        >>> selector = get_selector("Lars", {"nums": 20})
        >>> selector = get_selector("Cars", {"nums": 15})
    """
    if params is None:
        params = {}

    # 🚀 从注册表获取选择器类
    try:
        selector_class = get_feature_selection_function(method)
    except KeyError:
        available_methods = get_available_methods()
        raise ValueError(f"Unknown feature selection method: {method}. Available: {available_methods}")

    # 🔧 根据不同的选择器类型，使用适当的参数实例化
    if method == "Pca":
        return selector_class(n_components=params.get("n_components", 20))
    elif method in ["Lars", "Uve", "Cars"]:
        return selector_class(nums=params.get("nums", 40))
    else:
        # 对于自定义选择器，尝试直接传递所有参数
        return selector_class(**params)
