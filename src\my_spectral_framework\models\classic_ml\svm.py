"""
光谱分析的支持向量机模型模块。

本模块为分类和回归任务提供 SVM 实现，改编自 OpenSA 的 ClassicCls.py 和 ClassicRgs.py。
设计理念：基于 sklearn 的 SVC 和 SVR，提供标准化的训练接口，
支持自动数据预处理和参数优化，确保 SVM 模型的最佳性能。

Author: txy
License: Apache-2.0
"""

from typing import Any, Dict, Optional, Union

import numpy as np
from numpy.typing import NDArray
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC, SVR

from ...core.utils import get_logger
from ...core.registry import register_model, register_model_params

logger = get_logger(__name__)





# 旧的训练和预测函数已被 SpectralModelWrapper 取代
# 这些函数的功能现在由统一的包装器提供，消除了代码重复


@register_model_params(name="SVM", model_type="classification")
def get_svm_classifier_params() -> Dict[str, Any]:
    """
    Get default parameters for SVM classifier.

    Returns:
        Dictionary of default parameters
    """
    return {"C": 1, "gamma": 1e-3, "kernel": "rbf", "random_state": 42, "use_scaler": True}


@register_model_params(name="SVR", model_type="regression")
def get_svr_regressor_params() -> Dict[str, Any]:
    """
    Get default parameters for SVR regressor.

    Returns:
        Dictionary of default parameters
    """
    return {"C": 2, "gamma": 1e-07, "kernel": "linear", "epsilon": 0.1, "use_scaler": True}


def get_available_kernels() -> list:
    """
    Get list of available SVM kernels.

    Returns:
        List of available kernel names
    """
    return ["linear", "poly", "rbf", "sigmoid"]


def tune_svm_hyperparameters(X_train: np.ndarray, y_train: np.ndarray, model_type: str = "classifier") -> Dict[str, Any]:
    """
    Simple hyperparameter tuning for SVM models.

    Args:
        X_train: Training features
        y_train: Training targets
        model_type: "classifier" or "regressor"

    Returns:
        Dictionary of tuned parameters
    """
    from sklearn.model_selection import GridSearchCV

    if model_type == "classifier":
        param_grid = {"C": [0.1, 1, 10, 100], "gamma": [1e-4, 1e-3, 1e-2, 1e-1], "kernel": ["rbf", "linear"]}
        model = SVC()
    else:  # regressor
        param_grid = {
            "C": [0.1, 1, 10, 100],
            "gamma": [1e-4, 1e-3, 1e-2, 1e-1],
            "kernel": ["rbf", "linear"],
            "epsilon": [0.01, 0.1, 1.0],
        }
        model = SVR()

    # Apply scaling
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # Grid search
    grid_search = GridSearchCV(model, param_grid, cv=5, scoring="accuracy" if model_type == "classifier" else "r2")
    grid_search.fit(X_train_scaled, y_train.ravel())

    logger.info(f"Best {model_type} parameters: {grid_search.best_params_}")
    return grid_search.best_params_


# ===== 注册表驱动的工厂函数 =====
# 以下函数使用 @register_model 装饰器注册到框架的模型注册表中
# 这些工厂函数负责创建具体的模型实例，符合"完全由注册表驱动"的设计原则




@register_model(name="SVM", model_type="classification")
def create_svm_classifier(**params) -> SVC:
    """
    创建 SVM 分类器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 SVC 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 SVC 的参数

    Returns:
        SVC: 配置好的支持向量机分类器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("SVM", "classification")
        >>> classifier = factory_func(C=1.0, kernel="rbf", random_state=42)
    """
    logger.info(f"创建 SVM 分类器，参数: {params}")
    return SVC(**params)


@register_model(name="SVR", model_type="regression")
def create_svm_regressor(**params) -> SVR:
    """
    创建 SVR 回归器实例的工厂函数。

    这个函数被注册到模型注册表中，用于动态创建 SVR 实例。
    符合框架的"完全由注册表驱动"设计原则。

    Args:
        **params: 传递给 SVR 的参数

    Returns:
        SVR: 配置好的支持向量回归器实例

    Examples:
        >>> # 通过注册表调用（框架内部使用）
        >>> from my_spectral_framework.core.registry import get_model_function
        >>> factory_func = get_model_function("SVR", "regression")
        >>> regressor = factory_func(C=2.0, kernel="linear", epsilon=0.1)
    """
    logger.info(f"创建 SVR 回归器，参数: {params}")
    return SVR(**params)
