"""
SOLID原则驱动的光谱预处理器测试 - 函数级精度验证

本文件包含遵循SOLID原则的、使用新测试框架的单元测试。
每个测试函数都通过 @unit_test 装饰器注册，并专注于验证一个独立的职责。
"""
import numpy as np
import pytest

# 导入被测试的函数
from my_spectral_framework.preprocessing.spectral_processors import SNV

# 导入新的装饰器注册系统
from tests.core.test_registry import unit_test

@unit_test(
    description="[SRP] 验证 SNV 的数学计算与黄金标准一致",
    tags=["preprocessing", "math", "critical", "snv"],
    priority="critical"
)
def test_SNV_numerical_correctness(mock_spectral_data):
    """
    职责：确保SNV算法的数学正确性。
    """
    X, _, _ = mock_spectral_data

    # 黄金标准：手动计算第一行的SNV
    first_row = X[0].astype(np.float64)
    expected_mean = np.mean(first_row)
    expected_std = np.std(first_row, ddof=1)

    # 避免除以零
    if expected_std == 0:
        expected_snv_first_row = np.zeros_like(first_row)
    else:
        expected_snv_first_row = (first_row - expected_mean) / expected_std

    # 执行SNV预处理
    processed_X = SNV(X)

    # 断言：与黄金标准一致
    np.testing.assert_allclose(processed_X[0], expected_snv_first_row, rtol=1e-14, err_msg="SNV计算与黄金标准不一致")


@unit_test(
    description="[SRP] 验证 SNV 优雅处理常数光谱（标准差为0）",
    tags=["preprocessing", "edge_case", "snv"],
    priority="high"
)
def test_SNV_handles_zero_std_deviation():
    """
    职责：验证在标准差为零的边界情况下的鲁棒性。
    """
    # 创建包含常数行的数据
    constant_row = np.array([[5.0, 5.0, 5.0, 5.0, 5.0]])
    normal_row = np.array([[1.0, 2.0, 3.0, 4.0, 5.0]])
    mixed_data = np.vstack([constant_row, normal_row])

    # 执行SNV
    result = SNV(mixed_data)

    # 断言：常数行应该变成全零
    np.testing.assert_array_equal(result[0], np.zeros(5), err_msg="常数行的SNV应该是全零向量")

    # 断言：正常行应该被正确处理且不为零
    assert not np.allclose(result[1], 0), "正常行不应该是全零"


# --- 以下为从旧的 test_preprocessing.py 迁移并使用 @unit_test 装饰的测试 ---

from my_spectral_framework.preprocessing.spectral_processors import (
    MSC, CT, MMS, SS, MA, SG, D1, D2, DT,
    MSCTransformer, CenteringTransformer, SNVTransformer,
    get_transformer, validate_preprocessing_params, get_available_methods
)

@unit_test(
    description="[SRP] 验证 SNV 强制使用 float64 保证数值精度",
    tags=["preprocessing", "data_types", "snv"],
    priority="high"
)
def test_SNV_forces_float64_output():
    """职责：确保数据类型的一致性和精度。"""
    # 测试不同输入类型
    int_array = np.array([[1, 2, 3, 4, 5]], dtype=np.int32)
    float32_array = np.array([[1.0, 2.0, 3.0, 4.0, 5.0]], dtype=np.float32)

    # 执行SNV
    result_int = SNV(int_array)
    result_float32 = SNV(float32_array)

    # 断言：输出类型必须是float64
    assert result_int.dtype == np.float64, f"期望float64，得到{result_int.dtype}"
    assert result_float32.dtype == np.float64, f"期望float64，得到{result_float32.dtype}"

@unit_test(
    description="[SRP] 验证 MSC 函数的基本功能和数值有效性",
    tags=["preprocessing", "msc"],
    priority="normal"
)
def test_MSC_basic_functionality(mock_spectral_data):
    """职责：验证MSC函数的基本功能和输出的有效性。"""
    X, _, _ = mock_spectral_data
    processed_X = MSC(X)
    assert processed_X.shape == X.shape, "输出形状应与输入相同"
    assert np.isfinite(processed_X).all(), "处理后的数据应为有限数值"

@unit_test(
    description="[SRP] 验证所有sklearn兼容的Transformer包装器的基本功能",
    tags=["preprocessing", "transformer", "sklearn_compat"],
    priority="high"
)
def test_all_transformers_sklearn_compatibility(mock_spectral_data):
    """职责：验证所有Transformer包装器都遵循sklearn的fit/transform接口。"""
    X, _, _ = mock_spectral_data
    transformers = [SNVTransformer(), MSCTransformer(), CenteringTransformer()]
    for transformer in transformers:
        # 测试fit, transform, 和 fit_transform
        fitted = transformer.fit(X)
        assert fitted is transformer, f"{transformer.__class__.__name__} 的 fit 方法应返回 self"
        transformed = transformer.transform(X)
        assert transformed.shape == X.shape, f"{transformer.__class__.__name__} 的 transform 方法应保持样本数"
        fit_transformed = transformer.fit_transform(X)
        assert fit_transformed.shape == X.shape, f"{transformer.__class__.__name__} 的 fit_transform 方法应保持样本数"

@unit_test(
    description="[OCP] 验证 get_transformer 工厂函数能为所有已知方法创建实例",
    tags=["preprocessing", "factory", "smoke_test"],
    priority="critical"
)
def test_get_transformer_factory_coverage(mock_spectral_data):
    """职责：确保工厂函数覆盖所有已知的预处理方法，支持开闭原则。"""
    X, _, _ = mock_spectral_data
    # 移除了 'None' 因为它返回 None 而不是一个 transformer 实例
    for method in ["MMS", "SS", "CT", "SNV", "MSC", "MA", "SG", "D1", "D2", "DT"]:
        try:
            transformer = get_transformer(method)
            assert transformer is not None, f"方法 '{method}' 的 transformer 不应为 None"
            # 基本的功能健全性测试
            transformer.fit(X)
            transformed = transformer.transform(X)
            assert transformed.shape[0] == X.shape[0], f"方法 '{method}' 不应改变样本数量"
        except Exception as e:
            pytest.fail(f"为方法 '{method}' 创建或使用 transformer 失败: {e}")

@unit_test(
    description="[SRP] 验证 get_transformer 对未知方法抛出清晰的 ValueError",
    tags=["preprocessing", "factory", "error_handling"],
    priority="normal"
)
def test_get_transformer_handles_invalid_method():
    """职责：验证工厂函数的错误处理能力。"""
    with pytest.raises(ValueError, match="未知的预处理方法"):
        get_transformer("INVALID_METHOD")

