"""
光谱分析框架的执行引擎模块。

本模块实现了"编排层"的核心组件 ExecutionEngine，负责将模型构建逻辑从 Runner 中分离，
提供统一的、可扩展的模型构建接口。这种设计使 Runner 专注于流程编排，
而 ExecutionEngine 专注于对象组装。

设计理念：
1. 单一职责原则：ExecutionEngine 只负责模型构建，不涉及训练和评估
2. 开闭原则：通过抽象基类支持不同类型的执行引擎扩展
3. 依赖倒置：Runner 依赖于抽象的 ExecutionEngine 接口，而非具体实现
4. 组合优于继承：通过组合 model_factory 的功能来构建复杂的模型管道


License: Apache-2.0
"""

from abc import ABC, abstractmethod
from typing import Any, Dict

from sklearn.base import BaseEstimator
from sklearn.pipeline import Pipeline

from .config_models import ExperimentRootConfig
from .utils import get_logger
from ..models.model_factory import get_model_instance
from ..preprocessing.spectral_processors import get_transformer
from ..feature_selection.spectral_selectors import get_selector

logger = get_logger(__name__)


class BaseExecutionEngine(ABC):
    """
    执行引擎的抽象基类。

    定义了所有执行引擎必须实现的核心接口。执行引擎的职责是根据配置
    构建完整的、可训练的机器学习模型或流水线。
    """

    @abstractmethod
    def build(self, config: ExperimentRootConfig, dynamic_params: dict = None) -> BaseEstimator:
        """
        根据实验配置构建完整的机器学习模型或流水线。

        Args:
            config: 完整的实验配置
            dynamic_params: 动态参数字典，用于运行时参数注入（例如，从数据中推断的 input_size）

        Returns:
            BaseEstimator: 一个行为与Scikit-learn兼容的估计器（通常是一个Pipeline）
        """
        pass


class SklearnExecutionEngine(BaseExecutionEngine):
    """
    基于 sklearn 的执行引擎实现。

    这个引擎负责将预处理、特征选择和模型组装成一个原生的 scikit-learn Pipeline。
    """

    def build(self, config: ExperimentRootConfig, dynamic_params: dict = None) -> Pipeline:
        """
        使用原生 scikit-learn 组件构建一个完整的流水线。
        """
        steps = []

        # 1. 预处理步骤
        preprocessing_cfg = config.preprocessing_config
        if preprocessing_cfg and preprocessing_cfg.steps:
            # 支持多步预处理
            pipeline_steps = []
            for i, step_cfg in enumerate(preprocessing_cfg.steps):
                if step_cfg.method != "None":
                    transformer = get_transformer(step_cfg.method, step_cfg.params)
                    pipeline_steps.append((f"preprocessor_{i}_{step_cfg.method.lower()}", transformer))
            if pipeline_steps:
                # 如果只有一个预处理步骤，则直接添加，否则用Pipeline包装
                if len(pipeline_steps) == 1:
                    steps.append(pipeline_steps[0])
                else:
                    steps.append(("preprocessor", Pipeline(steps=pipeline_steps)))

        # 2. 特征选择步骤
        fs_cfg = config.feature_selection_config
        if fs_cfg and fs_cfg.method != "None":
            selector = get_selector(fs_cfg.method, fs_cfg.params)
            steps.append(("feature_selector", selector))

        # 3. 模型步骤
        model_config = config.engine_config.ml_model_config
        # 调用我们刚刚简化的 model_factory 函数
        base_model = get_model_instance(
            model_type=model_config.type,
            model_name=model_config.name,
            model_params=model_config.params
        )
        steps.append(("model", base_model))

        pipeline = Pipeline(steps)
        logger.info(f"[SUCCESS] Sklearn Pipeline 构建成功，步骤: {[s[0] for s in steps]}")
        return pipeline

class PytorchExecutionEngine(BaseExecutionEngine):
    """
    基于 PyTorch 的执行引擎实现。

    这个引擎负责构建PyTorch模型，并将其封装在一个Scikit-learn兼容的包装器中。
    """

    def build(self, config: ExperimentRootConfig, dynamic_params: dict = None) -> BaseEstimator:
        """
        构建并包装 PyTorch 模型。
        """
        from ..optimization.sklearn_wrappers import PyTorchSklearnWrapper
        from ..models.deep_learning.dl_builder import build_model

        pt_config = config.engine_config.pytorch_model_config

        if dynamic_params is None:
            dynamic_params = {}

        # 优先使用动态参数，如果没有则从配置中获取
        input_size = dynamic_params.get('input_size', pt_config.params.get('input_size'))
        n_outputs = dynamic_params.get('n_outputs', pt_config.params.get('n_outputs'))

        if input_size is None or n_outputs is None:
            raise ValueError("PyTorch 模型需要 'input_size' 和 'n_outputs'。请在dynamic_params中提供。")

        # 构建PyTorch nn.Module
        model_architecture = build_model(
            model_name=pt_config.architecture,
            task_type=pt_config.task_type.value,
            input_size=input_size,
            params=pt_config.params,
            n_outputs=n_outputs
        )

        # 创建Scikit-learn包装器
        wrapper = PyTorchSklearnWrapper(
            model=model_architecture,
            train_params=pt_config.params
        )
        logger.info(f"[SUCCESS] PyTorch模型构建并包装成功: {type(wrapper).__name__}")

        # 注意：PyTorch引擎目前不返回一个完整的Pipeline，而是返回一个包装好的模型。
        # 预处理等步骤需要在Runner中应用，或者将此包装器放入一个Pipeline中。
        # 为保持接口一致，可以返回一个只包含模型的Pipeline。
        return Pipeline([('model', wrapper)])


def create_engine(engine_type: str) -> BaseExecutionEngine:
    """
    执行引擎工厂函数。

    Args:
        engine_type: 引擎类型 ('sklearn' or 'pytorch')

    Returns:
        相应的执行引擎实例
    """
    engine_type = engine_type.lower()

    if engine_type == "sklearn":
        return SklearnExecutionEngine()
    elif engine_type == "pytorch":
        return PytorchExecutionEngine()
    else:
        raise ValueError(f"不支持的引擎类型: {engine_type}。支持的类型: ['sklearn', 'pytorch']")
