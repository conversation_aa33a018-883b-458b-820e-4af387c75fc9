#!/usr/bin/env python3
"""
🚀 2025最新PyTorch智能安装器 - 统一合并版本

集成所有功能的完整解决方案：
- 🎯 智能CUDA版本检测（区分驱动和Toolkit版本）
- 🚀 支持uv和pip两种安装方式
- 📦 完整的RTX 50系列支持
- ✅ 多级降级策略确保兼容性
- 🔄 实时进度显示
- 🧪 全面的安装验证

"""

import subprocess
import sys
import re
import os
import time
import threading
import platform
import urllib.request
import urllib.error
from pathlib import Path


def print_banner():
    """打印统一横幅"""
    print("🚀 PyTorch智能安装器 - 统一版本")
    print("=" * 60)
    print("🎯 支持功能:")
    print("   - 智能CUDA版本检测")
    print("   - uv/pip双重支持")
    print("   - RTX 50系列专用优化")
    print("   - 多级降级策略")
    print("   - 实时进度显示")
    print("=" * 60)


def detect_installation_method():
    """检测使用uv还是pip"""
    print("🔍 检测安装方法...")
    
    # 检查uv是否可用
    try:
        result = subprocess.run(['uv', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ 检测到uv: {result.stdout.strip()}")
            
            # 检查是否在uv项目中
            has_pyproject = os.path.exists('pyproject.toml')
            has_venv = os.path.exists('.venv')
            
            if has_pyproject or has_venv:
                print("✅ 检测到uv项目环境，将使用uv安装")
                return 'uv'
            else:
                print("⚠️  有uv但不在项目环境中")
        else:
            print("⚠️  uv不可用")
    except:
        print("⚠️  uv命令不存在")
    
    print("📦 将使用传统pip安装")
    return 'pip'


def detect_real_cuda_version():
    """检测真正的CUDA版本 - 区分驱动版本和实际安装版本"""
    print("\n🔍 开始智能CUDA版本检测...")
    print("   💡 区分驱动版本和实际安装版本")
    
    nvcc_version = None
    smi_version = None
    
    # 方法1: 检测实际安装的CUDA Toolkit (nvcc)
    print(f"\n   🎯 检测实际安装的CUDA Toolkit:")
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            match = re.search(r'release (\d+\.\d+)', result.stdout)
            if match:
                nvcc_version = match.group(1)
                print(f"   ✅ nvcc检测到CUDA Toolkit: {nvcc_version}")
            else:
                print(f"   ⚠️  nvcc执行成功但未找到版本信息")
        else:
            print(f"   ❌ nvcc执行失败")
    except FileNotFoundError:
        print(f"   ❌ nvcc命令不存在 (CUDA Toolkit未安装)")
    except Exception as e:
        print(f"   ❌ nvcc执行出错: {e}")
    
    # 方法2: 检测驱动支持的最高版本 (nvidia-smi)
    print(f"\n   📊 检测驱动支持的CUDA版本:")
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            match = re.search(r'CUDA Version: (\d+\.\d+)', result.stdout)
            if match:
                smi_version = match.group(1)
                print(f"   ✅ nvidia-smi检测到驱动支持: {smi_version}")
            else:
                print(f"   ⚠️  nvidia-smi执行成功但未找到版本信息")
        else:
            print(f"   ❌ nvidia-smi执行失败")
    except FileNotFoundError:
        print(f"   ❌ nvidia-smi命令不存在 (NVIDIA驱动未安装)")
    except Exception as e:
        print(f"   ❌ nvidia-smi执行出错: {e}")
    
    # 版本选择逻辑
    print(f"\n   🤔 版本选择逻辑:")
    if nvcc_version and smi_version:
        print(f"   - 实际安装的CUDA Toolkit: {nvcc_version}")
        print(f"   - 驱动支持的最高版本: {smi_version}")
        if nvcc_version == smi_version:
            print(f"   ✅ 版本一致，使用: {nvcc_version}")
            return nvcc_version
        else:
            print(f"   ⚠️  版本不一致，优先使用实际安装的版本: {nvcc_version}")
            return nvcc_version
    elif nvcc_version:
        print(f"   - 实际安装的CUDA Toolkit: {nvcc_version}")
        print(f"   - 驱动信息: 不可用")
        print(f"   ✅ 使用实际安装的版本: {nvcc_version}")
        return nvcc_version
    elif smi_version:
        print(f"   - 实际安装的CUDA Toolkit: 未安装")
        print(f"   - 驱动支持的最高版本: {smi_version}")
        print(f"   ⚠️  只有驱动没有Toolkit，建议安装CPU版本")
        return None
    else:
        print(f"   - 实际安装的CUDA Toolkit: 未安装")
        print(f"   - 驱动信息: 不可用")
        print(f"   ❌ 未检测到任何CUDA，将安装CPU版本")
        return None


def detect_rtx_50_series():
    """检测RTX 50系列显卡"""
    print("\n🔍 检测RTX 50系列GPU...")
    
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=name', '--format=csv,noheader'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            gpu_names = result.stdout.strip().split('\n')
            for gpu_name in gpu_names:
                rtx_50_keywords = ['5070', '5080', '5090', 'RTX 50']
                if any(keyword in gpu_name for keyword in rtx_50_keywords):
                    print(f"🚀 检测到RTX 50系列GPU: {gpu_name.strip()}")
                    return True, gpu_name.strip()
            
            print("📋 未检测到RTX 50系列GPU")
            return False, None
    except:
        print("⚠️  无法检测GPU信息")
        return False, None


def get_pytorch_version_mapping(cuda_version, is_rtx_50=False):
    """获取PyTorch版本映射 - 基于最新官方支持"""
    if not cuda_version:
        return 'cpu', False, 'CPU版本'
    
    try:
        cuda_float = float(cuda_version)
        
        # RTX 50系列必须使用CUDA 12.8 nightly版本
        if is_rtx_50 or cuda_float >= 12.8:
            return 'cu128', True, f'CUDA {cuda_version} -> cu128 nightly (RTX 50系列支持)'
        elif cuda_float >= 12.4:
            return 'cu124', False, f'CUDA {cuda_version} -> cu124 (稳定版本)'
        elif cuda_float >= 12.1:
            return 'cu121', False, f'CUDA {cuda_version} -> cu121 (稳定版本)'
        elif cuda_float >= 11.8:
            return 'cu118', False, f'CUDA {cuda_version} -> cu118 (兼容版本)'
        else:
            return 'cu118', False, f'CUDA {cuda_version} -> cu118 (向下兼容)'
    except:
        return 'cu128', True, f'CUDA {cuda_version} -> cu128 (默认最新)'


def show_progress_spinner(stop_event, description):
    """显示进度旋转器"""
    spinner_chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    i = 0
    while not stop_event.is_set():
        print(f"\r   {spinner_chars[i % len(spinner_chars)]} {description}...", end="", flush=True)
        time.sleep(0.1)
        i += 1
    print("\r", end="", flush=True)


def run_command_with_progress(cmd, description, timeout=600):
    """运行命令并显示实时进度"""
    print(f"🔧 {description}...")
    print(f"   命令: {' '.join(cmd)}")
    
    stop_spinner = threading.Event()
    spinner_thread = threading.Thread(target=show_progress_spinner, args=(stop_spinner, description))
    spinner_thread.daemon = True
    spinner_thread.start()
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        output_lines = []
        last_progress_time = time.time()
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line = output.strip()
                output_lines.append(line)
                
                current_time = time.time()
                if current_time - last_progress_time > 2:
                    stop_spinner.set()
                    spinner_thread.join(timeout=0.5)
                    
                    # 显示关键进度信息
                    if any(keyword in line.lower() for keyword in ['downloading', 'installing', 'resolving']):
                        print(f"   📥 {line}")
                    elif 'error' in line.lower() or 'failed' in line.lower():
                        print(f"   ❌ {line}")
                    elif 'successfully' in line.lower() or 'installed' in line.lower():
                        print(f"   ✅ {line}")
                    
                    stop_spinner.clear()
                    spinner_thread = threading.Thread(target=show_progress_spinner, args=(stop_spinner, description))
                    spinner_thread.daemon = True
                    spinner_thread.start()
                    last_progress_time = current_time
        
        stop_spinner.set()
        spinner_thread.join(timeout=0.5)
        
        return_code = process.poll()
        if return_code == 0:
            print(f"✅ {description} 完成")
            return True, '\n'.join(output_lines)
        else:
            print(f"❌ {description} 失败 (返回码: {return_code})")
            return False, '\n'.join(output_lines)
    
    except subprocess.TimeoutExpired:
        stop_spinner.set()
        print(f"⏰ {description} 超时")
        process.kill()
        return False, "超时"
    except Exception as e:
        stop_spinner.set()
        print(f"💥 {description} 异常: {e}")
        return False, str(e)
    finally:
        stop_spinner.set()
        if spinner_thread.is_alive():
            spinner_thread.join(timeout=0.5)


def ensure_environment(install_method):
    """确保安装环境就绪"""
    if install_method == 'uv':
        print("🔧 准备uv环境...")
        
        # 检查项目结构
        has_pyproject = os.path.exists('pyproject.toml')
        has_uv_lock = os.path.exists('uv.lock')
        
        if has_pyproject and has_uv_lock:
            try:
                result = subprocess.run(['uv', 'sync'], capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    print("✅ uv项目同步成功")
                    return True
                else:
                    print(f"⚠️  uv sync警告: {result.stderr}")
            except Exception as e:
                print(f"⚠️  uv sync异常: {e}")
        
        # 检查虚拟环境
        try:
            result = subprocess.run(['uv', 'pip', 'list'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print("✅ uv虚拟环境可用")
                return True
        except:
            pass
        
        # 创建虚拟环境
        if not os.path.exists('.venv'):
            print("🆕 创建uv虚拟环境...")
            try:
                subprocess.run(['uv', 'venv', '.venv'], check=True, timeout=120)
                print("✅ uv虚拟环境创建成功")
                return True
            except Exception as e:
                print(f"❌ 无法创建uv环境: {e}")
                return False
        
        return True
    else:
        print("📦 使用系统pip环境")
        return True


def install_pytorch_version(cuda_suffix, use_nightly, install_method):
    """安装指定版本的PyTorch"""
    packages = ["torch", "torchvision", "torchaudio"]
    
    if cuda_suffix == 'cpu':
        index_url = "https://download.pytorch.org/whl/cpu"
    else:
        base_url = "https://download.pytorch.org/whl"
        index_url = f"{base_url}/{'nightly/' if use_nightly else ''}{cuda_suffix}"
    
    if install_method == 'uv':
        cmd = ["uv", "pip", "install", "--upgrade"]
    else:
        cmd = [sys.executable, "-m", "pip", "install", "--upgrade"]
    
    if use_nightly:
        cmd.append("--pre")
    cmd.extend(packages)
    cmd.extend(["--index-url", index_url])
    
    success, output = run_command_with_progress(
        cmd,
        f"安装PyTorch {cuda_suffix} ({'nightly' if use_nightly else 'stable'})",
        timeout=1200
    )
    
    return success


def install_with_fallback_strategy(cuda_version, is_rtx_50, install_method):
    """多级降级策略安装"""
    print(f"\n🚀 开始PyTorch安装...")
    
    # 定义安装策略
    strategies = []
    
    if is_rtx_50:
        print("🚀 RTX 50系列专用安装策略")
        strategies = [
            ('cu128', True, 'RTX 50系列nightly版本 (推荐)'),
            ('cu128', False, 'RTX 50系列稳定版本'),
            ('cu124', False, '降级到cu124 (兼容性降级)'),
            ('cu121', False, '降级到cu121 (通用兼容)')
        ]
    else:
        cuda_suffix, use_nightly, desc = get_pytorch_version_mapping(cuda_version, False)
        strategies = [
            (cuda_suffix, use_nightly, f'推荐版本: {desc}'),
            ('cu121', False, '通用兼容版本'),
            ('cu118', False, '最大兼容版本'),
            ('cpu', False, 'CPU降级版本')
        ]
    
    print(f"\n📋 安装策略列表:")
    for i, (suffix, nightly, description) in enumerate(strategies, 1):
        print(f"   {i}. {description}")
    
    # 逐个尝试策略
    for i, (suffix, nightly, description) in enumerate(strategies, 1):
        print(f"\n🔄 尝试策略 {i}/{len(strategies)}: {description}")
        
        if install_pytorch_version(suffix, nightly, install_method):
            print(f"✅ 安装成功: {description}")
            return True, suffix, nightly
        else:
            print(f"❌ 策略失败: {description}")
            if i < len(strategies):
                print(f"   继续尝试下一个策略...")
    
    return False, None, None


def create_enhanced_verification_code(is_rtx_50=False):
    """创建增强的验证代码"""
    base_code = '''
import torch
import sys
import time

print('🔍 开始PyTorch安装验证...')
print(f'Python版本: {sys.version.split()[0]}')
print(f'PyTorch版本: {torch.__version__}')

# 检查CUDA可用性
cuda_available = torch.cuda.is_available()
print(f'CUDA可用: {cuda_available}')

if cuda_available:
    device_count = torch.cuda.device_count()
    print(f'GPU数量: {device_count}')
    
    device_name = torch.cuda.get_device_name(0)
    capability = torch.cuda.get_device_capability(0)
    
    print(f'GPU设备: {device_name}')
    print(f'计算能力: sm_{capability[0]}{capability[1]}')
'''
    
    if is_rtx_50:
        rtx_50_code = '''
    # RTX 50系列专项测试
    if any(x in device_name for x in ['5070', '5080', '5090', 'RTX 50']):
        print('🚀 确认RTX 50系列GPU!')
        
        if capability[0] >= 9:  # Blackwell架构
            print('✅ Blackwell架构支持正常')
            
            try:
                print('🧪 进行RTX 50系列性能测试...')
                
                # 大型矩阵运算测试
                x = torch.randn(2000, 2000, device='cuda')
                start_time = time.time()
                y = torch.mm(x, x.t())
                end_time = time.time()
                print(f'✅ 大型矩阵运算: {end_time-start_time:.3f}秒')
                
                # 混合精度测试
                with torch.autocast(device_type='cuda'):
                    z = torch.mm(x.half(), x.half().t())
                print('✅ 混合精度计算测试通过')
                
                print('🎉 RTX 50系列完美支持!')
                
            except Exception as e:
                print(f'⚠️  RTX 50系列测试失败: {e}')
                if 'no kernel image' in str(e):
                    print('💡 提示: 可能需要更新到最新nightly版本')
        else:
            print(f'⚠️  计算能力不符合Blackwell架构标准')
'''
        base_code += rtx_50_code
    
    standard_test_code = '''
    else:
        print('进行标准GPU测试...')
        try:
            x = torch.randn(1000, 1000, device='cuda')
            y = torch.mm(x, x.t())
            print('✅ 标准GPU计算测试通过')
        except Exception as e:
            print(f'❌ GPU测试失败: {e}')
    
    print(f'PyTorch CUDA版本: {torch.version.cuda}')
    try:
        print(f'cuDNN版本: {torch.backends.cudnn.version()}')
    except:
        print('cuDNN版本: 未知')
else:
    print('使用CPU版本')
    try:
        x = torch.randn(100, 100)
        y = torch.mm(x, x.t())
        print('✅ CPU计算测试通过')
    except Exception as e:
        print(f'❌ CPU测试失败: {e}')

print('验证完成')
'''
    
    return base_code + standard_test_code


def verify_installation(install_method, is_rtx_50=False):
    """验证PyTorch安装"""
    print(f"\n🔍 验证PyTorch安装...")
    
    verification_code = create_enhanced_verification_code(is_rtx_50)
    
    if install_method == 'uv':
        cmd = ["uv", "run", "python", "-c", verification_code]
    else:
        cmd = [sys.executable, "-c", verification_code]
    
    success, output = run_command_with_progress(
        cmd,
        "验证PyTorch安装",
        timeout=120
    )
    
    if success:
        print("📊 验证结果:")
        for line in output.strip().split('\n'):
            if line.strip():
                print(f"   {line}")
        return True
    else:
        print(f"❌ 验证失败")
        print(f"详细错误: {output}")
        return False


def main():
    """主函数"""
    print_banner()
    
    # 检测操作系统
    os_name = platform.system()
    print(f"🖥️  操作系统: {os_name}")
    
    # 检测安装方法
    install_method = detect_installation_method()
    
    # 检测CUDA版本
    cuda_version = detect_real_cuda_version()
    
    # 检测RTX 50系列
    is_rtx_50, gpu_name = detect_rtx_50_series()
    
    # 显示检测结果
    print(f"\n📊 检测结果汇总:")
    print(f"   安装方法: {install_method}")
    print(f"   CUDA版本: {cuda_version or 'CPU版本'}")
    print(f"   RTX 50系列: {'是' if is_rtx_50 else '否'}")
    if is_rtx_50:
        print(f"   GPU型号: {gpu_name}")
    
    # 用户确认
    print(f"\n❓ 是否继续安装? (y/N): ", end="")
    try:
        response = input().strip().lower()
        if response not in ['y', 'yes']:
            print("🚫 用户取消安装")
            return False
    except KeyboardInterrupt:
        print("\n🚫 用户中断安装")
        return False
    
    # 确保环境就绪
    if not ensure_environment(install_method):
        print("❌ 环境准备失败")
        return False
    
    # 安装PyTorch
    success, final_suffix, final_nightly = install_with_fallback_strategy(
        cuda_version, is_rtx_50, install_method
    )
    
    if not success:
        print("\n💥 所有安装策略都失败了!")
        print("💡 建议:")
        print("   - 检查网络连接")
        print("   - 尝试使用代理或镜像源")
        print("   - 手动安装PyTorch")
        return False
    
    print(f"\n🎉 PyTorch安装成功!")
    print(f"   最终版本: {final_suffix} ({'nightly' if final_nightly else 'stable'})")
    
    # 验证安装
    success = verify_installation(install_method, is_rtx_50)
    
    if success:
        print(f"\n🎉 安装完成并验证通过!")
        if is_rtx_50:
            print(f"💫 恭喜！您的RTX 50系列GPU已完美支持!")
        print(f"💡 现在可以开始深度学习开发:")
        if install_method == 'uv':
            print(f"   uv run python your_script.py")
        else:
            print(f"   python your_script.py")
    else:
        print(f"\n💥 验证失败，但安装可能成功")
        print(f"💡 请手动测试PyTorch功能")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n🚫 用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
