"""
本地文件系统存储后端实现。

本模块实现了基于本地文件系统的存储后端，支持多种数据格式的保存和加载。
这是框架的默认存储后端，提供了完整的文件操作功能。

设计理念：
1. 封装性：将所有文件操作逻辑封装在一个类中
2. 格式支持：支持多种常见的数据格式（joblib、CSV、JSON、TXT等）
3. 目录管理：自动创建和管理目录结构
4. 错误处理：提供详细的错误信息和异常处理


License: Apache-2.0
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import joblib
import numpy as np
import pandas as pd

from .base import BaseStorageBackend, StorageError, UnsupportedFormatError, StorageNotFoundError
from ..core.utils import json_custom_serializer


class LocalStorageBackend(BaseStorageBackend):
    """
    本地文件系统存储后端实现。
    
    这个类封装了所有本地文件系统的读写操作，支持多种数据格式，
    并提供了完整的文件管理功能。
    """
    
    def __init__(self, base_path: Union[str, Path]):
        """
        初始化本地存储后端。
        
        Args:
            base_path: 存储的基础路径
        """
        super().__init__(base_path)
        
        # 确保基础路径存在
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 支持的格式映射
        self._format_handlers = {
            'joblib': self._save_joblib,
            'csv': self._save_csv,
            'json': self._save_json,
            'txt': self._save_txt,
            'numpy': self._save_numpy,
        }

        self._load_handlers = {
            'joblib': self._load_joblib,
            'csv': self._load_csv,
            'json': self._load_json,
            'txt': self._load_txt,
            'numpy': self._load_numpy,
        }

        # 格式到文件扩展名的映射
        self._format_extensions = {
            'joblib': 'joblib',
            'csv': 'csv',
            'json': 'json',
            'txt': 'txt',
            'numpy': 'npy',  # NumPy 文件使用 .npy 扩展名
        }
        
        self.logger.info(f"LocalStorageBackend 初始化完成，基础路径: {self.base_path}")
    
    def save(
        self, 
        data: Any, 
        artifact_name: str, 
        format: str, 
        subdir: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        保存数据到本地文件系统。
        
        Args:
            data: 要保存的数据对象
            artifact_name: 产物名称
            format: 数据格式
            subdir: 子目录路径
            metadata: 可选的元数据信息
            
        Returns:
            str: 文件的完整路径
        """
        try:
            # 确定目标目录
            if subdir:
                target_dir = self.base_path / subdir
            else:
                # 根据格式使用默认子目录
                target_dir = self._get_default_subdir(format)
            
            # 创建目录
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 构建文件路径（使用正确的扩展名）
            extension = self._format_extensions.get(format, format)
            file_path = target_dir / f"{artifact_name}.{extension}"
            
            # 根据格式保存数据
            if format not in self._format_handlers:
                raise UnsupportedFormatError(f"不支持的格式: {format}")
            
            self._format_handlers[format](data, file_path)
            
            # 保存元数据（如果提供）
            if metadata:
                self._save_metadata(file_path, metadata)
            
            # 返回相对路径作为 URI
            try:
                uri = str(file_path.relative_to(Path.cwd()))
            except ValueError:
                uri = str(file_path.absolute())
            
            self.logger.info(f"成功保存产物 '{artifact_name}' 到: {uri}")
            return uri
            
        except Exception as e:
            error_msg = f"保存产物 '{artifact_name}' 失败: {e}"
            self.logger.error(error_msg)
            raise StorageError(error_msg) from e
    
    def load(self, uri: str, format: str) -> Any:
        """
        从本地文件系统加载数据。
        
        Args:
            uri: 文件路径
            format: 数据格式
            
        Returns:
            Any: 加载的数据对象
        """
        try:
            file_path = Path(uri)
            
            # 检查文件是否存在
            if not file_path.exists():
                raise StorageNotFoundError(f"文件不存在: {uri}")
            
            # 根据格式加载数据
            if format not in self._load_handlers:
                raise UnsupportedFormatError(f"不支持的格式: {format}")
            
            data = self._load_handlers[format](file_path)
            
            self.logger.info(f"成功加载产物: {uri}")
            return data
            
        except Exception as e:
            if isinstance(e, (StorageNotFoundError, UnsupportedFormatError)):
                raise
            error_msg = f"加载产物失败: {uri}, 错误: {e}"
            self.logger.error(error_msg)
            raise StorageError(error_msg) from e
    
    def get_uri(self, artifact_name: str, format: str, subdir: str = "") -> str:
        """
        获取产物的存储 URI。

        Args:
            artifact_name: 产物名称
            format: 数据格式
            subdir: 子目录路径

        Returns:
            str: 完整的文件路径
        """
        if subdir:
            target_dir = self.base_path / subdir
        else:
            target_dir = self._get_default_subdir(format)

        # 使用正确的扩展名
        extension = self._format_extensions.get(format, format)
        file_path = target_dir / f"{artifact_name}.{extension}"

        try:
            return str(file_path.relative_to(Path.cwd()))
        except ValueError:
            return str(file_path.absolute())
    
    def exists(self, uri: str) -> bool:
        """
        检查文件是否存在。
        
        Args:
            uri: 文件路径
            
        Returns:
            bool: 文件是否存在
        """
        return Path(uri).exists()
    
    def delete(self, uri: str) -> bool:
        """
        删除文件。
        
        Args:
            uri: 文件路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            file_path = Path(uri)
            if file_path.exists():
                file_path.unlink()
                self.logger.info(f"成功删除文件: {uri}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"删除文件失败: {uri}, 错误: {e}")
            return False
    
    def list_artifacts(self, subdir: str = "") -> List[Dict[str, Any]]:
        """
        列出指定子目录下的所有产物。
        
        Args:
            subdir: 子目录路径
            
        Returns:
            List[Dict[str, Any]]: 产物信息列表
        """
        if subdir:
            target_dir = self.base_path / subdir
        else:
            target_dir = self.base_path
        
        artifacts = []
        
        if target_dir.exists():
            for file_path in target_dir.rglob("*"):
                if file_path.is_file():
                    try:
                        uri = str(file_path.relative_to(Path.cwd()))
                    except ValueError:
                        uri = str(file_path.absolute())
                    
                    artifacts.append({
                        "name": file_path.stem,
                        "format": file_path.suffix[1:] if file_path.suffix else "",
                        "uri": uri,
                        "size_bytes": file_path.stat().st_size,
                        "modified_time": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
        
        return artifacts
    
    def _get_default_subdir(self, format: str) -> Path:
        """根据格式获取默认子目录。"""
        format_dirs = {
            'joblib': 'models',
            'csv': 'results',
            'json': 'results',
            'txt': 'results',
            'numpy': 'artifacts',
        }
        
        subdir_name = format_dirs.get(format, 'artifacts')
        return self.base_path / subdir_name
    
    def _save_metadata(self, file_path: Path, metadata: Dict[str, Any]) -> None:
        """保存元数据到 .meta 文件。"""
        meta_path = file_path.with_suffix(file_path.suffix + '.meta')
        with open(meta_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, default=json_custom_serializer)
    
    # 格式特定的保存方法
    def _save_joblib(self, data: Any, file_path: Path) -> None:
        """保存 joblib 格式数据。"""
        joblib.dump(data, file_path)
    
    def _save_csv(self, data: Any, file_path: Path) -> None:
        """保存 CSV 格式数据。"""
        if isinstance(data, pd.DataFrame):
            data.to_csv(file_path, index=False)
        elif isinstance(data, np.ndarray):
            pd.DataFrame(data).to_csv(file_path, index=False)
        else:
            raise ValueError(f"无法将 {type(data)} 保存为 CSV 格式")
    
    def _save_json(self, data: Any, file_path: Path) -> None:
        """保存 JSON 格式数据。"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=json_custom_serializer)
    
    def _save_txt(self, data: Any, file_path: Path) -> None:
        """保存文本格式数据。"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(str(data))
    
    def _save_numpy(self, data: Any, file_path: Path) -> None:
        """保存 NumPy 格式数据。"""
        if isinstance(data, np.ndarray):
            np.save(file_path, data)
        else:
            raise ValueError(f"无法将 {type(data)} 保存为 NumPy 格式")
    
    # 格式特定的加载方法
    def _load_joblib(self, file_path: Path) -> Any:
        """加载 joblib 格式数据。"""
        return joblib.load(file_path)
    
    def _load_csv(self, file_path: Path) -> pd.DataFrame:
        """加载 CSV 格式数据。"""
        return pd.read_csv(file_path)
    
    def _load_json(self, file_path: Path) -> Any:
        """加载 JSON 格式数据。"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_txt(self, file_path: Path) -> str:
        """加载文本格式数据。"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _load_numpy(self, file_path: Path) -> np.ndarray:
        """加载 NumPy 格式数据。"""
        return np.load(file_path)
