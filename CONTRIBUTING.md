# 贡献指南

欢迎您为 My Spectral Framework 贡献代码！本指南将帮助您了解如何为框架添加新的组件。

## 插件化组件系统

本框架采用插件化架构，您可以轻松添加自定义的预处理器、特征选择器和模型，而无需修改核心代码。所有自定义组件都应放置在 `src/my_spectral_framework/components/` 目录下。

### 添加自定义预处理器

1.  **创建文件**: 在 `src/my_spectral_framework/components/` 目录下创建新文件，例如 `my_preprocessor.py`。
2.  **编写并注册**: 使用 `@register_preprocessing` 装饰器来注册您的函数或类。框架在启动时会自动发现并加载它。

**示例：** `my_preprocessor.py`
```python
import numpy as np
from sklearn.base import BaseEstimator, TransformerMixin
from my_spectral_framework.core.registry import register_preprocessing

# 作为一个sklearn兼容的Transformer类进行注册
@register_preprocessing("MyCustomScaler")
class MyCustomScaler(BaseEstimator, TransformerMixin):
    def __init__(self, factor=2.0):
        self.factor = factor

    def fit(self, X, y=None):
        # 在这里可以学习数据中的参数，例如均值、标准差等
        self.data_mean_ = np.mean(X, axis=0)
        return self

    def transform(self, X):
        # 应用变换逻辑
        return (X - self.data_mean_) * self.factor
```

### 添加自定义特征选择器

流程与添加预处理器类似，但需使用 `@register_feature_selection` 装饰器。

**示例：** `my_selector.py`

```python
import numpy as np
from sklearn.base import BaseEstimator, TransformerMixin
from my_spectral_framework.core.registry import register_feature_selection

@register_feature_selection("SelectFirstN")
class SelectFirstN(BaseEstimator, TransformerMixin):
    def __init__(self, n_features=10):
        self.n_features = n_features

    def fit(self, X, y=None):
        # 无需学习，直接返回
        return self

    def transform(self, X):
        # 选择前N个特征
        return X[:, :self.n_features]
```

### 添加自定义模型

框架支持 **Sklearn** 和 **PyTorch** 两种引擎。

#### 添加 Sklearn 兼容模型

对于传统机器学习模型，使用 `@register_model` 装饰器。

**示例：** `my_sklearn_model.py`

```python
from sklearn.linear_model import LogisticRegression
from my_spectral_framework.core.registry import register_model

@register_model(name="MyLogisticRegression", model_type="classification")
def create_my_lr_classifier(**params):
    """
    一个简单的工厂函数，用于创建和配置模型实例。
    """
    # 可以在这里设置默认参数或进行参数验证
    return LogisticRegression(**params)
```

#### 添加 PyTorch 模型

1.  在 `src/my_spectral_framework/models/deep_learning/architectures.py` 中定义您的 `nn.Module` 网络架构。
2.  在 `src/my_spectral_framework/models/deep_learning/dl_builder.py` 的 `SUPPORTED_ARCHITECTURES` 字典中注册您的新架构。

### 在配置中使用自定义组件

完成注册后，您就可以在实验配置文件中，通过注册的名称直接使用您的自定义组件了：

```python
# 在 conf/runs/my_custom_exp.py 中
EXPERIMENT_CONFIG = {
    # ...
    "preprocessing_config": {
        "steps": [
            {"method": "MyCustomScaler", "params": {"factor": 1.5}}
        ]
    },
    "feature_selection_config": {
        "method": "SelectFirstN",
        "params": {"n_features": 30}
    },
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {
            "type": "Classification",
            "name": "MyLogisticRegression",
            "params": {"C": 0.5}
        }
    },
    # ...
}
```

## 提交贡献流程

1.  **Fork** 本仓库。
2.  创建您的特性分支 (`git checkout -b feature/AmazingFeature`)。
3.  提交您的更改 (`git commit -m 'Add some AmazingFeature'`)。
4.  将分支推送到远程 (`git push origin feature/AmazingFeature`)。
5.  创建一个 **Pull Request**。

感谢您的贡献！
