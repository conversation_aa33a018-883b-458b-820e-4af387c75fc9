{"experiment_type": "single_run", "data_config": {"source_type": "opensa_test", "spectrum_id_strategy": "auto_generate", "type": "Cls", "file_path": null, "label_col_name": "label", "group_col_name": null, "spectrum_unique_id_col_name": "spectrum_id", "wave_number_range": null, "test_size": 0.2, "random_seed": 42}, "preprocessing_config": {"steps": [{"method": "SNV", "params": {}}]}, "feature_selection_config": {"method": "Pca", "params": {"n_components": 20}}, "experiment_config": {"name": "sklearn_rf_cls_example", "description": "Sklearn随机森林分类示例", "tags": ["sklearn", "classification", "random_forest", "example"], "result_handler": "filesystem"}, "evaluation_config": {"metrics": {"classification": ["accuracy", "precision", "recall", "f1"], "regression": ["rmse", "r2", "mae"]}, "save_predictions": true, "save_model": true}, "paths_config": {"data_dir": "data", "models_dir": "models", "results_dir": "results", "logs_dir": "logs"}, "traceability_config": {"level": "none"}, "engine_config": {"engine": "sklearn", "ml_model_config": {"type": "Classification", "name": "RF", "params": {"n_estimators": 100, "max_depth": 5, "min_samples_split": 2, "min_samples_leaf": 1, "random_state": 42}}, "cv_config": null, "hyperparameter_optimization_config": null}}