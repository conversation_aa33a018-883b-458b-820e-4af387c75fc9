#!/bin/bash
# Comprehensive End-to-End Configuration Test Suite Runner
# This script runs all E2E tests to validate configuration stability

echo "=================================================================="
echo "  🚀 Comprehensive E2E Configuration Test Suite"
echo "=================================================================="
echo "  Testing all core configurations for stability and reliability"
echo "  This includes: Classification, CV, Hyperopt, PyTorch, and more"
echo "=================================================================="

# Set error handling
set -e

# Run the comprehensive E2E test suite with verbose output
echo "📋 Starting test execution..."
python -m unittest tests.e2e.test_e2e_config_runner -v

# Check the exit code of the test command
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉=================================================================="
    echo "  ✅ ALL E2E TESTS PASSED SUCCESSFULLY!"
    echo "  🛡️  Your configuration system is stable and reliable."
    echo "=================================================================="
else
    echo ""
    echo "💥=================================================================="
    echo "  ❌ E2E TESTS FAILED!"
    echo "  🔍 Please check the output above for detailed error information."
    echo "=================================================================="
    exit 1
fi
