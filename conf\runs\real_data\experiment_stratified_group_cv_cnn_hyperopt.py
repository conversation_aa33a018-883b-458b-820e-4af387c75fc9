# 由 ExamplePlugin 处理
"""
分层分组交叉验证CNN超参数优化实验配置。

此配置展示了一个完整的深度学习超参数优化实验，包括：
- 从 `data/real/spectral_data.csv` 加载真实光谱数据（2674个样本）
- 使用分层分组K折交叉验证（StratifiedGroupKFold）防止数据泄漏
- CNN深度学习模型 + CUDA GPU加速
- Optuna贝യെ斯超参数优化
- 完整溯源追踪（full级别）
- 50次超参数试验，每次5折交叉验证


Date: 2025-01-24
"""

EXPERIMENT_CONFIG = {
    # 实验类型：超参数优化
    "experiment_type": "hyperparameter_optimization",
    
    # 实验元数据
    "experiment_config": {
        "name": "stratified_group_cv_cnn_hyperopt_real_data",
        "description": "分层分组交叉验证CNN超参数优化 - 真实光谱数据全面测试",
        "tags": ["real_data", "cnn", "hyperopt", "stratified_group_cv", "cuda", "traceability"]
    },
    
    # 数据配置：使用真实光谱数据
    "data_config": {
        "source_type": "advanced_file",
        "file_path": "real/spectral_data.csv",  # 路径相对于 data/ 目录
        "type": "Cls",
        
        # 根据真实数据结构进行列映射
        "label_col_name": "category_name",  # 使用类别名称作为标签
        "spectrum_unique_id_col_name": "detailed_spectral_identifier",  # 使用详细标识符作为ID
        "group_col_name": "main_sample_name",  # 关键：按主样本名称分组以防泄漏
        
        # 数据处理选项
        "encoding_priority": ["utf-8", "gbk", "latin-1"],
        "nan_handling_key_cols": "remove_row",
        "nan_fill_value_spectra": 0.0,
        
        # 数据划分
        "test_size": 0.2,
        "random_seed": 42,
        "stratify": True  # 启用分层采样
    },
    
    # 预处理流程：针对CNN优化
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}}  # 标准正态变量变换
        ]
    },
    
    # 特征选择：CNN不需要特征选择
    "feature_selection_config": {
        "method": "None"
    },
    
    # ==================== 代码修正部分 START ====================
    # 引擎配置：PyTorch CNN + 超参数优化
    "engine_config": {
        "engine": "pytorch",
        
        # CNN模型配置
        # 修正1: "ml_model_config" -> "pytorch_model_config"
        # 修正2: "type" -> "task_type", "name" -> "architecture"
        "pytorch_model_config": {
            "task_type": "Classification",
            "architecture": "CNN",
            "params": {
                "device": "cuda",  # 使用GPU加速
                "random_state": 42
            }
        },
        
        # 分层分组交叉验证配置
        "cv_config": {
            "cv_strategy": "StratifiedGroupKFold",
            "n_splits": 5,
            "shuffle": True,
            "random_state": 42
        },
        
        # 超参数优化配置
        "hyperparameter_optimization_config": {
            "strategy": "optuna",
            "n_trials": 50,  # 50次试验
            "timeout": 3600,  # 1小时超时
            "cv_folds": 5,
            "scoring": "accuracy",
            "direction": "maximize",
            
            # CNN超参数搜索空间
            "param_space": {
                "model__conv_layers": {
                    "type": "categorical",
                    "choices": [1, 2, 3]
                },
                "model__conv_filters": {
                    "type": "categorical", 
                    "choices": [32, 64, 128]
                },
                "model__kernel_size": {
                    "type": "categorical",
                    "choices": [3, 5, 7]
                },
                "model__fc_hidden_size": {
                    "type": "categorical",
                    "choices": [64, 128, 256]
                },
                "model__dropout_rate": {
                    "type": "float",
                    "low": 0.1,
                    "high": 0.5
                },
                "model__learning_rate": {
                    "type": "float",
                    "low": 0.0001,
                    "high": 0.01,
                    "log": True
                },
                "model__batch_size": {
                    "type": "categorical",
                    "choices": [32, 64, 128]
                },
                "model__epochs": {
                    "type": "int",
                    "low": 20,
                    "high": 100
                }
            },
            
            # 剪枝配置：提前停止表现差的试验
            "pruning": {
                "enabled": True,
                "pruner": "MedianPruner",
                "n_startup_trials": 5,
                "n_warmup_steps": 10
            },
            
            # 研究配置
            "study_config": {
                "study_name": "cnn_spectral_stratified_group_cv",
                "storage": "sqlite:///optuna_study.db",
                "load_if_exists": True
            }
        }
    },
    # ===================== 代码修正部分 END =====================
    
    # 评估配置：全面的分类指标
    "evaluation_config": {
        "metrics": {
            "classification": [
                "accuracy",
                "precision",
                "recall", 
                "f1",
                "roc_auc"
            ]
        },
        "save_predictions": True,
        "save_model": True,
        "save_feature_importance": False,  # CNN不需要特征重要性
        "save_confusion_matrix": True,
        "save_classification_report": True
    },
    
    # 溯源配置：完整追踪
    "traceability_config": {
        "level": "full"
    },
    
    # 路径配置
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models", 
        "results_dir": "results",
        "logs_dir": "logs"
    }
}