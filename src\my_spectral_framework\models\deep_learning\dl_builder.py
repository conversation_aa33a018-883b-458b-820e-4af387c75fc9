"""
深度学习模型构建工厂模块。

本模块提供了统一的深度学习模型构建接口，支持：
- 基于配置的模型实例化
- 架构参数的验证和处理
- 未来的神经架构搜索（NAS）集成
- 复杂模型组合的支持

Author: txy
License: Apache-2.0
"""

from typing import Any, Dict, Optional, Union

import torch.nn as nn

from ...core.utils import get_logger
from ...core.registry import register_model_hyperparams
from .architectures import CNN1DNet

logger = get_logger(__name__)

# 支持的模型架构注册表
SUPPORTED_ARCHITECTURES = {
    "CNN": CNN1DNet,
    "CNN1D": CNN1DNet,  # 别名
}


def build_model(
    model_name: str,
    task_type: str,
    input_size: int,
    params: Optional[Dict[str, Any]] = None,
    n_outputs: Optional[int] = None,
    **kwargs
) -> nn.Module:
    """
    构建深度学习模型的工厂函数。
    
    该函数根据模型名称、任务类型和参数配置来实例化相应的 PyTorch 模型。
    支持分类和回归任务，并为未来的架构扩展提供了灵活的接口。
    
    Args:
        model_name: 模型名称（如 "CNN", "CNN1D"）
        task_type: 任务类型（"classification" 或 "regression"）
        input_size: 输入特征维度
        params: 模型参数字典，包含架构相关的配置
        n_outputs: 输出维度（分类任务为类别数，回归任务通常为1）
        **kwargs: 其他参数
        
    Returns:
        实例化的 PyTorch 模型
        
    Raises:
        ValueError: 当模型名称不支持或参数无效时
        
    Examples:
        >>> # 构建分类模型
        >>> model = build_model(
        ...     model_name="CNN",
        ...     task_type="classification", 
        ...     input_size=1000,
        ...     n_outputs=5,
        ...     params={"n_filters_1": 32, "dropout_rate": 0.3}
        ... )
        
        >>> # 构建回归模型
        >>> model = build_model(
        ...     model_name="CNN",
        ...     task_type="regression",
        ...     input_size=500,
        ...     n_outputs=1,
        ...     params={"learning_rate": 0.001}
        ... )
    """
    # 参数验证
    if model_name not in SUPPORTED_ARCHITECTURES:
        supported_models = list(SUPPORTED_ARCHITECTURES.keys())
        raise ValueError(
            f"Unsupported model name: {model_name}. "
            f"Supported models: {supported_models}"
        )
    
    # 🔧 修复：将 task_type 转换为小写以支持大小写不敏感
    task_type_lower = task_type.lower()

    if task_type_lower not in ["classification", "regression"]:
        raise ValueError(
            f"task_type must be 'classification' or 'regression', got {task_type}"
        )

    if input_size <= 0:
        raise ValueError(f"input_size must be positive, got {input_size}")

    # 处理参数
    if params is None:
        params = {}

    # 自动推断输出维度
    if n_outputs is None:
        if task_type_lower == "regression":
            n_outputs = 1
        else:  # classification
            n_outputs = params.get("n_classes", 2)  # 默认二分类

    # 提取架构相关参数
    architecture_params = _extract_architecture_params(model_name, params)

    # 添加必要的参数
    architecture_params.update({
        "input_size": input_size,
        "task_type": task_type_lower,  # 🔧 使用标准化的小写版本
        "n_outputs": n_outputs,
        **kwargs
    })
    
    # 获取模型类
    model_class = SUPPORTED_ARCHITECTURES[model_name]
    
    # 实例化模型
    try:
        model = model_class(**architecture_params)
        
        logger.debug(  # 🚀 优化：降低日志级别减少噪音
            f"Successfully built {model_name} model: "
            f"task_type={task_type_lower}, input_size={input_size}, n_outputs={n_outputs}"
        )
        
        # 记录架构信息（如果模型支持）
        if hasattr(model, 'get_architecture_info'):
            arch_info = model.get_architecture_info()
            logger.debug(f"Model architecture info: {arch_info}")
        
        return model
        
    except Exception as e:
        logger.error(f"Failed to build {model_name} model: {e}")
        raise


def _extract_architecture_params(model_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    从参数字典中提取架构相关的参数。
    
    Args:
        model_name: 模型名称
        params: 完整的参数字典
        
    Returns:
        架构相关的参数字典
    """
    if model_name in ["CNN", "CNN1D"]:
        return _extract_cnn_params(params)
    else:
        # 对于未来的新架构，可以在这里添加相应的参数提取逻辑
        return params.copy()


def _extract_cnn_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    提取CNN架构相关的参数。
    
    Args:
        params: 完整的参数字典
        
    Returns:
        CNN架构相关的参数字典
    """
    # CNN架构支持的参数
    cnn_param_keys = {
        "n_filters_1", "n_filters_2", "n_filters_3",
        "kernel_size_1", "kernel_size_2", "kernel_size_3",
        "dropout_rate", "n_classes"
    }
    
    # 提取相关参数
    architecture_params = {}
    for key, value in params.items():
        if key in cnn_param_keys:
            architecture_params[key] = value
    
    # 设置默认值
    defaults = {
        "n_filters_1": 32,
        "n_filters_2": 64,
        "n_filters_3": 128,
        "kernel_size_1": 5,
        "kernel_size_2": 3,
        "kernel_size_3": 3,
        "dropout_rate": 0.5
    }
    
    for key, default_value in defaults.items():
        if key not in architecture_params:
            architecture_params[key] = default_value
    
    return architecture_params


def get_supported_models() -> list:
    """
    获取支持的模型列表。
    
    Returns:
        支持的模型名称列表
    """
    return list(SUPPORTED_ARCHITECTURES.keys())


def register_architecture(name: str, architecture_class: type) -> None:
    """
    注册新的模型架构。
    
    这个函数为未来的架构扩展提供了接口，支持动态注册新的模型类。
    
    Args:
        name: 架构名称
        architecture_class: 架构类（必须继承自 nn.Module）
        
    Raises:
        ValueError: 当架构类无效时
    """
    if not issubclass(architecture_class, nn.Module):
        raise ValueError(f"Architecture class must inherit from nn.Module")
    
    SUPPORTED_ARCHITECTURES[name] = architecture_class
    logger.info(f"Registered new architecture: {name}")


def validate_model_config(model_name: str, task_type: str, params: Dict[str, Any]) -> bool:
    """
    验证模型配置的有效性。
    
    Args:
        model_name: 模型名称
        task_type: 任务类型
        params: 参数字典
        
    Returns:
        配置是否有效
    """
    try:
        # 检查模型名称
        if model_name not in SUPPORTED_ARCHITECTURES:
            return False
        
        # 检查任务类型
        if task_type not in ["classification", "regression"]:
            return False
        
        # 检查必要参数
        if "input_size" in params and params["input_size"] <= 0:
            return False
        
        return True
        
    except Exception:
        return False


# ============================================================================
# 超参数空间定义 - 实现模型"自包含"原则
# ============================================================================

@register_model_hyperparams("CNN", "classification")
@register_model_hyperparams("CNN", "regression")
@register_model_hyperparams("CNN1D", "classification")
@register_model_hyperparams("CNN1D", "regression")
def get_cnn_hyperparams(strategy: str = "optuna", complexity: str = "medium") -> Dict[str, Any]:
    """
    获取CNN模型的超参数搜索空间。

    此函数定义了与当前CNN1DNet架构兼容的超参数，实现了模型"自包含"原则。
    超参数空间的定义权交还给模型自身，而不是集中在外部配置文件中。

    Args:
        strategy (str): 优化策略 ("grid_search", "random_search", "optuna")
        complexity (str): 复杂度级别 ("simple", "medium", "complex")

    Returns:
        Dict[str, Any]: 超参数搜索空间字典

    Example:
        >>> hyperparams = get_cnn_hyperparams("optuna", "medium")
        >>> print(hyperparams["model__n_filters_1"])
        {"type": "categorical", "choices": [16, 32, 64]}

    Note:
        - 🔧 重要：所有参数都需要 'model__' 前缀，因为它们都传递给 Pipeline 中的 model 步骤
        - 训练参数（model__learning_rate, model__batch_size, model__epochs）由 PyTorchSklearnWrapper 处理
        - 架构参数（model__n_filters_*, model__kernel_size_*, model__dropout_rate）由 model builder 处理
        - 参数名称与 CNN1DNet 构造函数和 _extract_cnn_params 函数保持一致
    """
    if complexity == "simple":
        if strategy == "grid_search":
            return {
                # 训练参数 (🔧 修复：添加 model__ 前缀)
                "model__learning_rate": [0.001, 0.01],
                "model__batch_size": [16, 32],
                "model__epochs": [20, 50],

                # 架构参数 (由model builder处理, 需加'model__'前缀)
                "model__n_filters_1": [16, 32],
                "model__n_filters_2": [32, 64],
                "model__kernel_size_1": [3, 5],
                "model__dropout_rate": [0.2, 0.3],
            }
        else:  # random_search, optuna
            return {
                # 训练参数 (🔧 修复：添加 model__ 前缀)
                "model__learning_rate": {"type": "float", "low": 0.001, "high": 0.01, "log": True},
                "model__batch_size": {"type": "categorical", "choices": [16, 32]},
                "model__epochs": {"type": "int", "low": 20, "high": 50},

                # 架构参数
                "model__n_filters_1": {"type": "categorical", "choices": [16, 32]},
                "model__n_filters_2": {"type": "categorical", "choices": [32, 64]},
                "model__kernel_size_1": {"type": "categorical", "choices": [3, 5]},
                "model__dropout_rate": {"type": "float", "low": 0.2, "high": 0.3},
            }

    elif complexity == "medium":
        if strategy == "grid_search":
            return {
                # 训练参数 (🔧 修复：添加 model__ 前缀)
                "model__learning_rate": [0.0001, 0.001, 0.01],
                "model__batch_size": [16, 32, 64],
                "model__epochs": [20, 50, 100],

                # 架构参数
                "model__n_filters_1": [16, 32, 64],
                "model__n_filters_2": [32, 64, 128],
                "model__kernel_size_1": [3, 5, 7],
                "model__kernel_size_2": [3, 5],
                "model__dropout_rate": [0.1, 0.3, 0.5],
            }
        else:  # random_search, optuna
            return {
                # 训练参数 (🔧 修复：添加 model__ 前缀)
                "model__learning_rate": {"type": "float", "low": 0.0001, "high": 0.01, "log": True},
                "model__batch_size": {"type": "categorical", "choices": [16, 32, 64]},
                "model__epochs": {"type": "int", "low": 20, "high": 100},

                # 架构参数
                "model__n_filters_1": {"type": "categorical", "choices": [16, 32, 64]},
                "model__n_filters_2": {"type": "categorical", "choices": [32, 64, 128]},
                "model__kernel_size_1": {"type": "categorical", "choices": [3, 5, 7]},
                "model__kernel_size_2": {"type": "categorical", "choices": [3, 5]},
                "model__dropout_rate": {"type": "float", "low": 0.1, "high": 0.5},
            }

    elif complexity == "complex":
        if strategy == "grid_search":
            # 对于复杂的网格搜索，减少参数组合以避免计算爆炸
            return {
                # 训练参数 (🔧 修复：添加 model__ 前缀)
                "model__learning_rate": [0.0001, 0.001, 0.01],
                "model__batch_size": [16, 32, 64],
                "model__epochs": [50, 100],

                # 架构参数
                "model__n_filters_1": [32, 64],
                "model__n_filters_2": [64, 128],
                "model__n_filters_3": [128, 256],
                "model__kernel_size_1": [3, 5, 7],
                "model__kernel_size_2": [3, 5],
                "model__kernel_size_3": [3],
                "model__dropout_rate": [0.2, 0.4],
            }
        else:  # random_search, optuna
            return {
                # 训练参数 (🔧 修复：添加 model__ 前缀)
                "model__learning_rate": {"type": "float", "low": 0.00001, "high": 0.1, "log": True},
                "model__batch_size": {"type": "categorical", "choices": [8, 16, 32, 64, 128]},
                "model__epochs": {"type": "int", "low": 30, "high": 200},

                # 架构参数
                "model__n_filters_1": {"type": "categorical", "choices": [8, 16, 32, 64, 128]},
                "model__n_filters_2": {"type": "categorical", "choices": [16, 32, 64, 128, 256]},
                "model__n_filters_3": {"type": "categorical", "choices": [32, 64, 128, 256, 512]},
                "model__kernel_size_1": {"type": "categorical", "choices": [3, 5, 7, 9]},
                "model__kernel_size_2": {"type": "categorical", "choices": [3, 5, 7]},
                "model__kernel_size_3": {"type": "categorical", "choices": [3, 5]},
                "model__dropout_rate": {"type": "float", "low": 0.0, "high": 0.7},
            }

    else:
        raise ValueError(f"不支持的复杂度级别: {complexity}. 可选项: ['simple', 'medium', 'complex']")
