"""
一个标准的Sklearn随机森林超参数优化实验配置示例。

此配置展示了如何使用Sklearn引擎和Grid Search策略进行超参数优化。
"""

EXPERIMENT_CONFIG = {
    "experiment_type": "hyperparameter_optimization",
    
    "data_config": {
        "source_type": "opensa_test",
        "type": "Cls",
        "label_col_name": "label",
        "group_col_name": None,
        "spectrum_unique_id_col_name": "spectrum_id",
        "wave_number_range": None,
        "test_size": 0.2,
        "random_seed": 42,
    },
    
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}}
        ]
    },
    
    "feature_selection_config": {
        "method": "Pca",
        "params": {"n_components": 20}
    },
    
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {
            "type": "Classification",
            "name": "RF",
            "params": {  # 基础参数，将被搜索覆盖
                "random_state": 42
            },
        },
        "hyperparameter_optimization_config": {
            "strategy": "grid_search",
            "cv_folds": 3,
            "scoring": "accuracy",
            "param_grid": {
                "model__n_estimators": [10, 50, 100],
                "model__max_depth": [3, 5, 10]
            }
        }
    },
    
    "evaluation_config": {
        "metrics": {
            "classification": ["accuracy", "precision", "recall", "f1"]
        },
        "save_predictions": True,
        "save_model": True,
    },
    
    "experiment_config": {
        "name": "sklearn_rf_hyperopt_example",
        "description": "Sklearn随机森林超参数优化示例",
        "tags": ["sklearn", "hyperopt", "random_forest", "example"],
    },
    
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models",
        "results_dir": "results",
        "logs_dir": "logs",
    },
}
