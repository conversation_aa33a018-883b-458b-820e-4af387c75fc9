# notebooks/test_imports.py

# %% [markdown]
# # 导入测试脚本
# 
# 这个脚本用于验证所有必要的模块都能正确导入。
# 如果您遇到导入问题，请先运行这个脚本进行诊断。

# %%
# 路径设置和基础导入测试
import sys
from pathlib import Path

print("🔧 Python环境诊断")
print("=" * 50)
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {Path.cwd()}")

# 智能路径检测和设置
def setup_project_paths():
    """智能检测项目根目录并设置Python路径"""
    current_dir = Path('.').resolve()

    # 尝试找到项目根目录（包含conf和src目录的目录）
    project_root = None
    search_dir = current_dir

    print(f"🔍 开始搜索项目根目录...")
    print(f"   当前目录: {current_dir}")

    # 向上搜索最多3级目录
    for level in range(3):
        print(f"   检查目录 (级别 {level}): {search_dir}")
        if (search_dir / 'conf').exists() and (search_dir / 'src').exists():
            project_root = search_dir
            print(f"   ✅ 找到项目根目录: {project_root}")
            break
        search_dir = search_dir.parent

    if project_root is None:
        print(f"   ⚠️ 未找到标准项目结构，使用当前目录")
        project_root = current_dir

    # 添加必要的路径
    paths_to_add = [
        str(project_root),           # 项目根目录
        str(project_root / 'src'),   # src目录
    ]

    print(f"\n📁 路径设置结果:")
    for i, path in enumerate(paths_to_add, 1):
        if path not in sys.path:
            sys.path.insert(0, path)
            print(f"  {i}. {path} ✅ (已添加)")
        else:
            print(f"  {i}. {path} ✅ (已存在)")

    return project_root

# 设置路径
project_root = setup_project_paths()

print(f"\n🎯 最终验证:")
print(f"  项目根目录: {project_root}")
print(f"  conf目录存在: {(project_root / 'conf').exists()}")
print(f"  src目录存在: {(project_root / 'src').exists()}")

# %%
# 测试配置模块导入
print("\n🧪 配置模块导入测试")
print("=" * 50)

try:
    from conf.base.presets import FAST_DEV_RF_PRESET
    print("✅ conf.base.presets.FAST_DEV_RF_PRESET")
    
    from conf.model_params.rf import FAST_PARAMS as RF_FAST_PARAMS
    print("✅ conf.model_params.rf.FAST_PARAMS")
    
    from conf.model_params.cnn import FAST_PARAMS as CNN_FAST_PARAMS
    print("✅ conf.model_params.cnn.FAST_PARAMS")
    
except ImportError as e:
    print(f"❌ 配置模块导入失败: {e}")
    raise

# %%
# 测试核心框架导入
print("\n🚀 核心框架导入测试")
print("=" * 50)

try:
    from src.my_spectral_framework.main import run_experiment_from_config, load_config_by_path
    print("✅ src.my_spectral_framework.main")
    
    from src.my_spectral_framework.core.utils import deep_update
    print("✅ src.my_spectral_framework.core.utils")
    
except ImportError as e:
    print(f"❌ 核心框架导入失败: {e}")
    raise

# %%
# 测试工具模块导入
print("\n🛠️ 工具模块导入测试")
print("=" * 50)

try:
    from notebooks.utils import (
        print_experiment_summary, 
        plot_cv_results, 
        print_best_hyperparams,
        compare_experiments
    )
    print("✅ notebooks.utils (所有工具函数)")
    
except ImportError as e:
    print(f"❌ 工具模块导入失败: {e}")
    raise

# %%
# 综合测试 - 运行一个简单的实验
print("\n🎯 综合功能测试")
print("=" * 50)

try:
    import copy
    
    # 加载配置
    config = copy.deepcopy(FAST_DEV_RF_PRESET)
    print("✅ 配置加载成功")
    
    # 运行实验
    results = run_experiment_from_config(config)
    print("✅ 实验执行成功")
    
    # 显示结果
    print_experiment_summary(results)
    print("✅ 结果显示成功")
    
    print("\n🎉 所有测试通过！您的环境配置正确。")
    
except Exception as e:
    print(f"❌ 综合测试失败: {e}")
    import traceback
    traceback.print_exc()

# %% [markdown]
# ## 🎉 测试完成！
# 
# 如果所有测试都通过，说明您的环境配置正确，可以正常使用示例脚本。
# 
# **如果遇到问题，请检查：**
# 1. 确保在项目根目录中运行脚本
# 2. 确保所有必要的依赖包已安装
# 3. 确保Python路径设置正确
# 
# **下一步：**
# - 运行 `01_getting_started.py` 开始学习
# - 探索 `02_cross_validation.py` 了解交叉验证
# - 尝试 `03_deep_learning_and_hyperopt.py` 体验高级功能
