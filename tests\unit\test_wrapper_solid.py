"""
SOLID原则驱动的SpectralModelWrapper测试 - 单元级精度验证
"""
import numpy as np
import pytest
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

# 导入被测试的类
from my_spectral_framework.models.classic_ml._base import SpectralModelWrapper

# 导入新的装饰器注册系统
from tests.core.test_registry import unit_test

class TestSpectralModelWrapper:
    """测试 SpectralModelWrapper 的所有功能"""

    @pytest.fixture
    def sample_data(self):
        """创建示例数据"""
        np.random.seed(42)
        X = np.random.rand(100, 50)
        y = np.random.randint(0, 3, 100)
        return X, y

    @pytest.fixture
    def sample_preprocessor(self):
        """创建示例预处理器"""
        return StandardScaler()

    @pytest.fixture
    def sample_model(self):
        """创建示例模型"""
        return RandomForestClassifier(n_estimators=10, random_state=42)

    @unit_test(
        description="[SRP] 验证 SpectralModelWrapper 的基本初始化",
        tags=["wrapper", "initialization"],
        priority="critical"
    )
    def test_wrapper_initialization(self, sample_preprocessor, sample_model):
        """职责：验证包装器能否正确接收和存储预处理器和模型组件。"""
        wrapper = SpectralModelWrapper(
            preprocessor=sample_preprocessor,
            model=sample_model,
            task_type="classification"
        )
        assert wrapper.task_type == "classification"
        assert wrapper.model is sample_model
        assert wrapper.preprocessor is sample_preprocessor
        assert not wrapper.is_fitted_

    @unit_test(
        description="[SRP] 验证 SpectralModelWrapper 在无预处理器时也能正确初始化",
        tags=["wrapper", "initialization", "edge_case"],
        priority="high"
    )
    def test_wrapper_initialization_without_preprocessor(self, sample_model):
        """职责：验证包装器在预处理器为None时的行为。"""
        wrapper = SpectralModelWrapper(
            preprocessor=None,
            model=sample_model,
            task_type="classification"
        )
        assert wrapper.preprocessor is None

    @unit_test(
        description="[SRP] 验证包装器的fit方法能成功训练预处理器和模型",
        tags=["wrapper", "training", "fit"],
        priority="critical"
    )
    def test_wrapper_training(self, sample_data, sample_preprocessor, sample_model):
        """职责：验证fit方法能正确执行预处理和模型训练的流程。"""
        X, y = sample_data
        wrapper = SpectralModelWrapper(
            preprocessor=sample_preprocessor,
            model=sample_model,
            task_type="classification"
        )
        wrapper.fit(X, y)
        assert wrapper.is_fitted_
        assert hasattr(wrapper.preprocessor_, 'mean_') # StandardScaler should have mean_
        assert hasattr(wrapper.model_, 'feature_importances_') # RF should have this

    @unit_test(
        description="[SRP] 验证包装器的predict方法能生成正确形状的预测",
        tags=["wrapper", "prediction", "predict"],
        priority="critical"
    )
    def test_wrapper_prediction(self, sample_data, sample_preprocessor, sample_model):
        """职责：验证predict方法能正确执行预处理和模型预测的流程。"""
        X, y = sample_data
        wrapper = SpectralModelWrapper(
            preprocessor=sample_preprocessor,
            model=sample_model,
            task_type="classification"
        )
        wrapper.fit(X, y)
        predictions = wrapper.predict(X)
        assert predictions.shape == (len(y),)

    @unit_test(
        description="[SRP] 验证包装器的predict_proba方法能生成正确形状的概率预测",
        tags=["wrapper", "prediction", "predict_proba"],
        priority="high"
    )
    def test_wrapper_predict_proba(self, sample_data, sample_preprocessor, sample_model):
        """职责：验证predict_proba方法能正确返回符合规范的概率分布。"""
        X, y = sample_data
        wrapper = SpectralModelWrapper(
            preprocessor=sample_preprocessor,
            model=sample_model,
            task_type="classification"
        )
        wrapper.fit(X, y)
        probabilities = wrapper.predict_proba(X)
        assert probabilities.shape == (len(y), 3)  # 3 classes in sample data
        assert np.allclose(probabilities.sum(axis=1), 1.0)

    @unit_test(
        description="[SRP] 验证在未fit时调用predict会抛出错误",
        tags=["wrapper", "error_handling", "sklearn_compat"],
        priority="normal"
    )
    def test_wrapper_raises_error_if_not_fitted(self, sample_preprocessor, sample_model):
        """职责：验证包装器遵循scikit-learn的fit-then-predict约定。"""
        wrapper = SpectralModelWrapper(
            preprocessor=sample_preprocessor,
            model=sample_model,
            task_type="classification"
        )
        X_test = np.random.rand(10, 50)
        with pytest.raises(ValueError, match="模型尚未训练"):
            wrapper.predict(X_test)
