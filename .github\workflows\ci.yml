name: Spectral Framework CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  quality-and-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install ".[dev]"

    - name: Lint with flake8, black, isort
      run: |
        flake8 src/ tests/
        black --check src/ tests/
        isort --check-only src/ tests/

    - name: Type check with mypy
      run: |
        mypy src/
    
    - name: Run Unit Tests with Coverage
      run: |
        python scripts/run_solid_tests.py --level unit --cov=src --cov-report=xml

    - name: Run Integration Tests
      run: |
        python scripts/run_solid_tests.py --level integration

    - name: Run E2E Tests
      run: |
        python scripts/run_solid_tests.py --level e2e
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella-${{ matrix.python-version }}
        fail_ci_if_error: true
