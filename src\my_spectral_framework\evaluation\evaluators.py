"""
评估器策略模块。

本模块实现了评估模型的策略模式，将不同的评估方法（如留出法、交叉验证、超参数优化）
封装成独立、可互换的策略。
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple
import time
import numpy as np
import pandas as pd
from sklearn.base import BaseEstimator
from sklearn.model_selection import train_test_split, GroupShuffleSplit

from ..core.config_models import ExperimentRootConfig
from ..core.context import ExperimentContext
from ..core.steps import EvaluationStep
from ..core.config_adapter import ConfigAdapter
from ..data.factory import get_data_source
from ..data.splitters import get_cv_splitter
from ..optimization.tuners import create_tuner
from ..reporting.handlers import BaseResultHandler
from ..core.utils import get_logger

logger = get_logger(__name__)

def load_and_split_data(config: ExperimentRootConfig, perform_split: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, list, list, np.ndarray, np.ndarray]:
    """
    适配器函数：加载数据并可选地进行训练/测试分割。

    Args:
        config: 实验配置
        perform_split: 是否进行数据分割，False时返回全量数据

    Returns:
        元组 (X_train, X_test, y_train, y_test, ids_train, ids_test, groups_train, groups_test)
        当perform_split=False时，X_test等为None
    """
    # 使用数据源工厂加载数据
    data_source = get_data_source(config.model_dump())
    X, y, groups, ids, metadata = data_source.load()

    if not perform_split:
        # 交叉验证模式：不分割数据，返回全量数据
        return X, None, y, None, ids, None, groups, None

    # 进行数据分割
    data_cfg = config.data_config
    test_size = data_cfg.test_size if data_cfg.test_size > 0 else 0.3
    random_seed = data_cfg.random_seed

    if groups is not None:
        # 如果有分组信息，使用分组感知的分割
        splitter = GroupShuffleSplit(n_splits=1, test_size=test_size, random_state=random_seed)
        train_idx, test_idx = next(splitter.split(X, y, groups))
        X_train, X_test = X[train_idx], X[test_idx]
        y_train, y_test = y[train_idx], y[test_idx]
        ids_train, ids_test = [ids[i] for i in train_idx], [ids[i] for i in test_idx]
        groups_train, groups_test = groups[train_idx], groups[test_idx]
    else:
        # 普通分割
        X_train, X_test, y_train, y_test, ids_train, ids_test = train_test_split(
            X, y, ids, test_size=test_size, random_state=random_seed, stratify=y
        )
        groups_train, groups_test = None, None

    return X_train, X_test, y_train, y_test, ids_train, ids_test, groups_train, groups_test

class BaseEvaluator(ABC):
    def __init__(self, config: ExperimentRootConfig, handler: BaseResultHandler, context: ExperimentContext):
        self.config = config
        self.handler = handler
        self.context = context
        self.adapter = ConfigAdapter(config)
        self.logger = get_logger(f"evaluators.{self.__class__.__name__}")

    @abstractmethod
    def evaluate(self, model: BaseEstimator, X: np.ndarray, y: np.ndarray, groups: np.ndarray = None, ids: list = None) -> Dict[str, Any]:
        pass

class HoldOutEvaluator(BaseEvaluator):
    """执行单次训练/验证拆分的评估器。"""
    def evaluate(self, model: BaseEstimator, X: np.ndarray, y: np.ndarray, groups: np.ndarray = None, ids: list = None) -> Dict[str, Any]:
        self.logger.info("使用留出法 (Hold-Out) 进行评估...")

        # 获取测试数据（从上下文中）
        X_test, y_test, ids_test = self.context.X_test, self.context.y_test, self.context.ids_test

        # 计时 sklearn 模型训练
        fit_start_time = time.time()
        model.fit(X, y)
        fit_time = time.time() - fit_start_time

        # 记录训练时间指标
        self.handler.log_metric("fit_time", fit_time)
        self.logger.info(f"模型训练完成，耗时: {fit_time:.2f} 秒")

        y_pred = model.predict(X_test)
        self.logger.info("预测完成。")

        eval_payload = {"y_test": y_test, "y_pred": y_pred}
        EvaluationStep(self.config.model_dump()).process(self.context, eval_payload)

        self.handler.log_artifact("final_model", model, "joblib", subdir="models")

        # 构造并保存 predictions.csv
        predictions_df = pd.DataFrame({
            "spectrum_id": ids_test if ids_test is not None else list(range(len(y_test))),
            "true_label": y_test,
            "predicted_label": y_pred
        })
        self.handler.log_artifact("predictions", predictions_df, "csv", subdir="predictions")

        return {"metrics": self.context.evaluation_results, "model": model}

class CrossValidationEvaluator(BaseEvaluator):
    """执行交叉验证的评估器。"""
    def evaluate(self, model: BaseEstimator, X: np.ndarray, y: np.ndarray, groups: np.ndarray = None, ids: list = None) -> Dict[str, Any]:
        from sklearn.model_selection import cross_validate
        self.logger.info("使用交叉验证进行评估...")

        cv_cfg = self.config.engine_config.cv_config.model_dump() if self.config.engine_config.cv_config else {}
        n_splits = cv_cfg.get('n_splits', 5)
        self.logger.info(f"正在执行 {n_splits}-折交叉验证...")
        cv_splitter = get_cv_splitter(cv_cfg, groups)

        scoring_metrics = ["accuracy", "f1_weighted"] if self.config.engine_config.ml_model_config.type == "Classification" else ["r2", "neg_mean_squared_error"]
        cv_results = cross_validate(model, X, y, cv=cv_splitter, scoring=scoring_metrics, groups=groups, return_train_score=True, n_jobs=-1)

        # 聚合结果
        aggregated_metrics = {}
        for key, values in cv_results.items():
            if key.startswith("test_"):
                metric_name = key.replace("test_", "")
                aggregated_metrics[f"cv_test_{metric_name}_mean"] = np.mean(values)
                aggregated_metrics[f"cv_test_{metric_name}_std"] = np.std(values)

        self.handler.log_metrics_batch(aggregated_metrics)
        self.context.add_evaluation_results(aggregated_metrics)

        self.logger.info("在完整数据集上重新训练最终模型...")
        model.fit(X, y)
        self.handler.log_artifact("final_model", model, "joblib", subdir="models")

        return {"metrics": aggregated_metrics, "model": model}

class HyperparameterTuningEvaluator(BaseEvaluator):
    """执行超参数优化的评估器。"""
    def evaluate(self, model: BaseEstimator, X: np.ndarray, y: np.ndarray, groups: np.ndarray = None, ids: list = None) -> Dict[str, Any]:
        self.logger.info("使用超参数优化进行评估...")

        # 获取测试数据（从上下文中）
        X_test, y_test, ids_test = self.context.X_test, self.context.y_test, self.context.ids_test
        groups_train = self.context.groups_train

        hyperopt_config = self.config.engine_config.hyperparameter_optimization_config
        cv_cfg = self.config.engine_config.cv_config.model_dump() if self.config.engine_config.cv_config else {}
        cv_splitter = get_cv_splitter(cv_cfg, groups_train)

        tuner = create_tuner(strategy=hyperopt_config.strategy, model=model, config=self.config, cv_splitter=cv_splitter, groups=groups_train)

        search = tuner.tune(X, y)
        best_model = search.best_estimator_

        self.handler.log_artifact("best_params", search.best_params_, "json", subdir="hyperopt")
        self.handler.log_metric("best_cv_score", search.best_score_)

        self.logger.info(f"在测试集上评估最佳模型...")
        y_pred = best_model.predict(X_test)
        eval_payload = {"y_test": y_test, "y_pred": y_pred}
        EvaluationStep(self.config.model_dump()).process(self.context, eval_payload)

        self.handler.log_artifact("best_model", best_model, "joblib", subdir="models")
        predictions_df = pd.DataFrame({"spectrum_id": ids_test, "true_label": y_test, "predicted_label": y_pred})
        self.handler.log_artifact("test_predictions", predictions_df, "csv", subdir="predictions")

        return {"metrics": self.context.evaluation_results, "model": best_model}

def create_evaluator(strategy: str, config: ExperimentRootConfig, handler: BaseResultHandler, context: ExperimentContext) -> BaseEvaluator:
    evaluator_map = {
        "single_run": HoldOutEvaluator,
        "cross_validation": CrossValidationEvaluator,
        "hyperparameter_optimization": HyperparameterTuningEvaluator,
    }
    if strategy not in evaluator_map:
        raise ValueError(f"不支持的评估策略/实验类型: '{strategy}'")
    return evaluator_map[strategy](config, handler, context)
