#!/usr/bin/env python3
"""
验证修复的简单脚本
"""

import json
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🔍 验证修复...")
    
    try:
        # 导入配置模型
        from src.my_spectral_framework.core.config_models import ExperimentRootConfig
        
        # 加载配置文件
        with open("config_stratified_group_cv_cnn_hyperopt.json", 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        # 解析配置
        config_obj = ExperimentRootConfig.model_validate(config_dict)
        
        # 检查cv_config
        if hasattr(config_obj.engine_config, 'cv_config') and config_obj.engine_config.cv_config:
            cv_config = config_obj.engine_config.cv_config
            print(f"✅ cv_config解析成功: {cv_config.method}, {cv_config.n_splits} 折")
            
            # 测试HyperOptRunner的_get_cv_splitter方法
            from src.my_spectral_framework.runners import HyperOptRunner
            runner = HyperOptRunner({})
            
            # 模拟groups数据
            import numpy as np
            groups = np.array([1, 1, 2, 2, 3, 3, 4, 4, 5, 5])
            
            # 调用方法
            cv_cfg = cv_config.model_dump()
            cv_splitter = runner._get_cv_splitter(cv_cfg, groups)
            
            from sklearn.model_selection import StratifiedGroupKFold
            if isinstance(cv_splitter, StratifiedGroupKFold):
                print("✅ 正确创建了StratifiedGroupKFold分割器")
                print("🎉 修复验证成功！")
                return True
            else:
                print(f"❌ 错误的分割器类型: {type(cv_splitter).__name__}")
                return False
        else:
            print("❌ cv_config未正确解析")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    print(f"验证结果: {'成功' if success else '失败'}")
    print(f"{'='*50}")
