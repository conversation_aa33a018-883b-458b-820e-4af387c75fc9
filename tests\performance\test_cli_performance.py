"""
CLI性能测试模块

这个模块包含对CLI工具的性能基准测试，包括执行时间和内存使用情况。
这些测试从端到端测试中分离出来，以避免影响功能测试的稳定性。
"""

import json
import subprocess
import sys
import time
from pathlib import Path

import pytest

try:
    import psutil
except ImportError:
    psutil = None

from tests.core.test_registry import performance_test


class TestCLIPerformance:
    """CLI性能基准测试"""

    @performance_test(
        description="测试CLI实验执行时间性能",
        tags=["cli", "performance", "execution_time"],
        priority="normal",
    )
    def test_cli_execution_time(self):
        """测试CLI实验执行时间"""
        start_time = time.time()

        cmd = [sys.executable, "scripts/run_experiment.py", "--config", "e2e_test_config"]

        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path.cwd(), timeout=180)

        end_time = time.time()
        execution_time = end_time - start_time

        # 验证实验成功完成
        assert result.returncode == 0

        # 验证执行时间在合理范围内（应该在3分钟内完成）
        assert execution_time < 180

        # 验证实验摘要中记录的执行时间
        outputs_dir = Path("outputs")
        if outputs_dir.exists():
            experiment_dirs = [d for d in outputs_dir.iterdir() if d.is_dir()]
            if experiment_dirs:
                latest_dir = max(experiment_dirs, key=lambda x: x.stat().st_mtime)
                summary_file = latest_dir / "experiment_summary.json"

                if summary_file.exists():
                    with open(summary_file, "r") as f:
                        summary = json.load(f)

                    if "execution_time" in summary:
                        recorded_time = summary["execution_time"]
                        # 记录的时间应该与实际时间相近（允许一定误差）
                        assert abs(recorded_time - execution_time) < 15

    @performance_test(
        description="测试CLI实验内存使用性能",
        tags=["cli", "performance", "memory"],
        priority="normal",
    )
    def test_cli_memory_usage(self):
        """测试CLI实验内存使用"""
        if psutil is None:
            pytest.skip("psutil not available, skipping memory test")

        cmd = [sys.executable, "scripts/run_experiment.py", "--config", "e2e_test_config"]

        # 启动进程
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, cwd=Path.cwd())

        max_memory = 0

        try:
            # 监控内存使用
            ps_process = psutil.Process(process.pid)

            while process.poll() is None:
                try:
                    memory_info = ps_process.memory_info()
                    current_memory = memory_info.rss / 1024 / 1024  # MB
                    max_memory = max(max_memory, current_memory)
                    time.sleep(0.1)
                except psutil.NoSuchProcess:
                    break

            # 等待进程完成
            stdout, stderr = process.communicate(timeout=180)

            # 验证进程成功完成
            assert process.returncode == 0

            # 验证内存使用在合理范围内（应该小于1.5GB）
            assert max_memory < 1536, f"Memory usage too high: {max_memory:.2f} MB"

        except subprocess.TimeoutExpired:
            process.kill()
            pytest.fail("Process timed out")
        except psutil.NoSuchProcess:
            # 进程已经结束，这是正常的
            pass
