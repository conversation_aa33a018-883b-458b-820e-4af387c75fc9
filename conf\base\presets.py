# conf/base/presets.py
"""
定义可复用的实验级配置预设。

此模块通过组合模型级别的参数预设来构建实验模板，
实现了实验“模板”与模型“参数集”的清晰分离，提高了配置的复用性。
"""

# 导入所需的模型级预设
from conf.model_params.cnn import ROBUST_PARAMS as CNN_ROBUST_PARAMS
from conf.model_params.rf import FAST_PARAMS as RF_FAST_PARAMS

# 预设1：用于快速开发和调试的随机森林配置。
FAST_DEV_RF_PRESET = {
    "experiment_type": "single_run",
    "data_config": {
        "source_type": "opensa_test",
        "type": "Cls",
        "test_size": 0.2,
        "random_seed": 42,
    },
    "preprocessing_config": {
        "steps": [{"method": "SNV", "params": {}}]
    },
    "feature_selection_config": {
        "method": "Pca",
        "params": {"n_components": 20}
    },
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {
            "type": "Classification",
            "name": "RF",
            # 直接调用导入的模型预设
            "params": RF_FAST_PARAMS
        },
    },
    "evaluation_config": {
        "metrics": {
            "classification": ["accuracy", "precision", "recall", "f1"],
            "regression": ["rmse", "r2", "mae"],
        },
        "save_predictions": True,
        "save_model": True,
    },
    "experiment_config": {
        "name": "fast-dev-rf-preset",
        "description": "用于快速开发的随机森林预设",
        "tags": ["fast", "development", "random_forest"],
    },
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models",
        "results_dir": "results",
        "logs_dir": "logs",
    },
}

# 预设2：用于稳健的CNN交叉验证配置。
ROBUST_CNN_CV_PRESET = {
    "experiment_config": {"name": "robust-cnn-cv-preset"},
    "experiment_type": "cross_validation",
    "cv_config": {"method": "stratified", "n_splits": 10},
    "preprocessing_config": {"steps": [{"method": "MSC", "params": {}}]},
    "ml_model_config": {
        "name": "CNN",
        # 调用导入的稳健型CNN预设
        "params": {"CNN": CNN_ROBUST_PARAMS}
    },
}
