# conf/runs/classification/experiment_cls_rf.py
"""
随机森林分类实验配置。

本配置基于 `FAST_DEV_RF_PRESET` 预设，并对预处理和特征选择步骤
进行了覆盖，展示了如何通过模块化配置组合出特定的实验流程。
"""

import copy
from conf.base.presets import FAST_DEV_RF_PRESET
from src.my_spectral_framework.core.utils import deep_update

# 1. 从一个合适的实验级预设开始。
EXPERIMENT_CONFIG = copy.deepcopy(FAST_DEV_RF_PRESET)

# 2. 定义此实验的独特之处。
#    例如，这里我们使用多步预处理和Lars特征选择。
my_overrides = {
    # 使用多步预处理流程
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}},
            {"method": "SG", "params": {"w": 11, "p": 2}}
        ]
    },
    # 使用 Lars 进行特征选择
    "feature_selection_config": {
        "method": "Lars",
        "params": {"nums": 50}
    },
    # 给出更具描述性的名称和标签
    "experiment_config": {
        "name": "cls_rf_snv_sg_lars",
        "description": "使用SNV+SG预处理和Lars特征选择的随机森林分类",
        "tags": ["classification", "random_forest", "multi_preprocess", "lars"],
    },
    "paths_config": {
        "results_dir": "results/classification",
    }
}

# 3. 将覆盖配置安全地合并到基础配置中。
deep_update(EXPERIMENT_CONFIG, my_overrides)
