"""
Simple test script to verify the spectral analysis framework.

This script performs basic tests to ensure the framework components
are working correctly.

Author: txy
License: Apache-2.0
"""

import sys
from pathlib import Path
from typing import Any, Dict

import numpy as np

# 导入新的装饰器注册系统
from tests.core.test_registry import register_test, unit_test

# 项目路径设置（现在通过包安装自动处理）
project_root = Path(__file__).parent.parent


@unit_test(description="[SRP] 验证所有核心模块都能正确导入", tags=["imports", "smoke_test", "critical"], priority="critical")
def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")

    try:
        # Core modules
        from my_spectral_framework import runners
        from my_spectral_framework.core import context, registry, utils
        from my_spectral_framework.data import loaders, splitters
        from my_spectral_framework.evaluation import metrics
        from my_spectral_framework.feature_selection import spectral_selectors
        from my_spectral_framework.models import model_factory
        from my_spectral_framework.models.classic_ml import ann, pls, rf, svm
        from my_spectral_framework.preprocessing import spectral_processors

        print("✓ All imports successful")
        return True

    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


@unit_test(
    description="[SRP] 验证预处理函数的基本功能",
    tags=["preprocessing", "smoke_test"],
    priority="high"
)
def test_preprocessing():
    """Test preprocessing functions."""
    print("Testing preprocessing...")

    try:
        from my_spectral_framework.preprocessing.spectral_processors import get_transformer

        # Create dummy data
        X = np.random.randn(10, 100)

        # Test different preprocessing methods using new factory API
        methods = ["MMS", "SS", "SNV"]  # Remove "None" as it returns None

        for method in methods:
            transformer = get_transformer(method)
            X_processed = transformer.fit_transform(X)
            assert X_processed.shape[0] == X.shape[0], f"Sample count changed for {method}"
            print(f"  ✓ {method} preprocessing works")

        # Test "None" method separately
        transformer_none = get_transformer("None")
        if transformer_none is None:
            print(f"  ✓ None preprocessing works (no transformation)")

        print("✓ Preprocessing tests passed")
        return True

    except Exception as e:
        print(f"✗ Preprocessing test failed: {e}")
        return False


@unit_test(
    description="[SRP] 验证特征选择函数的基本功能",
    tags=["feature_selection", "smoke_test"],
    priority="high"
)
def test_feature_selection():
    """Test feature selection functions."""
    print("Testing feature selection...")

    try:
        from my_spectral_framework.feature_selection.spectral_selectors import get_selector

        # Create dummy data
        X = np.random.randn(50, 100)
        y = np.random.randn(50)

        # Test different feature selection methods using new factory API
        methods = ["Lars", "Pca"]  # Remove "None" as it's handled differently

        for method in methods:
            selector = get_selector(method)
            selector.fit(X, y)
            X_selected = selector.transform(X)
            assert X_selected.shape[0] == X.shape[0], f"Sample count changed for {method}"
            print(f"  ✓ {method} feature selection works")

        print("✓ Feature selection tests passed")
        return True

    except Exception as e:
        print(f"✗ Feature selection test failed: {e}")
        return False


@unit_test(
    description="[SRP] 验证模型实例创建功能",
    tags=["models", "smoke_test"],
    priority="high"
)
def test_models():
    """Test model instance creation."""
    print("Testing models...")

    try:
        from my_spectral_framework.models.model_factory import get_model_instance
        from sklearn.pipeline import Pipeline

        # Create dummy classification data
        X_train = np.random.randn(50, 20)
        y_train = np.random.randint(0, 3, 50)

        # Test RF classifier instance
        model_instance = get_model_instance("Classification", "RF")
        assert model_instance is not None, "Model instance creation failed"

        # Create a simple pipeline and test training
        pipeline = Pipeline([("model", model_instance)])
        pipeline.fit(X_train, y_train)
        predictions = pipeline.predict(X_train)
        assert len(predictions) == len(y_train), "Prediction length mismatch"
        print("  ✓ RF classifier pipeline works")

        print("✓ Model tests passed")
        return True

    except Exception as e:
        print(f"✗ Model test failed: {e}")
        return False


@unit_test(
    description="[SRP] 验证评估指标计算功能",
    tags=["evaluation", "metrics", "smoke_test"],
    priority="high"
)
def test_evaluation():
    """Test evaluation metrics."""
    print("Testing evaluation...")

    try:
        from my_spectral_framework.evaluation.metrics import calculate_metrics

        # Test classification metrics
        y_true_cls = np.array([0, 1, 2, 0, 1, 2])
        y_pred_cls = np.array([0, 1, 1, 0, 2, 2])

        cls_metrics = calculate_metrics(y_true_cls, y_pred_cls, "classification")
        assert "accuracy" in cls_metrics, "Accuracy metric missing"
        print("  ✓ Classification metrics work")

        # Test regression metrics
        y_true_reg = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        y_pred_reg = np.array([1.1, 2.1, 2.9, 3.8, 5.2])

        reg_metrics = calculate_metrics(y_true_reg, y_pred_reg, "regression")
        assert "rmse" in reg_metrics, "RMSE metric missing"
        assert "r2" in reg_metrics, "R2 metric missing"
        print("  ✓ Regression metrics work")

        print("✓ Evaluation tests passed")
        return True

    except Exception as e:
        print(f"✗ Evaluation test failed: {e}")
        return False


@unit_test(
    description="[SRP] 验证配置加载功能",
    tags=["configuration", "smoke_test"],
    priority="normal"
)
def test_configuration():
    """Test configuration loading."""
    print("Testing configuration...")

    try:
        from conf.base.parameters import DEFAULT_PARAMS
        from conf.runs.experiment_cls_rf import EXPERIMENT_CONFIG
        from conf.runs.experiment_cls_rf_cv import EXPERIMENT_CONFIG as CV_CONFIG

        # Check that default params exist
        assert isinstance(DEFAULT_PARAMS, dict), "DEFAULT_PARAMS is not a dict"
        assert "data_config" in DEFAULT_PARAMS, "data_config missing"
        print("  ✓ Default parameters loaded")

        # Check experiment config
        assert isinstance(EXPERIMENT_CONFIG, dict), "EXPERIMENT_CONFIG is not a dict"
        assert "model_config" in EXPERIMENT_CONFIG, "model_config missing"
        print("  ✓ Single run experiment configuration loaded")

        # Check CV config
        assert isinstance(CV_CONFIG, dict), "CV_CONFIG is not a dict"
        assert CV_CONFIG.get("experiment_type") == "cross_validation", "CV experiment_type missing"
        assert "cv_splits" in CV_CONFIG, "cv_splits missing"
        print("  ✓ Cross-validation experiment configuration loaded")

        print("✓ Configuration tests passed")
        return True

    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


@unit_test(
    description="[SRP] 验证新功能如上下文和产物保存",
    tags=["new_features", "context", "artifacts"],
    priority="normal"
)
def test_new_features():
    """Test new features like context and artifact saving."""
    print("Testing new features...")

    try:
        import os
        import tempfile

        from my_spectral_framework.core.context import ExperimentContext
        from my_spectral_framework.core.utils import save_artifact

        # Test ExperimentContext
        config = {"test": "config"}
        context = ExperimentContext(config)

        assert context.run_id is not None, "run_id not generated"
        assert context.config == config, "config not stored correctly"
        print("  ✓ ExperimentContext creation works")

        # Test metadata addition
        context.add_data_metadata({"n_samples": 100, "n_features": 50})
        context.add_split_metadata((80, 50), (20, 50), "random", 0.2, 42)
        assert "n_samples" in context.data_metadata, "data metadata not stored"
        assert context.split_metadata["split_method"] == "random", "split metadata not stored"
        print("  ✓ Metadata addition works")

        # Test to_dict conversion
        summary = context.to_dict()
        assert isinstance(summary, dict), "to_dict doesn't return dict"
        assert "run_id" in summary, "run_id missing from summary"
        print("  ✓ Context serialization works")

        print("✓ New features tests passed")
        return True

    except Exception as e:
        print(f"✗ New features test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 50)
    print("TESTING MY SPECTRAL FRAMEWORK V2")
    print("=" * 50)

    tests = [
        test_imports,
        test_configuration,
        test_new_features,
        test_preprocessing,
        test_feature_selection,
        test_models,
        test_evaluation,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            print()

    print("=" * 50)
    print(f"TEST RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Framework is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")

    print("=" * 50)

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
