"""
函数注册系统，用于光谱分析框架。

这个模块提供了一个简单的注册系统，用于动态加载预处理、特征选择和模型训练函数。
这种设计使得框架能够灵活地进行扩展，方便用户添加自定义功能。

Author: txy
License: Apache-2.0
"""

import functools
from typing import Any, Callable, Dict, Optional

# 全局注册表，用于存储不同类型的函数
# PREPROCESSING_REGISTRY: 预处理函数注册表
PREPROCESSING_REGISTRY: Dict[str, Callable] = {}
# FEATURE_SELECTION_REGISTRY: 特征选择函数注册表
FEATURE_SELECTION_REGISTRY: Dict[str, Callable] = {}
# MODEL_REGISTRY: 模型创建函数注册表
MODEL_REGISTRY: Dict[str, Callable] = {}
# MODEL_PARAMS_REGISTRY: 模型默认参数获取函数注册表
MODEL_PARAMS_REGISTRY: Dict[str, Callable] = {}
# HYPERPARAMS_REGISTRY: 模型超参数空间获取函数注册表
HYPERPARAMS_REGISTRY: Dict[str, Callable] = {}
# RUNNER_REGISTRY: 实验运行器注册表
RUNNER_REGISTRY: Dict[str, Callable] = {}
# RESULT_HANDLER_REGISTRY: 结果处理器注册表
RESULT_HANDLER_REGISTRY: Dict[str, Callable] = {}


def register_preprocessing(name: str):
    """
    用于注册预处理函数的装饰器。
    装饰器是一种特殊的函数，它接受一个函数作为输入，并返回一个新的函数。
    在这里，它用于将预处理函数添加到全局的 PREPROCESSING_REGISTRY 中。

    Args:
        name (str): 要注册的函数的名称。这个名称将作为字典的键，用于后续查找函数。

    Example:
        @register_preprocessing("custom_normalize")
        def my_normalize_function(data, params=None):
            # 这是一个自定义的归一化函数
            return normalized_data
    """

    def decorator(func: Callable) -> Callable:
        # 将传入的函数 func 注册到 PREPROCESSING_REGISTRY 中，键为 name
        PREPROCESSING_REGISTRY[name] = func

        # functools.wraps(func) 是一个装饰器，用于保留原始函数的元数据（如函数名、文档字符串等）
        # 这样，即使函数被装饰器包装了，它的元数据仍然是原始函数的元数据，方便调试和文档生成。
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # wrapper 函数是实际被调用的函数。它会调用原始的 func 函数，并返回其结果。
            # *args 和 **kwargs 允许 wrapper 函数接受任意数量的位置参数和关键字参数，并将它们传递给 func。
            return func(*args, **kwargs)

        return wrapper

    return decorator


def register_feature_selection(name: str):
    """
    用于注册特征选择函数的装饰器。
    与 register_preprocessing 类似，它将特征选择函数添加到全局的 FEATURE_SELECTION_REGISTRY 中。

    Args:
        name (str): 要注册的函数的名称。

    Example:
        @register_feature_selection("custom_selector")
        def my_selector_function(X, y, params=None):
            # 这是一个自定义的特征选择函数
            return selected_X, selected_indices
    """

    def decorator(func: Callable) -> Callable:
        # 将传入的函数 func 注册到 FEATURE_SELECTION_REGISTRY 中，键为 name
        FEATURE_SELECTION_REGISTRY[name] = func

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator


def register_model(name: str, model_type: str):
    """
    用于注册模型创建函数的装饰器。
    """

    def decorator(func: Callable) -> Callable:
        key = f"{model_type.lower()}_{name.upper()}"
        MODEL_REGISTRY[key] = func

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator


def register_model_params(name: str, model_type: str):
    """
    用于注册模型默认参数获取函数的装饰器。
    """

    def decorator(func: Callable) -> Callable:
        key = f"{model_type.lower()}_{name.upper()}"
        MODEL_PARAMS_REGISTRY[key] = func

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator


def register_model_hyperparams(name: str, model_type: str):
    """
    用于注册模型超参数空间获取函数的装饰器。

    这个装饰器允许模型自己定义和注册超参数搜索空间，实现真正的"自包含"插件。
    超参数空间的定义权交还给模型自身，而不是集中在外部配置文件中。

    Args:
        name (str): 模型名称（如 "RF", "SVM"）
        model_type (str): 模型类型（"classification" 或 "regression"）

    Example:
        @register_model_hyperparams("RF", "classification")
        def get_rf_hyperparams(strategy="grid_search", complexity="medium"):
            if strategy == "grid_search":
                return {
                    "model__n_estimators": [50, 100, 200],
                    "model__max_depth": [10, 20, None]
                }
            # ... 其他策略
    """

    def decorator(func: Callable) -> Callable:
        key = f"{model_type.lower()}_{name.upper()}"
        HYPERPARAMS_REGISTRY[key] = func

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator


def register_runner(name: str):
    """
    用于注册实验运行器的装饰器。

    这个装饰器允许将框架的"指挥大脑"（Runner）纳入统一的插件化架构中。
    解耦框架的命令行入口与具体的实验类型实现，使得未来添加新的实验范式
    （如在线学习、增量学习）只需创建一个新的插件文件，而无需修改核心脚本。

    Args:
        name (str): 运行器名称（如 "single_run", "cross_validation", "hyperparameter_optimization"）

    Example:
        @register_runner("single_run")
        class SingleRunRunner(BaseRunner):
            def run(self):
                # 单次运行实验逻辑
                pass
    """

    def decorator(cls: type) -> type:
        RUNNER_REGISTRY[name] = cls

        @functools.wraps(cls)
        def wrapper(*args, **kwargs):
            return cls(*args, **kwargs)

        return cls  # 返回原始类，而不是包装器

    return decorator


def register_result_handler(name: str):
    """
    用于注册结果处理器的装饰器。

    这个装饰器允许将框架的"报告系统"（Handler）纳入统一的插件化架构中。
    解耦结果报告逻辑，允许用户或开发者轻松集成新的MLOps平台（如 Weights & Biases）
    或自定义的报告后端，增强框架的生态适应性。

    Args:
        name (str): 处理器名称（如 "filesystem", "mlflow", "wandb"）

    Example:
        @register_result_handler("filesystem")
        class FileSystemResultHandler(BaseResultHandler):
            def log_metric(self, key, value):
                # 文件系统存储逻辑
                pass
    """

    def decorator(cls: type) -> type:
        RESULT_HANDLER_REGISTRY[name] = cls

        @functools.wraps(cls)
        def wrapper(*args, **kwargs):
            return cls(*args, **kwargs)

        return cls  # 返回原始类，而不是包装器

    return decorator


def get_preprocessing_function(name: str) -> Callable:
    """
    根据名称获取已注册的预处理函数。
    这个函数允许用户通过之前注册的名称来获取对应的预处理函数，以便在代码中调用。

    Args:
        name (str): 要获取的预处理函数的名称。

    Returns:
        Callable: 对应的预处理函数。

    Raises:
        KeyError: 如果在注册表中找不到指定名称的预处理函数，则抛出 KeyError。
                  错误信息会列出所有可用的预处理函数名称，方便用户查找。
    """
    # 检查指定名称的函数是否存在于 PREPROCESSING_REGISTRY 中
    if name not in PREPROCESSING_REGISTRY:
        # 如果不存在，则抛出 KeyError，并提供详细的错误信息和可用函数列表
        raise KeyError(f"预处理函数 '{name}' 未在注册表中找到。 " f"可用函数: {list(PREPROCESSING_REGISTRY.keys())}")
    # 如果找到，则返回对应的函数
    return PREPROCESSING_REGISTRY[name]


def get_feature_selection_function(name: str) -> Callable:
    """
    根据名称获取已注册的特征选择函数。
    与 get_preprocessing_function 类似，用于获取特征选择函数。

    Args:
        name (str): 要获取的特征选择函数的名称。

    Returns:
        Callable: 对应的特征选择函数。

    Raises:
        KeyError: 如果在注册表中找不到指定名称的特征选择函数，则抛出 KeyError。
                  错误信息会列出所有可用的特征选择函数名称。
    """
    # 检查指定名称的函数是否存在于 FEATURE_SELECTION_REGISTRY 中
    if name not in FEATURE_SELECTION_REGISTRY:
        # 如果不存在，则抛出 KeyError，并提供详细的错误信息和可用函数列表
        raise KeyError(f"特征选择函数 '{name}' 未在注册表中找到。 " f"可用函数: {list(FEATURE_SELECTION_REGISTRY.keys())}")
    # 如果找到，则返回对应的函数
    return FEATURE_SELECTION_REGISTRY[name]


def get_model_function(name: str, model_type: str) -> Callable:
    """
    根据名称和类型获取已注册的模型创建函数。
    """
    key = f"{model_type.lower()}_{name.upper()}"
    if key not in MODEL_REGISTRY:
        available_models = [k for k in MODEL_REGISTRY.keys() if k.startswith(model_type.lower())]
        raise KeyError(f"模型函数 '{key}' 未在注册表中找到。 " f"可用 {model_type} 模型: {available_models}")
    return MODEL_REGISTRY[key]


def get_model_params_function(name: str, model_type: str) -> Callable:
    """
    根据名称和类型获取已注册的模型默认参数获取函数。
    """
    key = f"{model_type.lower()}_{name.upper()}"
    if key not in MODEL_PARAMS_REGISTRY:
        raise KeyError(f"模型参数函数 '{key}' 未在注册表中找到。")
    return MODEL_PARAMS_REGISTRY[key]


def get_model_hyperparams_function(name: str, model_type: str) -> Callable:
    """
    根据名称和类型获取已注册的模型超参数空间获取函数。

    这个函数允许优化器动态获取模型的超参数搜索空间，实现了优化器与具体
    参数空间实现的解耦。

    Args:
        name (str): 模型名称（如 "RF", "SVM"）
        model_type (str): 模型类型（"classification" 或 "regression"）

    Returns:
        Callable: 超参数空间获取函数

    Raises:
        KeyError: 如果在注册表中找不到指定的超参数空间函数

    Example:
        >>> hyperparams_func = get_model_hyperparams_function("RF", "classification")
        >>> param_grid = hyperparams_func(strategy="grid_search", complexity="medium")
    """
    key = f"{model_type.lower()}_{name.upper()}"
    if key not in HYPERPARAMS_REGISTRY:
        available_hyperparams = [k for k in HYPERPARAMS_REGISTRY.keys() if k.startswith(model_type.lower())]
        raise KeyError(
            f"模型超参数函数 '{key}' 未在注册表中找到。 "
            f"可用 {model_type} 模型超参数: {available_hyperparams}"
        )
    return HYPERPARAMS_REGISTRY[key]


def get_runner_class(name: str) -> type:
    """
    根据名称获取已注册的实验运行器类。

    这个函数实现了框架命令行入口与具体实验类型实现的解耦，
    使得动态的 Runner 分发机制成为可能。

    Args:
        name (str): 运行器名称（如 "single_run", "cross_validation"）

    Returns:
        type: 运行器类

    Raises:
        KeyError: 如果在注册表中找不到指定的运行器

    Example:
        >>> runner_class = get_runner_class("single_run")
        >>> runner = runner_class(config, engine)
        >>> results = runner.run()
    """
    if name not in RUNNER_REGISTRY:
        available_runners = list(RUNNER_REGISTRY.keys())
        raise KeyError(
            f"运行器 '{name}' 未在注册表中找到。 "
            f"可用运行器: {available_runners}"
        )
    return RUNNER_REGISTRY[name]


def get_result_handler_class(name: str) -> type:
    """
    根据名称获取已注册的结果处理器类。

    这个函数实现了结果报告逻辑的解耦，允许用户或开发者轻松集成
    新的MLOps平台或自定义的报告后端。

    Args:
        name (str): 处理器名称（如 "filesystem", "mlflow"）

    Returns:
        type: 处理器类

    Raises:
        KeyError: 如果在注册表中找不到指定的处理器

    Example:
        >>> handler_class = get_result_handler_class("filesystem")
        >>> handler = handler_class(context)
        >>> handler.log_metric("accuracy", 0.95)
    """
    if name not in RESULT_HANDLER_REGISTRY:
        available_handlers = list(RESULT_HANDLER_REGISTRY.keys())
        raise KeyError(
            f"结果处理器 '{name}' 未在注册表中找到。 "
            f"可用处理器: {available_handlers}"
        )
    return RESULT_HANDLER_REGISTRY[name]


def list_registered_functions() -> Dict[str, list]:
    """
    列出所有按类别注册的函数。
    """
    return {
        "preprocessing": list(PREPROCESSING_REGISTRY.keys()),
        "feature_selection": list(FEATURE_SELECTION_REGISTRY.keys()),
        "models": list(MODEL_REGISTRY.keys()),
        "model_params": list(MODEL_PARAMS_REGISTRY.keys()),
        "model_hyperparams": list(HYPERPARAMS_REGISTRY.keys()),
        "runners": list(RUNNER_REGISTRY.keys()),
        "result_handlers": list(RESULT_HANDLER_REGISTRY.keys()),
    }


def list_registered_models() -> list:
    """
    获取所有已注册的模型键的列表。
    这个函数专门用于列出所有已注册的模型，包括它们的类型和名称组合。

    Returns:
        list: 所有已注册模型键的列表。例如：["classification_custom_classifier", "regression_linear_model"]
    """
    return list(MODEL_REGISTRY.keys())


def clear_registry(registry_type: str = "all") -> None:
    """
    清除一个或所有注册表中的函数。
    """
    global PREPROCESSING_REGISTRY, FEATURE_SELECTION_REGISTRY, MODEL_REGISTRY, MODEL_PARAMS_REGISTRY, HYPERPARAMS_REGISTRY, RUNNER_REGISTRY, RESULT_HANDLER_REGISTRY

    if registry_type == "all":
        PREPROCESSING_REGISTRY.clear()
        FEATURE_SELECTION_REGISTRY.clear()
        MODEL_REGISTRY.clear()
        MODEL_PARAMS_REGISTRY.clear()
        HYPERPARAMS_REGISTRY.clear()
        RUNNER_REGISTRY.clear()
        RESULT_HANDLER_REGISTRY.clear()
    elif registry_type == "preprocessing":
        PREPROCESSING_REGISTRY.clear()
    elif registry_type == "feature_selection":
        FEATURE_SELECTION_REGISTRY.clear()
    elif registry_type == "models":
        MODEL_REGISTRY.clear()
        MODEL_PARAMS_REGISTRY.clear()
        HYPERPARAMS_REGISTRY.clear()
    elif registry_type == "runners":
        RUNNER_REGISTRY.clear()
    elif registry_type == "result_handlers":
        RESULT_HANDLER_REGISTRY.clear()
    else:
        raise ValueError(f"未知的注册表类型: {registry_type}")


def register_function(func: Callable, name: str, category: str, model_type: Optional[str] = None) -> None:
    """
    手动注册一个函数。
    """
    if category == "preprocessing":
        PREPROCESSING_REGISTRY[name] = func
    elif category == "feature_selection":
        FEATURE_SELECTION_REGISTRY[name] = func
    elif category == "model":
        if model_type is None:
            raise ValueError("注册模型时，model_type 是必需的参数。")
        key = f"{model_type.lower()}_{name.upper()}"
        MODEL_REGISTRY[key] = func
    elif category == "model_params":
        if model_type is None:
            raise ValueError("注册模型参数时，model_type 是必需的参数。")
        key = f"{model_type.lower()}_{name.upper()}"
        MODEL_PARAMS_REGISTRY[key] = func
    else:
        raise ValueError(f"未知的类别: {category}")


# 示例用法和测试函数
def _example_preprocessing_function(data, params=None):
    """
    用于测试的示例预处理函数。
    这个函数不执行任何实际的预处理操作，只是简单地返回输入数据。
    """
    return data


def _example_feature_selection_function(X, y, params=None):
    """
    用于测试的示例特征选择函数。
    这个函数不执行任何实际的特征选择操作，只是简单地返回输入数据 X 和 None。
    """
    return X, None


def _example_model_function(X_train, y_train, params=None):
    """
    用于测试的示例模型函数。
    这个函数不执行任何实际的模型训练操作，只是简单地返回 None。
    """
    return None


# 注册示例函数，以便进行测试
# 注册一个名为 "example" 的预处理函数
register_function(_example_preprocessing_function, "example", "preprocessing")
# 注册一个名为 "example" 的特征选择函数
register_function(_example_feature_selection_function, "example", "feature_selection")
# 注册一个名为 "example" 的分类模型函数
register_function(_example_model_function, "example", "model", "classification")
