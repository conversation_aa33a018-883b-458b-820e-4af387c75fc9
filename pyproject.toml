[project]
name = "my-spectral-framework"
version = "3.2.0"
description = "API-first spectral data analysis framework with composable architecture"
readme = "README.md"
requires-python = ">=3.9"
license = { text = "Apache-2.0" }
authors = [
  { name = "[课题组光谱分析团队]" }
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: Chemistry",
    "Topic :: Scientific/Engineering :: Physics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence"
]
dependencies = [
    # Core scientific computing
    "numpy>=1.21.0",
    "scipy>=1.7.0",
    "pandas>=1.3.0",
    # Machine learning
    "scikit-learn>=1.0.0",
    "joblib>=1.0.0",
    # Deep learning dependencies moved to smart installer
    # "torch>=1.9.0",        # Removed - installed via smart installer
    # "torchvision>=0.10.0", # Removed - installed via smart installer
    # Progress bars and utilities
    "tqdm>=4.62.0",
    # Configuration and logging
    "pyyaml>=5.4.0", # Added based on config_models.py
    "pydantic>=2.0.0",
    # Hyperparameter optimization
    "optuna>=4.4.0",
    "optuna-integration[sklearn]>=4.0.0", # Required for OptunaSearchCV and sklearn integration
]

[project.optional-dependencies]
dev = [
    "pytest>=6.2.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "isort>=5.0.0", # Added based on ci.yml linting
    "mypy>=0.900", # Added based on ci.yml linting
    "pytest-xdist>=2.5.0", # For parallel test execution (used in CI)
    "pytest-benchmark>=3.4.1", # For performance testing (used in CI)
    "optuna-integration[sklearn]>=4.0.0", # For OptunaSearchCV and sklearn integration (dev testing)
    "mlflow>=2.0.0", # For MLflowResultHandler (optional, in handlers.py)
    "kennard-stone>=0.1.0" # Optional, for advanced CV method (mentioned in splitters.py)
]
docs = [
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=0.5.0"
]

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]

[tool.pytest.ini_options]
# Test discovery config - aligns with pytest.ini
testpaths = "tests"
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
markers = [
    "unit: unit tests",
    "integration: integration tests",
    "e2e: end-to-end tests",
    "slow: slow tests",
    "benchmark: performance benchmark tests"
]
addopts = "-v --strict-markers --strict-config --tb=short --disable-warnings"

[tool.black]
line-length = 127
include = '\.(pyi|py)$'
exclude = '''
/(
    \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | build
    | dist
    | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 127
known_first_party = "my_spectral_framework"
skip_glob = ["**/__init__.py"] # skip __init__.py files as they are often simple package declarations

[tool.mypy]
python_version = "3.9"
ignore_missing_imports = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable_code = true
disallow_untyped_defs = false # Allow untyped function definitions for flexibility
check_untyped_defs = true
implicit_reexport = false

[tool.coverage.run]
source = ["src/my_spectral_framework"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\\\bProtocol\\\\):",
    "@\\\\(abc\\\\.\\\\)?abstractmethod"
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
