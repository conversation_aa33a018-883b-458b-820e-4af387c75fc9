# tests/e2e/test_e2e_config_runner.py
"""
Comprehensive End-to-End test suite for various common configurations.
This test is parameterized to run multiple configs with the same test logic.

This test suite validates that all core configuration files can be successfully
loaded and executed by the framework, ensuring system stability and reliability.


License: Apache-2.0
"""
import unittest
import time
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
if str(project_root / "src") not in sys.path:
    sys.path.insert(0, str(project_root / "src"))

from src.my_spectral_framework.main import load_config_by_path, run_experiment_from_config
from tests.core.test_registry import e2e_test


class TestEndToEndConfigRunner(unittest.TestCase):
    """Comprehensive E2E test suite for configuration validation."""

    # --- Test Matrix: Define all configurations to be tested here ---
    # Rationale: Easy to add or remove test cases by just editing this list.
    # This comprehensive matrix covers different engines, experiment types, and scenarios.
    CONFIGS_TO_TEST = [
        # === Refactored Configurations (New Preset System) ===
        "conf.runs.classification.experiment_cls_rf",  # Basic classification with presets

        # === Traditional Sklearn Engine Configurations ===
        "conf.runs.sklearn_rf_cls_example",           # Single run classification
        "conf.runs.sklearn_rf_cv_example",            # Cross-validation
        "conf.runs.sklearn_rf_hyperopt_example",      # Hyperparameter optimization

        # === PyTorch Engine Configurations ===
        "conf.runs.pytorch_cnn_cls_example",          # PyTorch CNN classification

        # Note: Additional configurations can be easily added here as they become available
        # Examples of what could be added:
        # "conf.runs.regression.experiment_rgs_cnn",
        # "conf.runs.test_pytorch_unified",
        # "conf.runs.classification.experiment_cls_rf_preset_example",
    ]

    @e2e_test(
        description="[E2E] 验证所有核心配置文件能被正确加载和执行",
        tags=["e2e", "config", "comprehensive", "stability", "critical"],
        priority="critical"
    )
    def test_run_all_configurations(self):
        """
        Iterates through the CONFIGS_TO_TEST matrix, loads each config,
        and runs the experiment, checking for successful completion.
        
        This test uses subtests to provide individual reports for each
        configuration file, making it easy to identify which specific
        config might be failing.
        """
        total_configs = len(self.CONFIGS_TO_TEST)
        passed_configs = 0
        failed_configs = []
        
        print(f"\n🚀 Starting comprehensive E2E test for {total_configs} configurations...")
        print("=" * 80)
        
        for i, config_path in enumerate(self.CONFIGS_TO_TEST, 1):
            # Use subtests to get a separate report for each config file
            with self.subTest(config=config_path):
                start_time = time.time()
                print(f"\n[{i}/{total_configs}] Testing: {config_path}")
                print("-" * 60)

                try:
                    # 1. Load the configuration
                    print("  📋 Loading configuration...")
                    config_obj = load_config_by_path(config_path)
                    self.assertIsNotNone(config_obj, "Config object should not be None.")
                    self.assertIsInstance(config_obj, dict, "Config should be a dictionary.")
                    
                    # Extract key info for logging
                    exp_name = config_obj.get("experiment_config", {}).get("name", "unnamed")
                    exp_type = config_obj.get("experiment_type", "unknown")
                    print(f"    ✓ Loaded: {exp_name} ({exp_type})")

                    # 2. Run the experiment
                    print("  🔬 Running experiment...")
                    results = run_experiment_from_config(config_obj)
                    self.assertIsInstance(results, dict, "Runner should return a results dictionary.")
                    self.assertTrue(results, "Results dictionary should not be empty.")
                    self.assertEqual(results.get("status"), "success", "Experiment should complete successfully")
                    
                    duration = time.time() - start_time
                    print(f"    ✓ Completed successfully in {duration:.2f}s")
                    print(f"  ✅ PASSED: {config_path}")
                    passed_configs += 1
                    
                except Exception as e:
                    duration = time.time() - start_time
                    print(f"    ❌ Failed after {duration:.2f}s: {str(e)}")
                    print(f"  ❌ FAILED: {config_path}")
                    failed_configs.append(config_path)
                    # Re-raise to fail the subtest
                    raise

        # Final summary
        print("\n" + "=" * 80)
        print(f"📊 E2E Test Summary:")
        print(f"  Total configurations tested: {total_configs}")
        print(f"  Passed: {passed_configs}")
        print(f"  Failed: {len(failed_configs)}")
        
        if failed_configs:
            print(f"  Failed configurations:")
            for config in failed_configs:
                print(f"    - {config}")
        else:
            print("  🎉 All configurations passed successfully!")

    @e2e_test(
        description="[E2E] 验证配置加载机制的健壮性和错误处理",
        tags=["e2e", "config", "error_handling", "robustness"],
        priority="high"
    )
    def test_config_loading_robustness(self):
        """
        Test the robustness of configuration loading mechanism.
        This includes testing error handling for invalid configurations.
        """
        print("\n🔍 Testing configuration loading robustness...")

        # Test loading a non-existent configuration
        with self.assertRaises(ImportError):
            load_config_by_path("conf.runs.nonexistent_config")

        # Test that all valid configurations can be loaded without execution
        for config_path in self.CONFIGS_TO_TEST:
            with self.subTest(config_loading=config_path):
                try:
                    config_obj = load_config_by_path(config_path)
                    self.assertIsInstance(config_obj, dict, f"Config {config_path} should be a dictionary")
                    self.assertIn("experiment_config", config_obj, f"Config {config_path} should have experiment_config")
                    print(f"    ✓ Successfully loaded: {config_path}")
                except Exception as e:
                    self.fail(f"Failed to load config {config_path}: {e}")

        print("  ✅ Configuration loading robustness test passed!")


if __name__ == '__main__':
    unittest.main(verbosity=2)
