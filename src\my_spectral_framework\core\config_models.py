"""
使用 Pydantic 的类型安全配置模型。

此模块提供了强类型的配置类，可以在加载实验配置时进行验证，
从而防止运行时错误并提供出色的 IDE 支持。
此版本使用判别联合（Discriminated Unions）来优雅地处理不同引擎的配置。

作者: txy
许可证: Apache-2.0
"""
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Tuple, Union

from pydantic import BaseModel, Field, validator


# --- 枚举类型 ---
class SourceType(str, Enum):
    SIMPLE_FILE = "simple_file"
    ADVANCED_FILE = "advanced_file"
    OPENSA_TEST = "opensa_test"

class ExperimentType(str, Enum):
    SINGLE_RUN = "single_run"
    CROSS_VALIDATION = "cross_validation"
    HYPERPARAMETER_OPTIMIZATION = "hyperparameter_optimization"

class ModelTaskType(str, Enum):
    CLASSIFICATION = "Classification"
    REGRESSION = "Regression"

class EngineType(str, Enum):
    SKLEARN = "sklearn"
    PYTORCH = "pytorch"


# --- 共享配置块 ---

class TraceabilityConfig(BaseModel):
    level: str = "none"  # 可选值: "none", "basic", "full"

    @validator('level')
    def check_level(cls, v):
        allowed_levels = ["none", "basic", "full"]
        if v not in allowed_levels:
            raise ValueError(f"无效的溯源级别. 可选值为: {allowed_levels}")
        return v

class DataConfig(BaseModel):
    source_type: SourceType = SourceType.OPENSA_TEST
    spectrum_id_strategy: str = "auto_generate"
    type: str = "Cls"
    file_path: Optional[str] = None
    label_col_name: str = "label"
    group_col_name: Optional[str] = None
    spectrum_unique_id_col_name: str = "spectrum_id"
    wave_number_range: Optional[Tuple[float, float]] = None
    test_size: float = Field(0.2, ge=0.0, le=1.0)
    random_seed: int = Field(42, ge=0)

    @validator('spectrum_unique_id_col_name')
    def check_id_col_name(cls, v, values):
        if values.get('spectrum_id_strategy') == 'from_column' and not v:
            raise ValueError("当 strategy 为 'from_column' 时, 'spectrum_unique_id_col_name' 不能为空")
        return v

class SinglePreprocessingStep(BaseModel):
    method: str
    params: Dict[str, Any] = Field(default_factory=dict)

class PreprocessingConfig(BaseModel):
    steps: List[SinglePreprocessingStep] = Field(default_factory=lambda: [SinglePreprocessingStep(method="SNV", params={})])

class FeatureSelectionConfig(BaseModel):
    method: str = "Pca"
    params: Dict[str, Any] = Field(default_factory=dict)

class ExperimentConfig(BaseModel):
    name: str = "unnamed_experiment"
    description: str = ""
    tags: List[str] = Field(default_factory=list)
    result_handler: str = "filesystem"  # 🚀 新增：支持动态结果处理器配置

class EvaluationConfig(BaseModel):
    metrics: Dict[str, List[str]] = Field(default_factory=lambda: {
        "classification": ["accuracy", "precision", "recall", "f1"],
        "regression": ["rmse", "r2", "mae"],
    })
    save_predictions: bool = True
    save_model: bool = True

class PathsConfig(BaseModel):
    data_dir: str = "data"
    models_dir: str = "models"
    results_dir: str = "results"
    logs_dir: str = "logs"


# --- 引擎特定配置块 ---

class ModelConfig(BaseModel):
    """传统机器学习模型配置 (用于Sklearn引擎)"""
    type: ModelTaskType = ModelTaskType.CLASSIFICATION
    name: str = "RF"
    params: Dict[str, Any] = Field(default_factory=dict)

class CVConfig(BaseModel):
    method: str = "stratified"
    n_splits: int = Field(5, ge=2)
    random_state: int = Field(42, ge=0)

class HyperparameterOptimizationConfig(BaseModel):
    strategy: str = "grid_search"
    cv_folds: int = Field(3, ge=2)
    scoring: str = "accuracy"
    param_grid: Optional[Dict[str, Any]] = None
    # 新增：支持 Optuna 的 param_space 字段
    param_space: Optional[Dict[str, Any]] = None
    # 新增：支持 Optuna 的其他配置字段
    n_trials: Optional[int] = Field(50, ge=1)
    timeout: Optional[int] = Field(3600, ge=1)

class SklearnEngineConfig(BaseModel):
    engine: Literal[EngineType.SKLEARN]
    ml_model_config: ModelConfig
    cv_config: Optional[CVConfig] = None
    hyperparameter_optimization_config: Optional[HyperparameterOptimizationConfig] = None

class PyTorchModelConfig(BaseModel):
    """深度学习模型配置 (用于PyTorch引擎)"""
    task_type: ModelTaskType = ModelTaskType.CLASSIFICATION
    architecture: str = "CNN1D"
    params: Dict[str, Any] = Field(default_factory=dict)

class ValidationConfig(BaseModel):
    strategy: str = "hold-out"
    split_size: Optional[float] = Field(0.2, ge=0.0, le=1.0)
    n_splits: Optional[int] = Field(5, ge=2)

class PytorchEngineConfig(BaseModel):
    engine: Literal[EngineType.PYTORCH]
    pytorch_model_config: PyTorchModelConfig
    validation_config: Optional[ValidationConfig] = None
    cv_config: Optional[CVConfig] = None  # 🚀 新增：支持交叉验证配置，与 SklearnEngineConfig 保持一致
    # 新增：支持超参数优化，与 SklearnEngineConfig 保持一致
    hyperparameter_optimization_config: Optional[HyperparameterOptimizationConfig] = None

# --- 根配置模型 ---

class ExperimentRootConfig(BaseModel):
    """
    所有实验的根配置模型，使用判别联合来管理不同引擎的配置。
    """
    experiment_type: ExperimentType
    data_config: DataConfig
    preprocessing_config: PreprocessingConfig
    feature_selection_config: FeatureSelectionConfig
    experiment_config: ExperimentConfig
    evaluation_config: EvaluationConfig
    paths_config: PathsConfig
    traceability_config: TraceabilityConfig = Field(default_factory=TraceabilityConfig)

    engine_config: Union[SklearnEngineConfig, PytorchEngineConfig] = Field(..., discriminator='engine')

    class Config:
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
