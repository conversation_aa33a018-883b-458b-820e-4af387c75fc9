"""
PyTorch 训练器模块。

本模块实现了可插拔的训练器架构，将训练逻辑从 Wrapper 中解耦出来。
遵循单一职责原则：Wrapper 负责"适配"sklearn接口，Trainer 负责"执行训练"。
这种设计允许未来通过创建不同的 Trainer 子类来支持更高级的训练技术。

Author: txy
License: Apache-2.0
"""

from abc import ABC, abstractmethod
from typing import Optional
import time
import torch
import torch.nn as nn
from torch.utils.data import DataLoader

from ...core.utils import get_logger

logger = get_logger(__name__)


class BaseTrainer(ABC):
    """
    所有 PyTorch 训练器的抽象基类。
    
    此接口定义了所有训练器必须遵循的契约，支持在不同训练策略之间无缝切换。
    设计理念：采用抽象基类定义统一接口，确保不同实现的一致性和可替换性。
    """

    @abstractmethod
    def train(self, model: nn.Module, train_loader: DataLoader, val_loader: Optional[DataLoader] = None) -> nn.Module:
        """
        执行模型训练。

        Args:
            model: 要训练的 PyTorch 模型
            train_loader: 训练数据加载器
            val_loader: 可选的验证数据加载器

        Returns:
            nn.Module: 训练完成的模型
        """
        pass


class StandardTrainer(BaseTrainer):
    """
    标准的 PyTorch 训练器实现。
    
    这个训练器实现了标准的监督学习训练循环，支持分类和回归任务。
    它从 PyTorchSklearnWrapper 中迁移而来，保持了原有的训练逻辑。
    """

    def __init__(self, optimizer_class, criterion, device: str, epochs: int, learning_rate: float, handler=None):
        """
        初始化标准训练器。

        Args:
            optimizer_class: 优化器类（如 torch.optim.Adam）
            criterion: 损失函数
            device: 训练设备（'cpu' 或 'cuda'）
            epochs: 训练轮数
            learning_rate: 学习率
            handler: 可选的结果处理器，用于记录训练指标
        """
        self.optimizer_class = optimizer_class
        self.criterion = criterion
        self.device = device
        self.epochs = epochs
        self.learning_rate = learning_rate
        self.handler = handler  # 用于记录结构化训练日志
        self.logger = get_logger(f"trainer.{self.__class__.__name__}")

    def train(self, model: nn.Module, train_loader: DataLoader, val_loader: Optional[DataLoader] = None) -> nn.Module:
        """
        执行标准的监督学习训练循环。
        
        这个方法包含了从 PyTorchSklearnWrapper.fit 迁移而来的完整训练逻辑，
        实现了训练逻辑与适配器代码的解耦。

        Args:
            model: 要训练的 PyTorch 模型
            train_loader: 训练数据加载器
            val_loader: 可选的验证数据加载器（暂未使用）

        Returns:
            nn.Module: 训练完成的模型
        """
        # 将模型移动到指定设备
        model.to(self.device)
        
        # 初始化优化器
        optimizer = self.optimizer_class(model.parameters(), lr=self.learning_rate)
        
        # 设置模型为训练模式
        model.train()

        # 🔻 修改: 将日志级别从 INFO 降为 DEBUG，减少超参数优化时的日志噪音
        self.logger.debug(f"开始训练，共 {self.epochs} 轮，学习率: {self.learning_rate}")

        # 训练循环 - 从 PyTorchSklearnWrapper.fit 迁移而来
        for epoch in range(self.epochs):
            epoch_start_time = time.time()
            epoch_loss = 0.0
            num_batches = 0
            total_samples = 0

            for batch_X, batch_y in train_loader:
                # 将数据移动到指定设备
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)

                # 清零梯度
                optimizer.zero_grad()

                # 前向传播
                outputs = model(batch_X)

                # 计算损失
                loss = self.criterion(outputs, batch_y)

                # 反向传播
                loss.backward()

                # 更新参数
                optimizer.step()

                # 累积损失和样本数用于日志记录
                epoch_loss += loss.item()
                num_batches += 1
                total_samples += batch_X.size(0)

            # 计算平均损失和训练指标
            avg_loss = epoch_loss / num_batches if num_batches > 0 else 0.0
            epoch_time = time.time() - epoch_start_time
            throughput = total_samples / epoch_time if epoch_time > 0 else 0.0

            # 获取当前学习率
            current_lr = optimizer.param_groups[0]['lr'] if optimizer.param_groups else self.learning_rate

            self.logger.debug(f"Epoch {epoch+1}/{self.epochs}, 平均损失: {avg_loss:.4f}")

            # 记录结构化训练指标（如果提供了 handler）
            if self.handler:
                try:
                    self.handler.log_metric("train/loss", avg_loss)
                    self.handler.log_metric("train/learning_rate", current_lr)
                    self.handler.log_metric("train/epoch_time", epoch_time)
                    self.handler.log_metric("train/throughput", throughput)
                except Exception as e:
                    self.logger.warning(f"Failed to log training metrics: {e}")

        # 🔻 修改: 将日志级别从 INFO 降为 DEBUG，减少超参数优化时的日志噪音
        self.logger.debug("训练完成")
        return model
