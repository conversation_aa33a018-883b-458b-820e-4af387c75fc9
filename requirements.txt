# Core scientific computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Machine learning
scikit-learn>=1.0.0
joblib>=1.0.0

# Deep learning (optional, for CNN models)
torch>=1.9.0
torchvision>=0.10.0

# Data visualization (optional)
matplotlib>=3.3.0
seaborn>=0.11.0

# Progress bars and utilities
tqdm>=4.62.0

# Configuration and logging
pyyaml>=5.4.0

# Hyperparameter optimization
optuna>=4.4.0
optuna-integration[sklearn]>=4.0.0

# Development and testing (optional)
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.0.0
flake8>=3.9.0

# Documentation (optional)
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0
