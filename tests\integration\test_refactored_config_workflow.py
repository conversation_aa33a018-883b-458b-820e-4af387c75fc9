# tests/integration/test_refactored_config_workflow.py
"""
Integration test to ensure the refactored configuration system works end-to-end.

This test validates that our modular configuration system (model presets + 
experiment presets + configuration overrides) can be successfully loaded 
and executed by the framework's core components.


License: Apache-2.0
"""
import unittest
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
if str(project_root / "src") not in sys.path:
    sys.path.insert(0, str(project_root / "src"))

from src.my_spectral_framework.main import load_config_by_path, run_experiment_from_config
from tests.core.test_registry import integration_test


class TestRefactoredConfigWorkflow(unittest.TestCase):
    """Integration tests for the refactored configuration system."""

    @integration_test(
        description="[CONFIG] 验证重构后的RF实验配置能被正确加载和执行",
        tags=["config", "refactored", "rf", "integration", "critical"],
        priority="critical"
    )
    def test_run_refactored_rf_experiment(self):
        """
        Tests if the refactored 'experiment_cls_rf.py' can be loaded
        and executed by the core runner without errors.
        
        This test validates the entire refactored configuration chain:
        1. Model-level presets (conf/model_params/rf.py)
        2. Experiment-level presets (conf/base/presets.py)  
        3. Specific experiment config (conf/runs/classification/experiment_cls_rf.py)
        4. Configuration loading and execution
        """
        # 1. Define the path to our refactored config
        config_path = "conf.runs.classification.experiment_cls_rf"

        # 2. Load the configuration object
        try:
            config_obj = load_config_by_path(config_path)
            self.assertIsNotNone(config_obj, "Config object should not be None.")
            self.assertIsInstance(config_obj, dict, "Config object should be a dictionary.")
        except Exception as e:
            self.fail(f"Loading refactored config failed with an exception: {e}")

        # 3. Validate key configuration structure
        self.assertIn("experiment_config", config_obj, "Config should have experiment_config section")
        self.assertIn("engine_config", config_obj, "Config should have engine_config section")
        self.assertIn("preprocessing_config", config_obj, "Config should have preprocessing_config section")
        
        # Validate that the configuration reflects our refactored structure
        experiment_name = config_obj["experiment_config"]["name"]
        self.assertEqual(experiment_name, "cls_rf_snv_sg_lars", "Experiment name should match expected value")
        
        # Validate that model parameters come from our RF preset
        model_config = config_obj["engine_config"]["ml_model_config"]
        self.assertEqual(model_config["name"], "RF", "Model should be Random Forest")
        self.assertIn("n_estimators", model_config["params"], "RF params should include n_estimators")

        # 4. Run the experiment with the loaded config
        try:
            # Assuming the runner returns a results dictionary upon success
            results = run_experiment_from_config(config_obj)
            self.assertIsInstance(results, dict, "Runner should return a dictionary.")
            self.assertTrue(results, "Results dictionary should not be empty.")
            self.assertEqual(results.get("status"), "success", "Experiment should complete successfully")
            
            print("\n✅ Integration test passed: Refactored config ran successfully.")
            print(f"   - Experiment: {results.get('experiment_name')}")
            print(f"   - Model: {results.get('model_name')}")
            print(f"   - Status: {results.get('status')}")
            
        except Exception as e:
            self.fail(f"Running experiment with refactored config failed: {e}")

    @integration_test(
        description="[CONFIG] 验证模型级预设能被正确导入和使用",
        tags=["config", "model_presets", "integration"],
        priority="high"
    )
    def test_model_presets_import(self):
        """
        Test that model-level presets can be imported and contain expected parameters.
        """
        try:
            # Test RF preset import
            from conf.model_params.rf import FAST_PARAMS as RF_FAST_PARAMS
            self.assertIsInstance(RF_FAST_PARAMS, dict, "RF_FAST_PARAMS should be a dictionary")
            self.assertIn("n_estimators", RF_FAST_PARAMS, "RF params should include n_estimators")
            self.assertIn("random_state", RF_FAST_PARAMS, "RF params should include random_state")
            
            # Test CNN preset import
            from conf.model_params.cnn import FAST_PARAMS as CNN_FAST_PARAMS
            self.assertIsInstance(CNN_FAST_PARAMS, dict, "CNN_FAST_PARAMS should be a dictionary")
            self.assertIn("epochs", CNN_FAST_PARAMS, "CNN params should include epochs")
            self.assertIn("batch_size", CNN_FAST_PARAMS, "CNN params should include batch_size")
            
            print("\n✅ Model presets import test passed.")
            
        except ImportError as e:
            self.fail(f"Failed to import model presets: {e}")

    @integration_test(
        description="[CONFIG] 验证实验级预设能被正确导入和使用",
        tags=["config", "experiment_presets", "integration"],
        priority="high"
    )
    def test_experiment_presets_import(self):
        """
        Test that experiment-level presets can be imported and have correct structure.
        """
        try:
            from conf.base.presets import FAST_DEV_RF_PRESET
            self.assertIsInstance(FAST_DEV_RF_PRESET, dict, "FAST_DEV_RF_PRESET should be a dictionary")
            self.assertIn("experiment_config", FAST_DEV_RF_PRESET, "Preset should have experiment_config")
            self.assertIn("engine_config", FAST_DEV_RF_PRESET, "Preset should have engine_config")
            
            # Validate that the preset uses model-level parameters
            model_params = FAST_DEV_RF_PRESET["engine_config"]["ml_model_config"]["params"]
            self.assertIn("n_estimators", model_params, "Preset should include RF parameters")
            
            print("\n✅ Experiment presets import test passed.")
            
        except ImportError as e:
            self.fail(f"Failed to import experiment presets: {e}")


if __name__ == '__main__':
    unittest.main()
