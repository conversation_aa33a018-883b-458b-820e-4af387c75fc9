"""
一个标准的Sklearn随机森林交叉验证实验配置示例。

此配置展示了如何使用Sklearn引擎运行一个交叉验证实验。
"""

EXPERIMENT_CONFIG = {
    "experiment_type": "cross_validation",
    
    "data_config": {
        "source_type": "opensa_test",
        "type": "Cls",
        "label_col_name": "label",
        "group_col_name": None,
        "spectrum_unique_id_col_name": "spectrum_id",
        "wave_number_range": None,
        "test_size": 0.2,  # CV中此参数被忽略，但为保持结构一致而保留
        "random_seed": 42,
    },
    
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}}
        ]
    },
    
    "feature_selection_config": {
        "method": "Pca",
        "params": {"n_components": 20}
    },
    
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {
            "type": "Classification",
            "name": "RF",
            "params": {
                "n_estimators": 100,
                "max_depth": 5,
                "min_samples_split": 2,
                "min_samples_leaf": 1,
                "random_state": 42
            },
        },
        "cv_config": {
            "method": "stratified",
            "n_splits": 5,
            "random_state": 42
        }
    },
    
    "evaluation_config": {
        "metrics": {
            "classification": ["accuracy", "precision", "recall", "f1"],
            "regression": ["rmse", "r2", "mae"],
        },
        "save_predictions": True,
        "save_model": True,
    },
    
    "experiment_config": {
        "name": "sklearn_rf_cv_example",
        "description": "Sklearn随机森林交叉验证示例",
        "tags": ["sklearn", "cross_validation", "random_forest", "example"],
    },
    
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models",
        "results_dir": "results",
        "logs_dir": "logs",
    },
}
