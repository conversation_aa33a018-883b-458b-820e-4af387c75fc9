"""
Sklearn 兼容包装器模块。

本模块为 PyTorch 模型提供 sklearn 兼容的包装器，
使其能够被统一的 Runner 和 scikit-learn 工具（如 GridSearchCV）使用。
这是实现双核驱动架构对称性的核心适配器。

Author: txy
License: Apache-2.0
"""
import numpy as np
import torch
import torch.nn as nn
from sklearn.base import BaseEstimator, ClassifierMixin, RegressorMixin
from sklearn.preprocessing import LabelEncoder
from torch.utils.data import TensorDataset, DataLoader

from ..core.utils import get_logger
from ..models.deep_learning.dl_builder import build_model
from ..models.deep_learning.trainer import StandardTrainer

logger = get_logger(__name__)

class PyTorchSklearnWrapper(BaseEstimator, ClassifierMixin):
    """
    一个将PyTorch模型封装为Scikit-learn兼容API的适配器。
    """
    def __init__(self, model: nn.<PERSON><PERSON><PERSON>, train_params: dict, handler=None):
        self.model = model
        self.train_params = train_params
        self.handler = handler  # 用于记录训练指标

        # 将所有训练参数提升为顶层属性，以便set_params可以找到它们
        for key, value in train_params.items():
            setattr(self, key, value)

        # 设置默认值（如果没有在train_params中指定）
        self.epochs = train_params.get('epochs', 10)
        self.batch_size = train_params.get('batch_size', 32)
        self.learning_rate = train_params.get('learning_rate', 0.001)
        self.device = train_params.get('device', 'cpu')

        self.classes_ = None
        self.label_encoder_ = None

    def fit(self, X, y):
        """
        训练 PyTorch 模型。

        🚀 重构：现在使用可插拔的 Trainer 架构，将训练逻辑从 Wrapper 中解耦出来。
        遵循单一职责原则：Wrapper 负责"适配"sklearn接口，Trainer 负责"执行训练"。
        """
        # 1. 数据预处理和设备准备
        if self.model.task_type == 'classification':
            self.label_encoder_ = LabelEncoder()
            y_encoded = self.label_encoder_.fit_transform(y)
            self.classes_ = self.label_encoder_.classes_
            y_tensor = torch.LongTensor(y_encoded)
        else: # regression
            y_tensor = torch.FloatTensor(y).reshape(-1, 1)

        X_tensor = torch.FloatTensor(X).unsqueeze(1) # Add channel dimension

        dataset = TensorDataset(X_tensor, y_tensor)
        dataloader = DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        # 2. 根据任务类型选择损失函数
        if self.model.task_type == 'classification':
            criterion = nn.CrossEntropyLoss()
        else:
            criterion = nn.MSELoss()

        # 🚀 3. 使用 StandardTrainer 执行训练（替代原有的训练循环）
        trainer = StandardTrainer(
            optimizer_class=torch.optim.Adam,
            criterion=criterion,
            device=self.device,
            epochs=self.epochs,
            learning_rate=self.learning_rate,
            handler=self.handler  # 传递 handler 用于记录训练指标
        )

        # 委托给训练器执行训练
        self.model = trainer.train(self.model, dataloader)

        # 🔻 修改: 将日志级别从 INFO 降为 DEBUG，减少超参数优化时的日志噪音
        logger.debug(f"PyTorch 模型训练完成，使用 {type(trainer).__name__}")

        return self

    def predict(self, X):
        self.model.eval()
        X_tensor = torch.FloatTensor(X).unsqueeze(1).to(self.device)

        with torch.no_grad():
            outputs = self.model(X_tensor)

        if self.model.task_type == 'classification':
            _, predicted_indices = torch.max(outputs, 1)
            predicted_indices = predicted_indices.cpu().numpy()
            # 将数值标签解码回原始标签
            return self.label_encoder_.inverse_transform(predicted_indices)
        else: # regression
            return outputs.cpu().numpy().flatten()

    def predict_proba(self, X):
        if self.model.task_type != 'classification':
            raise AttributeError("predict_proba is only available for classification models.")

        self.model.eval()
        X_tensor = torch.FloatTensor(X).unsqueeze(1).to(self.device)

        with torch.no_grad():
            outputs = self.model(X_tensor)
            probabilities = torch.softmax(outputs, dim=1)

        return probabilities.cpu().numpy()

    def set_params(self, **params):
        """
        scikit-learn兼容的set_params方法。
        这个方法会拦截以'model__'为前缀的参数，并用它们重新构建PyTorch模型。

        Args:
            **params: 要设置的参数字典

        Returns:
            self: 返回自身以支持链式调用
        """
        # 提取模型架构参数和训练参数
        model_arch_params = {}
        training_params = {}

        for key, value in params.items():
            if key.startswith('model__'):
                # 移除'model__'前缀，这是OptunaSearchCV添加的
                param_name = key[7:]
                model_arch_params[param_name] = value
            else:
                # 其他参数（如batch_size, learning_rate, epochs）
                setattr(self, key, value)
                training_params[key] = value

        # 如果有模型架构参数被更新，则需要重新构建模型
        if model_arch_params:
            # 获取当前模型的架构信息
            arch_info = self.model.get_architecture_info()

            # 合并新的架构参数和旧的训练参数
            new_params = {**self.train_params, **model_arch_params}

            # 映射模型类型名称（CNN1DNet -> CNN）
            model_type = arch_info['model_type']
            if model_type == 'CNN1DNet':
                model_type = 'CNN'

            # 重新构建模型
            self.model = build_model(
                model_name=model_type,
                task_type=arch_info['task_type'],
                input_size=arch_info['input_size'],
                n_outputs=arch_info['n_outputs'],
                params=new_params
            )

            # 更新train_params以保持一致性
            self.train_params.update(model_arch_params)

            logger.debug(f"Rebuilt PyTorch model with new params: {model_arch_params}")  # 🚀 优化：降低日志级别减少噪音

        return self
