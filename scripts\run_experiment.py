"""
光谱分析实验运行的命令行界面。

这个脚本就像一个“总指挥”，让你通过简单的命令行指令，
来启动和管理光谱分析框架中的各种实验。
它支持三种不同类型的实验，每种实验都有其特定的用途，
帮助你从不同角度研究光谱数据：

1. single_run: 单次运行实验
   - 就像只做一次测试，看看你的模型在特定配置下表现如何。

2. cross_validation: 交叉验证实验
   - 更严谨的测试方法！它会把数据分成几份，轮流用一部分做训练，一部分做测试，
     这样能更全面地评估模型的稳定性和泛化能力（模型对新数据的处理能力）。

3. hyperparameter_optimization: 超参数优化实验
   - 寻找“最佳配方”！模型有很多参数需要手动设置（这些叫超参数），
     这个实验会自动尝试不同的参数组合，帮你找到让模型表现最好的那一组参数。

用法示例（如何在命令行里运行这个脚本）：
    python scripts/run_experiment.py --config experiment_cls_rf
    # ⬆️ 这条命令会运行一个名为 'experiment_cls_rf' 的配置定义的实验。
    # 'experiment_cls_rf' 通常是一个文件，里面包含了这次实验的所有设置。

    python scripts/run_experiment.py --config experiment_rgs_cnn
    # ⬆️ 这条命令会运行另一个名为 'experiment_rgs_cnn' 的配置定义的实验。

Author: txy
License: Apache-2.0
"""

import argparse  # 导入argparse模块，用于处理命令行参数，让你的程序能接收外部指令。
import ast  # 导入ast模块，用于安全解析Python表达式
import importlib  # 导入importlib模块，用于动态导入其他Python模块（文件）。
import logging  # 导入logging模块，用于日志记录
import logging.config  # 导入logging.config模块，用于从配置文件加载日志设置
import sys  # 导入sys模块，它提供了与Python解释器及其环境交互的功能，比如修改系统路径。
from pathlib import Path  # 导入pathlib模块中的Path类，用于更方便地处理文件和目录路径。

import yaml  # 导入yaml模块，用于读取YAML配置文件

# --- 项目路径设置（现在通过包安装自动处理） ---
# 获取当前脚本文件（run_experiment.py）所在的绝对路径
script_dir = Path(__file__).resolve().parent
# 获取项目根目录的路径（当前脚本目录的上一级目录）
project_root = script_dir.parent

# 临时添加 src 路径以确保导入工作（在包安装问题解决前）
src_path = project_root / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))
# 也添加项目根目录以便导入 conf 模块
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# 从my_spectral_framework.core.utils导入get_logger函数，用于创建日志记录器
# --- 导入光谱分析框架的核心模块 ---
# 从my_spectral_framework.core.utils导入validate_config函数，用于旧版的配置验证
from my_spectral_framework.core.utils import get_logger  # 日志工具函数
from my_spectral_framework.core.utils import validate_config  # 遗留验证函数

# 这里的注释说明了旧的导入方式已被移除，现在使用更先进的“Runner策略模式”
# 遗留导入已移除 - 现在使用Runner策略模式


def setup_logging():
    """从YAML文件加载日志配置。"""
    config_path = Path("conf/logging_config.yaml")
    if config_path.exists():
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            # 确保logs目录存在
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            # 应用配置
            logging.config.dictConfig(config)
            logging.info("日志系统已从 'conf/logging_config.yaml' 加载配置。")
        except Exception as e:
            # 如果配置文件有问题，则使用基础配置
            logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
            logging.error(f"加载日志配置失败: {e}，已切换到基础配置。")
    else:
        # 如果配置文件不存在，则使用一个基础配置
        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
        logging.warning("警告: 未找到 'conf/logging_config.yaml'，已使用基础日志配置。")


# 初始化一个日志记录器。__name__是当前模块的名字，这样日志会显示来自哪个文件。
logger = get_logger(__name__)


def load_config(config_name: str):
    """
    使用 Pydantic 库加载并验证实验配置文件。
    配置文件里包含了运行一个实验所需的所有设置，比如用什么模型、用什么数据等等。

    Args:
        config_name: 配置文件名（例如 'experiment_cls_rf'），不包含 .py 扩展名。
                     如果配置文件在子目录里，例如 'some_folder/my_config'，也可以这样写。

    Returns:
        已验证的 ExperimentRootConfig 对象。这是一个特殊的数据结构，
        它会确保你配置里的所有信息都是正确且完整的。

    Raises:
        ImportError: 如果 Python 找不到你指定的配置文件。
        AttributeError: 如果找到文件但文件里没有一个叫 'EXPERIMENT_CONFIG' 的变量。
                        这个变量是存放实验设置的关键。
        ValidationError: 如果配置文件的内容不符合预设的规则（Pydantic会检查这些规则）。
                         例如，某个数字写成了文本，或者缺少了必需的设置项。
    """
    try:
        # --- 导入 Pydantic 模型和验证工具 ---
        from pydantic import ValidationError
        from my_spectral_framework.core.config_models import ExperimentRootConfig

        # --- 动态导入配置模块（支持子目录）---
        module_path = f"conf.runs.{config_name.replace('/', '.')}"
        config_module = importlib.import_module(module_path)

        # --- 获取配置文件中的 EXPERIMENT_CONFIG 变量 ---
        if not hasattr(config_module, "EXPERIMENT_CONFIG"):
            raise AttributeError(f"在 '{config_name}.py' 中找不到 'EXPERIMENT_CONFIG' 变量")

        config_dict = config_module.EXPERIMENT_CONFIG

        # --- 使用 Pydantic 验证配置字典 ---
        try:
            validated_config = ExperimentRootConfig.model_validate(config_dict)
            logger.info(f"[SUCCESS] 配置 '{config_name}' 加载并验证成功")
            return validated_config
        except ValidationError as e:
            logger.error(f"[ERROR] 配置 '{config_name}' 验证失败:")
            for error in e.errors():
                field = " -> ".join(map(str, error["loc"]))
                message = error["msg"]
                value = error.get("input", "N/A")
                logger.error(f"  - 字段: `{field}`")
                logger.error(f"    错误: {message}")
                logger.error(f"    问题值: {value}")
            raise

    except ImportError as e:
        logger.error(f"导入配置 '{config_name}' 失败: {e}")
        logger.error("请确保配置文件存在于 'conf/runs/' 目录中，并且路径正确。")
        raise

    except AttributeError as e:
        logger.error(f"加载配置 '{config_name}' 时出错: {e}")
        raise


def list_available_configs():
    """
    查找并列出所有可用的实验配置文件。
    这些文件定义了不同的实验设置，你可以通过命令行选择运行它们。
    它会递归查找 'conf/runs/' 目录及其子目录中的所有 .py 文件。

    Returns:
        一个列表，包含所有可用配置的名称（包含子目录路径，例如 'group1/my_exp_config'）。
    """
    try:
        # 构造存放配置文件的目录路径：项目根目录下的 'conf/runs' 文件夹
        conf_runs_dir = project_root / "conf" / "runs"
        config_files = []  # 初始化一个空列表，用于存放找到的配置文件名

        # 递归地遍历 conf_runs_dir 目录及其所有子目录，查找所有以 .py 结尾的文件
        for file_path in conf_runs_dir.rglob("*.py"):
            # 忽略 Python 包的初始化文件 __init__.py
            if file_path.name != "__init__.py":
                # 获取文件相对于 conf/runs 目录的路径。
                # 例如，如果文件是 'conf/runs/groupA/exp1.py'，那么 relative_path 就是 'groupA/exp1.py'
                relative_path = file_path.relative_to(conf_runs_dir)
                # 移除文件的 .py 扩展名，并将路径分隔符从反斜杠（Windows）转换为正斜杠
                # 这样得到的就是可以在命令行中使用的配置名称，例如 'groupA/exp1'
                config_name = str(relative_path.with_suffix("")).replace("\\", "/")
                config_files.append(config_name)  # 将配置名添加到列表中

        return sorted(config_files)  # 返回按字母顺序排序的配置名称列表

    except Exception as e:
        # 如果在列出配置时发生任何错误，打印错误信息并返回空列表
        logger.error(f"列出配置时出错: {e}")
        return []


def main():
    """
    这是脚本的主函数，程序从这里开始执行。
    它负责解析命令行参数、加载配置、运行实验以及报告结果。

    程序执行流程概览：
    1. **解析命令行参数**: 读取你在运行脚本时输入的指令（比如 --config, --list-configs）。
    2. **设置日志级别**: 根据你的设置，决定日志信息的详细程度（DEBUG, INFO 等）。
    3. **处理配置列表请求**: 如果你要求列出所有可用配置，就执行这个操作。
    4. **加载和验证指定配置**: 根据你选择的配置，加载它的内容并检查是否符合规则。
    5. **根据实验类型运行实验**: 根据配置中定义的实验类型（单次运行、交叉验证或超参数优化），
       选择对应的“Runner”（实验执行器）来启动实验。
    6. **输出实验结果摘要**: 实验结束后，会打印一份简洁的报告，总结实验的主要结果。
    """
    # --- 创建命令行参数解析器 ---
    parser = argparse.ArgumentParser(
        description="运行光谱分析实验",  # 命令行工具的简短描述
        # 使用 RawDescriptionHelpFormatter 可以保留 epilog 中的换行和格式
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例（如何在命令行中使用这个脚本）：
  python scripts/run_experiment.py --config experiment_cls_rf
  python scripts/run_experiment.py --config experiment_rgs_cnn
  python scripts/run_experiment.py --list-configs
        """,  # 显示给用户的示例用法
    )

    # --- 定义命令行参数 ---
    parser.add_argument(
        "--config",
        "-c",  # 参数名，可以使用--config或-c
        type=str,  # 参数值是字符串类型
        help="要运行的实验配置名称（不包含.py扩展名）。例如：'my_exp' 或 'group/my_exp'",  # 帮助信息
    )

    parser.add_argument(
        "--list-configs",
        "-l",  # 参数名，可以使用--list-configs或-l
        action="store_true",  # 这是一个“开关”参数，如果出现就为True，否则为False
        help="列出所有可用的实验配置名称",  # 帮助信息
    )

    parser.add_argument(
        "--validate-only",
        "-v",  # 参数名，可以使用--validate-only或-v
        action="store_true",  # 同样是一个“开关”参数
        help="仅验证配置的正确性，而不实际运行实验",  # 帮助信息
    )

    parser.add_argument(
        "--log-level",  # 参数名
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],  # 允许的值列表
        default="INFO",  # 如果不指定，默认为INFO级别
        help="设置日志级别（决定日志输出的详细程度）。从低到高依次为：\
             DEBUG(最详细), INFO, WARNING, ERROR, CRITICAL(最不详细)",  # 帮助信息
    )

    parser.add_argument(
        "--set",
        metavar="KEY=VALUE",
        nargs='+',
        help="覆盖配置文件中的一个或多个参数。例如: --set engine_config.ml_model_config.params.n_estimators=200"
    )

    args = parser.parse_args()  # 解析命令行参数，把它们存储在一个叫做args的对象里

    # --- 首先设置日志系统 ---
    # 将 setup_logging() 作为 main 函数的第一步
    setup_logging()

    # --- 设置日志级别 ---
    # 获取根日志记录器，并根据用户传入的log-level参数设置其级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # --- 处理列出配置选项 ---
    if args.list_configs:  # 如果用户在命令行中使用了 --list-configs 参数
        print("可用的实验配置:")
        configs = list_available_configs()  # 调用函数列出所有配置
        if configs:  # 如果找到了配置
            for config in configs:
                print(f"  - {config}")  # 打印每个配置的名称
        else:
            print("  在conf/runs/目录中未找到配置")  # 如果没找到，打印提示信息
        return  # 打印完配置后，程序就结束了

    # --- 检查是否提供了config参数 ---
    if not args.config:  # 如果用户没有指定 --config 参数
        # 打印错误信息并退出程序，提示用户必须指定配置或选择列出配置
        parser.error("请使用--config指定配置或使用--list-configs查看可用选项")

    try:
        # --- 加载并验证配置 ---
        logger.info(f"正在加载配置: {args.config}")  # 记录日志，告知正在加载哪个配置
        config = load_config(args.config)  # 调用 load_config 函数加载和验证配置

        # 应用命令行参数覆盖
        if args.set:
            from my_spectral_framework.core.utils import deep_update
            from my_spectral_framework.core.config_models import ExperimentRootConfig

            config_dict = config.model_dump(by_alias=True)
            overrides = {}
            for arg in args.set:
                key, value = arg.split('=', 1)
                original_value = value
                # 尝试将值转换为正确的类型，使用安全的 ast.literal_eval
                try:
                    value = ast.literal_eval(value)
                except (ValueError, SyntaxError):
                    # 如果解析失败，保持为字符串
                    pass

                # 构建嵌套字典
                temp = overrides
                keys = key.split('.')
                for k in keys[:-1]:
                    temp = temp.setdefault(k, {})
                temp[keys[-1]] = value

                # 记录覆盖项变更日志
                logger.info(f"参数覆盖: {key}: {original_value} -> {value}")

            logger.info(f"应用命令行参数覆盖: {overrides}")
            deep_update(config_dict, overrides)
            config = ExperimentRootConfig.model_validate(config_dict)

        # 配置已通过 Pydantic 验证（这是非常重要的一步，确保配置是正确的）
        logger.info("配置验证通过（Pydantic）")

        # --- 如果设置了仅验证标志，则在此退出 ---
        if args.validate_only:  # 如果用户指定了 --validate-only 参数
            logger.info("配置验证成功完成")
            return  # 验证成功后直接退出，不运行实验

        # --- 使用插件化的 Runner 注册表运行实验 ---
        # 🚀 重构：从注册表动态获取运行器，而不是硬编码映射

        # 导入注册表模块以触发装饰器注册
        import my_spectral_framework.runners
        from my_spectral_framework.core.registry import get_runner_class

        engine_type = config.engine_config.engine
        experiment_type = config.experiment_type

        # 创建执行引擎
        from my_spectral_framework.core.engine import create_engine
        engine = create_engine(engine_type)
        logger.info(f"已创建 {type(engine).__name__}")

        # 🚀 从注册表动态获取运行器类
        try:
            runner_class = get_runner_class(experiment_type)
            logger.info(f"正在使用 {engine_type} 引擎执行 {experiment_type.replace('_', ' ')} 实验。")
            runner = runner_class(config, engine)
        except KeyError as e:
            logger.error(f"不支持的 experiment_type: {experiment_type}")
            logger.error(f"注册表错误: {e}")
            sys.exit(1)

        results = runner.run()  # 运行实验，并获取结果

        # --- 提取上下文以进行一致的结果报告 ---
        # 上下文 (context) 对象包含了实验运行过程中的各种信息和结果
        context = runner.context
        # 将实验结果存储到 context 中，以保持与旧代码的兼容性
        if hasattr(context, "__dict__"):  # 检查 context 是否有 __dict__ 属性（通常是类实例）
            context.results = results

        # --- 为超参数优化实验打印特殊成功消息 ---
        if experiment_type == "hyperparameter_optimization":
            print(f"\n[SUCCESS] 超参数优化成功完成!")  # 庆祝消息
            if isinstance(results, dict):  # 如果结果是字典类型
                if "metrics" in results:  # 检查是否有性能指标
                    metrics = results["metrics"]
                    if "best_cv_score" in metrics:
                        print(f"最佳交叉验证得分: {metrics['best_cv_score']['value']:.4f}")
                    if "test_score" in metrics:
                        print(f"测试得分: {metrics['test_score']['value']:.4f}")
                if "experiment_info" in results:
                    exp_dir = results["experiment_info"].get("directory", "未知")
                    print(f"结果保存至: {exp_dir}")
            else:
                print(f"结果保存至实验目录")  # 默认结果保存位置

        # --- 检查实验是否成功完成或是否有错误 ---
        if context.errors:  # 如果 context 对象中记录了错误
            logger.error("实验完成但有错误!")
            for error in context.errors:
                logger.error(f"  - {error}")  # 打印每个错误信息
            sys.exit(1)  # 有错误就退出

        else:  # 如果实验成功完成
            logger.info("实验成功完成!")

            # =================== 修改/新增代码 开始 ===================
            # --- 打印美化后的实验摘要 ---
            print("\n" + "=" * 70)
            print("[SUMMARY] 实验摘要".center(70))
            print("=" * 70)

            # 实验基本信息
            experiment_name = context.config.get('experiment_config', {}).get('name', 'N/A')
            print(f"[NAME] 实验名称: {experiment_name}")
            print(f"[ID] 运行 ID: {context.run_id}")
            print(f"[TYPE] 实验类型: {experiment_type}")
            print(f"[TIME] 执行时间: {context.execution_time:.2f} 秒")

            print("-" * 70)
            # =================== 修改/新增代码 结束 ===================

            # =================== 修改/新增代码 开始 ===================
            # 关键指标
            print("[METRICS] 关键指标:")
            if experiment_type == "hyperparameter_optimization" and hasattr(context, "hyperopt_results"):
                # 超参数优化结果的特殊处理
                opt_results = context.hyperopt_results
                if isinstance(opt_results, dict):
                    scoring_metric = opt_results.get('scoring', 'score')
                    best_score = opt_results.get('best_score', 'N/A')
                    if isinstance(best_score, (int, float)):
                        print(f"  - 最佳交叉验证得分 ({scoring_metric}): {best_score:.4f}")
                    else:
                        print(f"  - 最佳交叉验证得分 ({scoring_metric}): {best_score}")

                    test_score = opt_results.get("test_score")
                    if test_score is not None and isinstance(test_score, (int, float)):
                        print(f"  - 最终测试得分: {test_score:.4f}")
                    elif test_score is not None:
                        print(f"  - 最终测试得分: {test_score}")

                    print(f"  - 优化策略: {opt_results.get('strategy', 'N/A')}")
                    print(f"  - 最佳参数: {opt_results.get('best_params', {})}")

            elif context.evaluation_results:  # 如果有评估结果
                # 获取模型类型（例如 'classification' 分类或 'regression' 回归）
                config_dict = context.config
                model_config = config_dict.get("ml_model_config") or config_dict.get("model_config", {})
                model_type = model_config.get("type", "classification").lower()

                if experiment_type == "cross_validation" and "cv_average" in context.evaluation_results:
                    # 如果是交叉验证实验，并且有交叉验证的平均结果
                    avg_metrics = context.evaluation_results["cv_average"]  # 获取平均指标
                    cv_folds = config_dict.get("cv_config", {}).get("n_splits", 5)
                    print(f"  (基于 {cv_folds}-折交叉验证平均值)")
                    if model_type == "classification":  # 如果是分类模型
                        print(f"  - 平均准确率 (Accuracy): {avg_metrics.get('accuracy', 0):.4f}")
                        print(f"  - 平均 F1-Score: {avg_metrics.get('f1_score', 0):.4f}")
                    elif model_type == "regression":  # 如果是回归模型
                        print(f"  - 平均 RMSE: {avg_metrics.get('rmse', 0):.4f}")
                        print(f"  - 平均 R²: {avg_metrics.get('r2', 0):.4f}")
                else:
                    # 如果是单次运行实验的结果显示
                    metrics = context.evaluation_results  # 获取评估指标
                    if model_type == "classification":
                        print(f"  - 准确率 (Accuracy): {metrics.get('accuracy', 0):.4f}")
                        print(f"  - F1-Score: {metrics.get('f1_score', 0):.4f}")
                    elif model_type == "regression":
                        print(f"  - RMSE: {metrics.get('rmse', 0):.4f}")
                        print(f"  - R²: {metrics.get('r2', 0):.4f}")
            else:
                print("  - 未记录评估指标")

            print("-" * 70)
            # =================== 修改/新增代码 结束 ===================

            # =================== 修改/新增代码 开始 ===================
            # 结果保存位置
            from my_spectral_framework.core.utils import get_project_root
            if hasattr(context, "_experiment_dirname") and context._experiment_dirname:
                results_dir = get_project_root() / "outputs" / context._experiment_dirname
                print(f"[SAVED] 结果已保存至: {results_dir}")
            elif context.artifacts:
                results_dir = get_project_root() / "outputs" / context.run_id
                print(f"[SAVED] 结果已保存至: {results_dir}")
            else:
                print("[SAVED] 结果已保存至 'outputs' 目录")

            print("=" * 70)
            # =================== 修改/新增代码 结束 ===================

    except KeyboardInterrupt:
        # 捕获用户按下 Ctrl+C 的中断信号
        logger.info("用户中断了实验")
        sys.exit(1)  # 正常退出，但告知是被用户中断

    except Exception as e:
        # 捕获其他所有未预料到的错误
        logger.error(f"实验执行失败，错误: {str(e)}")  # 打印错误信息
        if args.log_level == "DEBUG":  # 如果日志级别是 DEBUG
            import traceback  # 导入traceback模块

            # 打印完整的错误堆栈信息，这对于调试非常有用
            logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        sys.exit(1)  # 程序非正常退出


if __name__ == "__main__":
    # 这是一个标准的 Python 写法，表示：
    # 当这个脚本文件被直接运行时（而不是被其他文件导入时），就执行 main() 函数。
    # 这样可以防止在其他文件导入这个脚本时，main() 函数也自动运行。
    main()
