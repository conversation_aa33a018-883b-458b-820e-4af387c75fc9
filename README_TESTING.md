# 测试指南

本项目采用分层测试策略，确保框架的稳定性、可靠性和性能。测试系统通过自定义的装饰器进行组织，支持灵活的筛选和执行。

## 如何运行测试

所有测试都可以通过 `scripts/run_solid_tests.py` 脚本执行。

### 运行所有核心测试

```bash
# 运行所有单元测试和集成测试
python scripts/run_solid_tests.py
```

### 按级别运行测试

```bash
# 只运行单元测试
python scripts/run_solid_tests.py --level unit

# 只运行集成测试
python scripts/run_solid_tests.py --level integration

# 只运行端到端测试
python scripts/run_solid_tests.py --level e2e
```

### 按标签或优先级筛选

```bash
# 只运行与预处理相关的测试
python scripts/run_solid_tests.py --tag preprocessing

# 只运行标记为"critical"优先级的测试
python scripts/run_solid_tests.py --priority critical
```

### 生成覆盖率报告

```bash
# 运行单元测试并生成覆盖率报告
python scripts/run_solid_tests.py --level unit --cov=src

# 查看HTML格式的报告
# 在浏览器中打开 htmlcov/index.html
```

## 测试架构

测试遵循标准的测试金字塔模型，分为三个主要层次：

1.  **单元测试 (`tests/unit/`)**: 专注于测试单个函数或类的逻辑正确性，不依赖外部组件。
2.  **集成测试 (`tests/integration/`)**: 测试多个内部组件之间的交互是否符合预期，例如测试`Runner`能否正确调用`Engine`。
3.  **端到端测试 (`tests/e2e/`)**: 从用户的视角出发，通过命令行接口运行完整的实验流程，验证整个系统的功能。

所有测试用例都通过装饰器（如 `@unit_test`, `@integration_test`）在 `tests/core/test_registry.py` 中进行注册，实现了测试的解耦和自动化发现。

## CI/CD 集成

本项目已在 `.github/workflows/ci.yml` 中配置了完整的CI/CD流程，每次代码推送或合并请求都会自动执行以下检查：

  - 代码风格检查 (Black, Flake8, isort)
  - 静态类型检查 (Mypy)
  - 在多个Python版本上运行单元测试、集成测试和端到端测试
  - 计算并上传测试覆盖率报告到Codecov

## 编写新测试

  - **位置**: 根据测试类型，将新的测试文件放在 `tests/unit`, `tests/integration`, 或 `tests/e2e` 目录下。
  - **装饰器**: 使用 `@unit_test`, `@integration_test`, 或 `@e2e_test` 装饰您的测试函数，并提供清晰的 `description`。
  - **Fixtures**: 对于需要共享的数据或对象（如模拟数据、配置），请在 `tests/conftest.py` 中定义Pytest Fixtures。
  - **断言**: 使用清晰的断言来验证预期结果。
