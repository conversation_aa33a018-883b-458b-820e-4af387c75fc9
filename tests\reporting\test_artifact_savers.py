"""
DataSaver 单元测试 - 验证追踪关键能力
"""
import json
import numpy as np
import pandas as pd
import pytest
from pathlib import Path

# 导入被测试的类
from my_spectral_framework.reporting.artifact_savers import DataSaver


class TestDataSaverStaticMethods:
    """测试 DataSaver 的静态方法"""

    def test_save_split_indices_writes_json(self, tmp_path):
        """
        用例1：验证 save_split_indices 能正确写入 JSON 文件
        """
        # 准备测试数据
        train_ids = [1, 2, 3, 4]
        test_ids = [5, 6]
        split_path = tmp_path / "split_indices.json"
        
        # 调用被测试的方法
        DataSaver.save_split_indices(train_ids, test_ids, split_path)
        
        # 断言文件存在
        assert split_path.exists(), "split_indices.json 文件应该被创建"
        
        # 验证 JSON 结构和内容
        with open(split_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert "train_ids" in data, "JSON 应包含 train_ids 字段"
        assert "test_ids" in data, "JSON 应包含 test_ids 字段"
        assert "train_count" in data, "JSON 应包含 train_count 字段"
        assert "test_count" in data, "JSON 应包含 test_count 字段"
        
        assert data["train_ids"] == train_ids, "train_ids 内容应正确"
        assert data["test_ids"] == test_ids, "test_ids 内容应正确"
        assert data["train_count"] == len(train_ids), "train_count 应正确"
        assert data["test_count"] == len(test_ids), "test_count 应正确"

    def test_save_split_indices_creates_directory(self, tmp_path):
        """
        验证 save_split_indices 能自动创建目录
        """
        # 使用不存在的子目录
        nested_path = tmp_path / "debug_artifacts" / "split_indices.json"
        train_ids = ["train_1", "train_2"]
        test_ids = ["test_1"]
        
        # 调用方法
        DataSaver.save_split_indices(train_ids, test_ids, nested_path)
        
        # 验证目录和文件都被创建
        assert nested_path.parent.exists(), "父目录应被自动创建"
        assert nested_path.exists(), "文件应被创建"

    def test_save_matrix_csv_sampling(self, tmp_path):
        """
        用例2：验证 save_matrix_csv 的采样行为
        """
        # 构造 15,000 行 x 3 列的测试数据
        large_data = np.random.rand(15000, 3)
        matrix_path = tmp_path / "X_train_raw.csv"
        sample_limit = 1000
        
        # 调用被测试的方法
        DataSaver.save_matrix_csv("X_train_raw", large_data, matrix_path, sample_limit=sample_limit)
        
        # 断言 CSV 存在
        assert matrix_path.exists(), "CSV 文件应该被创建"
        
        # 验证行数不超过采样限制
        df = pd.read_csv(matrix_path)
        assert len(df) <= sample_limit, f"CSV 行数应不超过 {sample_limit}，实际为 {len(df)}"
        
        # 验证列数正确（3个特征列）
        expected_columns = 3
        assert df.shape[1] == expected_columns, f"CSV 应有 {expected_columns} 列，实际为 {df.shape[1]}"
        
        # 验证列名格式
        expected_col_pattern = "X_train_raw_feature_"
        for col in df.columns:
            assert col.startswith(expected_col_pattern), f"列名应以 '{expected_col_pattern}' 开头"

    def test_save_matrix_csv_with_id_column(self, tmp_path):
        """
        验证 save_matrix_csv 能正确添加 ID 列
        """
        # 准备测试数据
        test_data = np.random.rand(100, 2)
        id_column = [f"sample_{i}" for i in range(100)]
        matrix_path = tmp_path / "test_matrix.csv"
        
        # 调用方法
        DataSaver.save_matrix_csv("test", test_data, matrix_path, id_column=id_column)
        
        # 验证结果
        df = pd.read_csv(matrix_path)
        
        # 应该有 3 列：1个ID列 + 2个特征列
        assert df.shape[1] == 3, f"应有 3 列（1个ID + 2个特征），实际为 {df.shape[1]}"
        
        # 验证 ID 列存在且位于第一列
        assert df.columns[0] == "spectrum_id", "第一列应为 spectrum_id"
        
        # 验证 ID 列内容
        assert df["spectrum_id"].tolist() == id_column, "ID 列内容应正确"

    def test_save_matrix_csv_with_dataframe_input(self, tmp_path):
        """
        验证 save_matrix_csv 能处理 DataFrame 输入
        """
        # 准备 DataFrame 输入
        df_input = pd.DataFrame({
            'feature_1': np.random.rand(50),
            'feature_2': np.random.rand(50),
            'feature_3': np.random.rand(50)
        })
        matrix_path = tmp_path / "dataframe_test.csv"
        
        # 调用方法
        DataSaver.save_matrix_csv("df_test", df_input, matrix_path)
        
        # 验证结果
        df_output = pd.read_csv(matrix_path)
        
        # 验证形状
        assert df_output.shape == df_input.shape, "输出 DataFrame 形状应与输入一致"
        
        # 验证列名保持不变
        assert list(df_output.columns) == list(df_input.columns), "列名应保持不变"

    def test_save_matrix_csv_no_sampling_when_under_limit(self, tmp_path):
        """
        验证当数据量小于采样限制时不进行采样
        """
        # 准备小数据集
        small_data = np.random.rand(500, 4)
        matrix_path = tmp_path / "small_data.csv"
        sample_limit = 1000
        
        # 调用方法
        DataSaver.save_matrix_csv("small", small_data, matrix_path, sample_limit=sample_limit)
        
        # 验证结果
        df = pd.read_csv(matrix_path)
        
        # 应该保持原始行数，不进行采样
        assert len(df) == 500, f"小数据集不应被采样，应保持 500 行，实际为 {len(df)}"

    def test_save_matrix_csv_creates_directory(self, tmp_path):
        """
        验证 save_matrix_csv 能自动创建目录
        """
        # 使用不存在的嵌套目录
        nested_path = tmp_path / "debug_artifacts" / "matrices" / "X_test.csv"
        test_data = np.random.rand(10, 2)
        
        # 调用方法
        DataSaver.save_matrix_csv("X_test", test_data, nested_path)
        
        # 验证目录结构被创建
        assert nested_path.parent.exists(), "嵌套目录应被自动创建"
        assert nested_path.exists(), "文件应被创建"
