"""
产物分析模块。

本模块提供了面向用户的产物加载和分析 API，是可追溯性基础设施的用户接口层。
通过简单的函数调用，用户可以轻松加载和分析之前实验的产物。

设计理念：
1. 用户友好：提供简单直观的 API
2. 自动化：自动处理后端发现和连接
3. 容错性：提供详细的错误信息和建议
4. 扩展性：支持未来的分析功能扩展


License: Apache-2.0
"""

from typing import Any, Dict, List, Optional

from .resolver import ArtifactResolver, ArtifactNotFoundError
from ..core.utils import get_logger

logger = get_logger(__name__)

# 全局解析器实例（延迟初始化）
_global_resolver: Optional[ArtifactResolver] = None


def get_resolver() -> ArtifactResolver:
    """
    获取全局产物解析器实例。
    
    Returns:
        ArtifactResolver: 解析器实例
    """
    global _global_resolver
    
    if _global_resolver is None:
        try:
            _global_resolver = ArtifactResolver(auto_discover=True)
            logger.info("全局产物解析器初始化成功")
        except Exception as e:
            logger.error(f"初始化全局产物解析器失败: {e}")
            raise
    
    return _global_resolver


def load_artifact(run_id: str, artifact_name: str) -> Any:
    """
    加载指定的实验产物。
    
    这是面向用户的主要 API，用于加载之前实验中保存的产物。
    函数会自动发现存储后端和注册表，并处理所有底层细节。
    
    Args:
        run_id: 实验运行 ID
        artifact_name: 产物名称
        
    Returns:
        Any: 加载的产物数据
        
    Raises:
        ArtifactNotFoundError: 当产物不存在时
        ValueError: 当参数无效时
        RuntimeError: 当加载过程中出现错误时
        
    Examples:
        >>> # 加载模型
        >>> model = load_artifact("2023-10-27_14-30-15_single_RF_SNV_none", "final_model")
        
        >>> # 加载预测结果
        >>> predictions = load_artifact("2023-10-27_14-30-15_single_RF_SNV_none", "predictions")
        
        >>> # 加载评估指标
        >>> metrics = load_artifact("2023-10-27_14-30-15_single_RF_SNV_none", "metrics")
    """
    if not run_id or not artifact_name:
        raise ValueError("run_id 和 artifact_name 不能为空")
    
    try:
        resolver = get_resolver()
        return resolver.resolve(run_id, artifact_name)
    except ArtifactNotFoundError:
        # 提供更友好的错误信息
        available_runs = list_runs()
        if run_id not in available_runs:
            raise ArtifactNotFoundError(
                f"实验运行 '{run_id}' 不存在。\n"
                f"可用的实验运行: {available_runs[:5]}{'...' if len(available_runs) > 5 else ''}"
            )
        else:
            available_artifacts = list_artifacts(run_id)
            artifact_names = [a['artifact_name'] for a in available_artifacts]
            raise ArtifactNotFoundError(
                f"产物 '{artifact_name}' 在实验运行 '{run_id}' 中不存在。\n"
                f"可用的产物: {artifact_names}"
            )
    except Exception as e:
        logger.error(f"加载产物失败: {run_id}/{artifact_name}, 错误: {e}")
        raise RuntimeError(f"加载产物失败: {e}") from e


def list_artifacts(run_id: str) -> List[Dict[str, Any]]:
    """
    列出指定实验运行的所有产物。
    
    Args:
        run_id: 实验运行 ID
        
    Returns:
        List[Dict[str, Any]]: 产物信息列表，每个字典包含产物的元数据
        
    Examples:
        >>> artifacts = list_artifacts("2023-10-27_14-30-15_single_RF_SNV_none")
        >>> for artifact in artifacts:
        ...     print(f"{artifact['artifact_name']} ({artifact['format']})")
    """
    if not run_id:
        raise ValueError("run_id 不能为空")
    
    try:
        resolver = get_resolver()
        return resolver.list_artifacts(run_id)
    except Exception as e:
        logger.error(f"列出产物失败: {run_id}, 错误: {e}")
        raise RuntimeError(f"列出产物失败: {e}") from e


def list_runs() -> List[str]:
    """
    列出所有可用的实验运行 ID。
    
    Returns:
        List[str]: 实验运行 ID 列表
        
    Examples:
        >>> runs = list_runs()
        >>> print(f"找到 {len(runs)} 个实验运行")
        >>> for run_id in runs[:5]:  # 显示前5个
        ...     print(f"  {run_id}")
    """
    try:
        resolver = get_resolver()
        return resolver.list_runs()
    except Exception as e:
        logger.error(f"列出实验运行失败: {e}")
        raise RuntimeError(f"列出实验运行失败: {e}") from e


def get_artifact_info(run_id: str, artifact_name: str) -> Optional[Dict[str, Any]]:
    """
    获取产物的详细信息。
    
    Args:
        run_id: 实验运行 ID
        artifact_name: 产物名称
        
    Returns:
        Optional[Dict[str, Any]]: 产物的详细元数据，如果不存在则返回 None
        
    Examples:
        >>> info = get_artifact_info("2023-10-27_14-30-15_single_RF_SNV_none", "final_model")
        >>> if info:
        ...     print(f"产物格式: {info['format']}")
        ...     print(f"创建时间: {info['created_at']}")
        ...     print(f"存储位置: {info['uri']}")
    """
    if not run_id or not artifact_name:
        raise ValueError("run_id 和 artifact_name 不能为空")
    
    try:
        resolver = get_resolver()
        return resolver.get_artifact_metadata(run_id, artifact_name)
    except Exception as e:
        logger.error(f"获取产物信息失败: {run_id}/{artifact_name}, 错误: {e}")
        raise RuntimeError(f"获取产物信息失败: {e}") from e


def search_artifacts(
    name_pattern: Optional[str] = None,
    format: Optional[str] = None,
    run_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    搜索符合条件的产物。
    
    Args:
        name_pattern: 产物名称模式（支持 SQL LIKE 语法，如 '%model%'）
        format: 产物格式（如 'joblib', 'csv', 'json'）
        run_id: 实验运行 ID
        
    Returns:
        List[Dict[str, Any]]: 符合条件的产物信息列表
        
    Examples:
        >>> # 搜索所有模型文件
        >>> models = search_artifacts(format='joblib')
        
        >>> # 搜索包含 'final' 的产物
        >>> final_artifacts = search_artifacts(name_pattern='%final%')
        
        >>> # 搜索特定实验的 CSV 文件
        >>> csv_files = search_artifacts(
        ...     run_id="2023-10-27_14-30-15_single_RF_SNV_none",
        ...     format='csv'
        ... )
    """
    try:
        resolver = get_resolver()
        return resolver.search_artifacts(
            name_pattern=name_pattern,
            format=format,
            run_id=run_id
        )
    except Exception as e:
        logger.error(f"搜索产物失败: {e}")
        raise RuntimeError(f"搜索产物失败: {e}") from e


def print_experiment_summary(run_id: str) -> None:
    """
    打印实验运行的摘要信息。
    
    Args:
        run_id: 实验运行 ID
        
    Examples:
        >>> print_experiment_summary("2023-10-27_14-30-15_single_RF_SNV_none")
        实验运行: 2023-10-27_14-30-15_single_RF_SNV_none
        产物数量: 5
        产物列表:
          - final_model (joblib)
          - predictions (csv)
          - metrics (json)
          - config (json)
          - summary (json)
    """
    if not run_id:
        raise ValueError("run_id 不能为空")
    
    try:
        artifacts = list_artifacts(run_id)
        
        print(f"实验运行: {run_id}")
        print(f"产物数量: {len(artifacts)}")
        
        if artifacts:
            print("产物列表:")
            for artifact in artifacts:
                name = artifact['artifact_name']
                format = artifact['format']
                created_at = artifact.get('created_at', 'Unknown')
                print(f"  - {name} ({format}) - {created_at}")
        else:
            print("  (无产物)")
            
    except Exception as e:
        logger.error(f"打印实验摘要失败: {run_id}, 错误: {e}")
        raise RuntimeError(f"打印实验摘要失败: {e}") from e


# 导出主要的 API
__all__ = [
    'load_artifact',
    'list_artifacts', 
    'list_runs',
    'get_artifact_info',
    'search_artifacts',
    'print_experiment_summary',
    'ArtifactResolver',
    'ArtifactNotFoundError'
]
