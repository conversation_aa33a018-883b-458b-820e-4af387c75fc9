"""
Models package for spectral analysis framework.

This package contains all machine learning models and their training functions.
Models are automatically registered through dynamic imports.
"""

import pkgutil
import importlib
import logging

# Get logger
logger = logging.getLogger(__name__)

# Import model factory functions
from .model_factory import get_model_instance, get_model_predictor, get_available_models, get_model_default_params

# Dynamic model registration
logger.info("Initializing and registering models...")

try:
    # Import all model modules to trigger registration
    for subpackage_name in ["classic_ml", "deep_learning"]:
        try:
            subpackage = importlib.import_module(f".{subpackage_name}", __name__)

            # Walk through all modules in the subpackage
            if hasattr(subpackage, "__path__"):
                for loader, module_name, is_pkg in pkgutil.walk_packages(subpackage.__path__):
                    if not is_pkg and not module_name.startswith("__"):
                        try:
                            full_module_name = f".{subpackage_name}.{module_name}"
                            importlib.import_module(full_module_name, __name__)
                            logger.debug(f"Imported model module: {full_module_name}")
                        except Exception as e:
                            logger.warning(f"Failed to import {full_module_name}: {e}")
        except Exception as e:
            logger.warning(f"Failed to import subpackage {subpackage_name}: {e}")

    logger.info("Model registration completed")

except Exception as e:
    logger.error(f"Error during model registration: {e}")

__all__ = ["get_model_instance", "get_model_predictor", "get_available_models", "get_model_default_params"]
