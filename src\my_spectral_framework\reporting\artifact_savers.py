# src/my_spectral_framework/reporting/artifact_savers.py
"""
数据保存组件模块。

此模块提供了在Scikit-learn管道中用于保存中间数据的转换器组件。
设计理念：遵循Scikit-learn Transformer协议，可以将"保存数据"这一行为
优雅地注入到任何sklearn.pipeline中，实现数据溯源功能。


License: Apache-2.0
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.base import BaseEstimator, TransformerMixin
from typing import Optional, Union, Any
from .handlers import BaseResultHandler


class DataSaver(BaseEstimator, TransformerMixin):
    """
    一个在Scikit-learn管道中用于保存中间数据的转换器。
    它的transform方法会保存传入的数据，然后原封不动地返回。
    
    设计理念：
    1. 遵循Scikit-learn Transformer协议，可以无缝集成到任何Pipeline中
    2. 支持保存带ID的数据，便于溯源和调试
    3. 异常安全：即使保存失败也不会中断主流程
    4. 灵活的数据格式：支持numpy数组和pandas DataFrame
    """

    def __init__(self, handler: BaseResultHandler, artifact_name: str, id_vector: Optional[list] = None):
        """
        初始化DataSaver。

        Args:
            handler (BaseResultHandler): 用于保存产物的结果处理器。
            artifact_name (str): 保存产物时使用的名称。
            id_vector (list, optional): 与数据行对应的光谱ID向量。
        """
        self.handler = handler
        self.artifact_name = artifact_name
        self.id_vector = id_vector

    def fit(self, X: Union[np.ndarray, pd.DataFrame], y: Optional[np.ndarray] = None) -> 'DataSaver':
        """
        fit方法不做任何事，直接返回自身。
        
        Args:
            X: 输入特征数据
            y: 目标变量（可选）
            
        Returns:
            self: 返回自身以支持方法链
        """
        return self

    def transform(self, X: Union[np.ndarray, pd.DataFrame]) -> Union[np.ndarray, pd.DataFrame]:
        """
        transform方法的核心：保存数据，然后原封不动地返回。
        
        Args:
            X: 要保存和传递的数据
            
        Returns:
            X: 原封不动地返回输入数据
        """
        try:
            data_to_save = X
            
            # 如果提供了ID向量，则将其与数据合并为DataFrame进行保存
            if self.id_vector is not None and len(self.id_vector) == X.shape[0]:
                if isinstance(X, pd.DataFrame):
                    # 如果输入已经是DataFrame，直接添加ID列
                    df = X.copy()
                    df.insert(0, 'spectrum_id', self.id_vector)
                else:
                    # 如果输入是numpy数组，转换为DataFrame并添加ID列
                    df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
                    df.insert(0, 'spectrum_id', self.id_vector)
                data_to_save = df
                
                # 使用CSV格式保存带ID的数据
                self.handler.log_artifact(
                    self.artifact_name,
                    data_to_save,
                    format="csv",
                    subdir="debug_artifacts"
                )
            else:
                # 没有ID向量或长度不匹配，直接保存原始数据
                format_type = "csv" if isinstance(data_to_save, pd.DataFrame) else "npy"
                self.handler.log_artifact(
                    self.artifact_name,
                    data_to_save,
                    format=format_type,
                    subdir="debug_artifacts"
                )
                
        except Exception as e:
            # 即使保存失败，也不应中断主流程
            self.handler.logger.error(f"DataSaver failed to save {self.artifact_name}: {e}")
        
        return X

    def fit_transform(self, X: Union[np.ndarray, pd.DataFrame], y: Optional[np.ndarray] = None) -> Union[np.ndarray, pd.DataFrame]:
        """
        组合fit和transform操作。
        
        Args:
            X: 输入特征数据
            y: 目标变量（可选）
            
        Returns:
            X: 转换后的数据（在这个实现中与输入相同）
        """
        return self.fit(X, y).transform(X)

    def get_params(self, deep: bool = True) -> dict:
        """
        获取此估计器的参数。
        
        Args:
            deep: 是否返回子估计器的参数
            
        Returns:
            dict: 参数字典
        """
        return {
            'handler': self.handler,
            'artifact_name': self.artifact_name,
            'id_vector': self.id_vector
        }

    def set_params(self, **params) -> 'DataSaver':
        """
        设置此估计器的参数。
        
        Args:
            **params: 要设置的参数
            
        Returns:
            self: 返回自身
        """
        for key, value in params.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"Invalid parameter {key} for estimator {type(self).__name__}")
        return self

    @staticmethod
    def save_split_indices(train_ids: list, test_ids: list, path: Union[str, Path]) -> None:
        """
        保存数据分割索引到 JSON 文件。

        Args:
            train_ids: 训练集ID列表
            test_ids: 测试集ID列表
            path: 保存路径
        """
        split_data = {
            "train_ids": train_ids,
            "test_ids": test_ids,
            "train_count": len(train_ids),
            "test_count": len(test_ids)
        }

        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(split_data, f, indent=2, ensure_ascii=False)

    @staticmethod
    def save_matrix_csv(name: str, array_or_df: Union[np.ndarray, pd.DataFrame],
                       path: Union[str, Path], id_column: Optional[list] = None,
                       sample_limit: Optional[int] = None) -> None:
        """
        保存矩阵数据到 CSV 文件。

        Args:
            name: 数据名称（用于列命名）
            array_or_df: 要保存的数据
            path: 保存路径
            id_column: 可选的ID列
            sample_limit: 可选的采样限制，避免内存峰值
        """
        path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)

        # 转换为 DataFrame
        if isinstance(array_or_df, np.ndarray):
            df = pd.DataFrame(array_or_df, columns=[f'{name}_feature_{i}' for i in range(array_or_df.shape[1])])
        else:
            df = array_or_df.copy()

        # 添加ID列（如果提供）
        if id_column is not None and len(id_column) == len(df):
            df.insert(0, 'spectrum_id', id_column)

        # 应用采样限制
        if sample_limit is not None and len(df) > sample_limit:
            df = df.sample(n=sample_limit, random_state=42)
            print(f"数据量过大，已采样至 {sample_limit} 行")

        # 保存到 CSV
        df.to_csv(path, index=False)
