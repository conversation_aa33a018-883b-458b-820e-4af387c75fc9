"""
光谱分析框架的数据加载工具模块。

本模块提供从各种数据源加载光谱数据的功能，主要支持 OpenSA 数据集格式。
设计理念：采用分层架构，提供统一的数据加载接口，支持多种数据格式和编码方式，
确保数据加载的鲁棒性和可扩展性。

Author: txy
License: Apache-2.0
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
from numpy.typing import NDArray
import pandas as pd

from ..core.utils import get_logger, get_project_root

logger = get_logger(__name__)


def load_and_process_data(
    data_path: str,
    label_col_name: str,
    spectrum_unique_id_col_name: str,
    group_col_name: Optional[str] = None,
    wave_number_range: Optional[Tuple[float, float]] = None,
    encoding_priority: Optional[List[str]] = None,
    nan_fill_value_spectra: float = 0.0,
    nan_handling_key_cols: str = "remove_row",
    return_type: str = 'tuple',
    spectrum_id_strategy: str = "auto_generate",
) -> Tuple[NDArray[np.floating], NDArray[np.integer], Optional[NDArray[np.integer]], List[str], Dict[str, Any]]:
    """
    从 CSV 文件加载和处理光谱数据，基于列名格式智能分离元数据和光谱特征。
    此函数现在负责实现 spectrum_id_strategy。

    设计思路：
    1. 智能列识别：通过尝试将列名转换为浮点数来区分光谱列和元数据列
    2. 多编码支持：按优先级尝试不同编码方式，确保文件读取成功
    3. 灵活的 NaN 处理：支持删除行或填充默认值两种策略
    4. 波数范围过滤：支持按波数范围筛选光谱特征
    5. 完整的元数据记录：保存加载过程中的所有关键信息
    6. ID管理策略：支持从列读取或自动生成光谱ID

    Args:
        data_path: CSV 文件路径
        label_col_name: 包含标签的列名
        spectrum_unique_id_col_name: 包含唯一光谱 ID 的列名
        group_col_name: 标识样本组的列名（可选）
        wave_number_range: 用于过滤的波数范围元组 (最小值, 最大值)（可选）
        encoding_priority: 读取 CSV 时尝试的编码列表
        nan_fill_value_spectra: 光谱列中 NaN 的填充值（默认：0.0）
        nan_handling_key_cols: 关键列中 NaN 的处理方式（'remove_row' 或 'fill'）
        return_type: 返回类型，'tuple' 返回元组，'object' 返回 SpectralData 对象
        spectrum_id_strategy: ID策略，'from_column' 或 'auto_generate'

    Returns:
        当 return_type='tuple' 时，返回元组 (X, y, groups, ids, metadata)：
        当 return_type='object' 时，返回 SpectralData 对象：
        - X: 特征矩阵 (n_samples, n_features)
        - y: 目标向量 (n_samples,)
        - groups: 组标识符 (n_samples,) 或 None
        - ids: 光谱ID列表 (n_samples,)
        - metadata: 包含数据信息的字典

    Raises:
        FileNotFoundError: 数据文件未找到时抛出
        ValueError: 缺少必需列或未找到光谱列时抛出
    """
    file_to_load = Path(data_path)  # 确保使用 Path 对象处理路径
    logger.info(f"--- 开始高级数据加载和处理，来源：{file_to_load} ---")

    if not file_to_load.exists():
        logger.error(f"文件未找到：{file_to_load}")
        raise FileNotFoundError(f"错误：文件未找到 {file_to_load}")

    if encoding_priority is None:
        encoding_priority = ["utf-8-sig", "gbk", "utf-8", "latin1"]

    # 尝试使用不同编码读取 CSV 文件
    raw_df: Optional[pd.DataFrame] = None
    for encoding in encoding_priority:
        try:
            raw_df = pd.read_csv(file_to_load, encoding=encoding, low_memory=False)
            logger.info(f"成功使用 '{encoding}' 编码读取 CSV。形状：{raw_df.shape}")
            break
        except Exception as e:
            logger.debug(f"使用 '{encoding}' 编码失败：{e}")
            if encoding == encoding_priority[-1]:
                logger.error(f"所有尝试的编码都无法读取 CSV 文件。", exc_info=True)
                raise IOError(f"错误：无法读取 CSV 文件 {file_to_load}。") from e

    # 确保 raw_df 不为 None
    if raw_df is None:
        raise IOError(f"错误：无法读取 CSV 文件 {file_to_load}。")

    # 识别光谱列和元数据列
    spectral_col_names = []
    metadata_col_names = []

    for col in raw_df.columns:
        try:
            # 尝试将列名转换为浮点数（波数）
            float(col)
            spectral_col_names.append(col)
        except (ValueError, TypeError):
            # 检查是否为特征列（如 feature_0, feature_1 等）
            col_str = str(col).lower()
            if col_str.startswith("feature_") or col_str.startswith("wavelength_") or col_str.startswith("wl_"):
                spectral_col_names.append(col)
            else:
                # 列名不是数字或特征，视为元数据
                metadata_col_names.append(str(col))

    if not spectral_col_names:
        raise ValueError("错误：未找到光谱列（列名无法转换为浮点数）。")

    logger.info(f"识别出 {len(spectral_col_names)} 个光谱列和 {len(metadata_col_names)} 个元数据列。")

    # --- ID 处理 ---
    if spectrum_id_strategy == "from_column":
        if not spectrum_unique_id_col_name or spectrum_unique_id_col_name not in raw_df.columns:
            raise ValueError(f"ID列 '{spectrum_unique_id_col_name}' 在文件中不存在。")
        ids = raw_df[spectrum_unique_id_col_name].astype(str).tolist()
        logger.info(f"已从 '{spectrum_unique_id_col_name}' 列加载 {len(ids)} 个光谱ID。")
    else:  # auto_generate
        num_samples = len(raw_df)
        ids = [f"auto_spec_{i+1:04d}" for i in range(num_samples)]
        logger.info(f"已自动生成 {len(ids)} 个光谱ID。")

    # 验证必需列是否存在
    key_cols_to_check = [label_col_name]
    if spectrum_id_strategy == "from_column":
        key_cols_to_check.append(spectrum_unique_id_col_name)
    if group_col_name:
        key_cols_to_check.append(group_col_name)

    missing_cols = [col for col in key_cols_to_check if col not in raw_df.columns]
    if missing_cols:
        raise ValueError(f"错误：缺少必需列：{missing_cols}")

    # 处理关键列中的 NaN 值
    initial_rows = len(raw_df)
    key_cols_for_nan_check = [label_col_name]
    if spectrum_id_strategy == "from_column":
        key_cols_for_nan_check.append(spectrum_unique_id_col_name)
    if group_col_name:
        key_cols_for_nan_check.append(group_col_name)

    if nan_handling_key_cols == "remove_row":
        raw_df.dropna(subset=key_cols_for_nan_check, inplace=True)
        rows_removed = initial_rows - len(raw_df)
        if rows_removed > 0:
            logger.info(f"由于关键列中存在 NaN，删除了 {rows_removed} 行。")
            # 如果是自动生成ID，需要重新生成以匹配删除后的行数
            if spectrum_id_strategy == "auto_generate":
                num_samples = len(raw_df)
                ids = [f"auto_spec_{i+1:04d}" for i in range(num_samples)]
                logger.info(f"重新生成 {len(ids)} 个光谱ID以匹配删除后的数据。")
    elif nan_handling_key_cols == "fill":
        # 使用适当的默认值填充关键列中的 NaN
        for col in key_cols_for_nan_check:
            if raw_df[col].isna().any():
                if col == label_col_name:
                    raw_df[col].fillna("unknown", inplace=True)
                elif col == spectrum_unique_id_col_name:
                    raw_df[col].fillna(f"spectrum_{raw_df.index}", inplace=True)
                elif col == group_col_name:
                    raw_df[col].fillna("unknown_group", inplace=True)
        logger.info(f"使用默认值填充了关键列中的 NaN。")
    else:
        raise ValueError(f"无效的 nan_handling_key_cols：{nan_handling_key_cols}。请使用 'remove_row' 或 'fill'。")

    if raw_df.empty:
        raise ValueError("错误：处理关键列中的 NaN 后没有剩余数据。")

    # 处理波数以进行过滤
    wave_numbers = []
    numeric_wave_numbers = True

    for col in spectral_col_names:
        try:
            # 尝试转换为浮点数（实际波数）
            wave_numbers.append(float(col))
        except (ValueError, TypeError):
            # 不是数值波数，使用索引代替
            numeric_wave_numbers = False
            break

    if not numeric_wave_numbers:
        # 对于像 feature_0, feature_1 这样的特征列，创建合成波数
        wave_numbers = list(range(len(spectral_col_names)))
        logger.info(f"为 {len(spectral_col_names)} 个特征列使用合成波数（索引）")
    else:
        wave_numbers = np.array(wave_numbers)
        logger.info(f"使用列名中的实际波数")

    # 如果指定了波数范围且有数值波数，则按波数范围过滤
    if wave_number_range and numeric_wave_numbers:
        w_min, w_max = min(wave_number_range), max(wave_number_range)
        mask = (wave_numbers >= w_min) & (wave_numbers <= w_max)
        spectral_col_names = list(np.array(spectral_col_names)[mask])
        wave_numbers = wave_numbers[mask]

        if len(spectral_col_names) == 0:
            raise ValueError(f"按范围 {wave_number_range} 过滤后没有剩余的光谱特征。")

        logger.info(f"过滤到范围 {wave_number_range} 内的 {len(spectral_col_names)} 个特征。")
    elif wave_number_range and not numeric_wave_numbers:
        logger.warning(f"非数值列名不支持波数范围过滤。使用所有特征。")

    # 创建最终数组并增强 NaN 处理
    X = raw_df[spectral_col_names].fillna(nan_fill_value_spectra).to_numpy()
    y = raw_df[label_col_name].to_numpy()
    groups = raw_df[group_col_name].to_numpy() if group_col_name else None

    # 记录 NaN 处理统计信息
    nan_count = raw_df[spectral_col_names].isna().sum().sum()
    if nan_count > 0:
        logger.info(f"使用 {nan_fill_value_spectra} 填充了光谱数据中的 {nan_count} 个 NaN 值")

    # 创建元数据字典
    data_metadata = {
        "source_file": str(file_to_load),
        "source_type": "file",
        "n_samples": X.shape[0],
        "n_features": X.shape[1],
        "label_col_name": label_col_name,
        "group_col_name": group_col_name,
        "spectrum_unique_id_col_name": spectrum_unique_id_col_name,
        "wave_numbers": wave_numbers.tolist() if hasattr(wave_numbers, "tolist") else wave_numbers,
        "spectral_columns": spectral_col_names,
        "metadata_columns": metadata_col_names,
        "wave_number_range": wave_number_range,
    }

    logger.info(f"高级数据处理完成。最终形状：X={X.shape}, y={y.shape}")
    if groups is not None:
        unique_groups = len(np.unique(groups))
        logger.info(f"组：识别出 {unique_groups} 个唯一组")

    # 支持返回 SpectralData 对象
    if return_type == 'object':
        from .core import SpectralData
        return SpectralData(
            X=X,
            y=y,
            wavelengths=data_metadata.get('wave_numbers'),
            metadata={k: v for k, v in data_metadata.items() if k != 'wave_numbers'}
        )

    return X, y, groups, ids, data_metadata


def load_spectral_data(data_type: str, data_path: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    根据数据类型加载光谱数据。

    设计思路：提供统一的接口加载不同类型的光谱数据，自动查找 OpenSA 数据目录，
    支持分类和回归两种任务类型。

    Args:
        data_type: 要加载的数据类型（"Cls" 表示分类，"Rgs" 表示回归）
        data_path: 自定义数据目录路径（可选）

    Returns:
        返回元组 (X, y)，其中 X 是特征矩阵，y 是目标向量

    Raises:
        ValueError: 不支持的数据类型时抛出
        FileNotFoundError: 数据文件未找到时抛出
    """
    if data_type not in ["Cls", "Rgs"]:
        raise ValueError(f"不支持的数据类型：{data_type}。必须是 'Cls' 或 'Rgs'")

    # 确定数据路径
    resolved_data_path: Path
    if data_path is None:
        # 尝试相对于项目根目录查找 OpenSA 数据目录
        project_root = get_project_root()
        opensa_data_path = project_root / "OpenSA" / "Data"

        if not opensa_data_path.exists():
            # 尝试替代路径结构
            opensa_data_path = project_root.parent / "OpenSA" / "Data"

        if not opensa_data_path.exists():
            raise FileNotFoundError(
                f"未找到 OpenSA 数据目录。请确保 OpenSA/Data 存在 "
                f"或提供自定义的 data_path。搜索路径：{project_root / 'OpenSA' / 'Data'}, "
                f"{project_root.parent / 'OpenSA' / 'Data'}"
            )
        resolved_data_path = opensa_data_path
    else:
        resolved_data_path = Path(data_path)

    logger.info(f"从以下位置加载 {data_type} 数据：{resolved_data_path}")

    if data_type == "Rgs":
        return _load_regression_data(resolved_data_path)
    else:  # data_type == "Cls"
        return _load_classification_data(resolved_data_path)


def _load_regression_data(data_path: Path) -> Tuple[np.ndarray, np.ndarray]:
    """
    从 OpenSA 格式加载回归数据。

    设计思路：按照 OpenSA 数据集的标准格式，加载并合并多个回归数据文件，
    提取特征和标签，确保数据格式的一致性。

    Args:
        data_path: 数据目录路径

    Returns:
        返回回归数据的元组 (X, y)
    """
    rgs_path = data_path / "Rgs"

    # 定义文件路径
    cdata1_path = rgs_path / "Cdata1.csv"
    vdata1_path = rgs_path / "Vdata1.csv"
    tdata1_path = rgs_path / "Tdata1.csv"

    # 检查文件是否存在
    for file_path in [cdata1_path, vdata1_path, tdata1_path]:
        if not file_path.exists():
            raise FileNotFoundError(f"未找到必需的数据文件：{file_path}")

    try:
        # 加载数据文件
        logger.info("正在加载回归数据文件...")
        cdata1 = np.loadtxt(cdata1_path, dtype=np.float64, delimiter=",", skiprows=0)
        vdata1 = np.loadtxt(vdata1_path, dtype=np.float64, delimiter=",", skiprows=0)
        tdata1 = np.loadtxt(tdata1_path, dtype=np.float64, delimiter=",", skiprows=0)

        # 连接数据
        nirdata1 = np.concatenate((cdata1, vdata1))
        nirdata = np.concatenate((nirdata1, tdata1))

        # 提取特征和标签
        # 特征是除最后 4 列外的所有列，标签是最后一列
        data = nirdata[:, :-4]
        label = nirdata[:, -1]

        logger.info(f"已加载回归数据：{data.shape[0]} 个样本，{data.shape[1]} 个特征")

        return data, label

    except Exception as e:
        raise RuntimeError(f"加载回归数据时出错：{str(e)}")


def _load_classification_data(data_path: Path) -> Tuple[np.ndarray, np.ndarray]:
    """
    从 OpenSA 格式加载分类数据。

    设计思路：按照 OpenSA 数据集的标准格式，从单个表格文件中加载分类数据，
    自动分离特征和标签列。

    Args:
        data_path: 数据目录路径

    Returns:
        返回分类数据的元组 (X, y)
    """
    cls_path = data_path / "Cls"
    table_path = cls_path / "table.csv"

    # 检查文件是否存在
    if not table_path.exists():
        raise FileNotFoundError(f"未找到必需的数据文件：{table_path}")

    try:
        # 加载数据文件
        logger.info("正在加载分类数据文件...")
        nirdata = np.loadtxt(table_path, dtype=np.float64, delimiter=",", skiprows=0)

        # 提取特征和标签
        # 特征是除最后一列外的所有列，标签是最后一列
        data = nirdata[:, :-1]
        label = nirdata[:, -1]

        logger.info(f"已加载分类数据：{data.shape[0]} 个样本，{data.shape[1]} 个特征")

        return data, label

    except Exception as e:
        raise RuntimeError(f"加载分类数据时出错：{str(e)}")


def load_custom_data(
    file_path: Union[str, Path],
    feature_columns: Optional[Union[List, slice]] = None,
    target_column: Union[int, str] = -1,
    delimiter: str = ",",
    skiprows: int = 0,
) -> Tuple[np.ndarray, np.ndarray]:
    """
    从文件加载自定义光谱数据。

    设计思路：提供灵活的数据加载接口，支持自定义特征列和目标列的选择，
    适应不同的数据格式和文件结构。

    Args:
        file_path: 数据文件路径
        feature_columns: 用作特征的列（默认：除最后一列外的所有列）
        target_column: 用作目标的列（默认：最后一列）
        delimiter: 文件中使用的分隔符
        skiprows: 开头要跳过的行数

    Returns:
        返回元组 (X, y)，其中 X 是特征矩阵，y 是目标向量
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"数据文件未找到：{file_path}")

    try:
        # 加载数据
        logger.info(f"从以下位置加载自定义数据：{file_path}")

        if file_path.suffix.lower() == ".csv":
            data = np.loadtxt(file_path, dtype=np.float64, delimiter=delimiter, skiprows=skiprows)
        else:
            # 对其他格式尝试使用 pandas
            df = pd.read_csv(file_path, delimiter=delimiter, skiprows=skiprows)
            data = df.values.astype(np.float64)

        # 提取特征和目标
        if feature_columns is None:
            # 使用除目标列外的所有列
            if target_column == -1:
                X = data[:, :-1]
            else:
                feature_indices = [i for i in range(data.shape[1]) if i != target_column]
                X = data[:, feature_indices]
        else:
            X = data[:, feature_columns]

        # 处理目标列的类型问题
        if isinstance(target_column, str):
            # 如果是字符串，需要特殊处理（这里假设是数值索引）
            raise ValueError("字符串类型的目标列索引在此函数中不受支持")
        y = data[:, target_column]

        logger.info(f"已加载自定义数据：{X.shape[0]} 个样本，{X.shape[1]} 个特征")

        return X, y

    except Exception as e:
        raise RuntimeError(f"加载自定义数据时出错：{str(e)}")


def load_spectral_data_from_file(
    file_path: Union[str, Path],
    has_header: bool = True,
    label_col: Union[int, str] = "last",
    delimiter: str = ",",
    wavelength_row: bool = False,
) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
    """
    从 CSV 文件加载光谱数据。

    设计思路：提供灵活的文件加载接口，支持多种文件格式和结构，
    自动处理标题行和波长行，生成完整的元数据信息。

    Args:
        file_path: 数据文件路径
        has_header: 文件是否有标题行
        label_col: 包含标签的列（'last'、'first' 或列索引）
        delimiter: 文件中使用的分隔符
        wavelength_row: 第一行是否包含波长信息

    Returns:
        返回元组 (X, y, metadata)，其中 X 是特征，y 是目标，metadata 是文件信息
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"数据文件未找到：{file_path}")

    logger.info(f"从文件加载光谱数据：{file_path}")

    try:
        # 使用 pandas 加载数据以获得更好的处理能力
        skiprows = 0
        if has_header:
            skiprows = 1
        if wavelength_row:
            skiprows += 1

        df = pd.read_csv(file_path, delimiter=delimiter, skiprows=skiprows, header=None)
        data = df.values.astype(np.float64)

        # 提取特征和目标
        if label_col == "last":
            X = data[:, :-1]
            y = data[:, -1]
        elif label_col == "first":
            X = data[:, 1:]
            y = data[:, 0]
        else:
            # 特定列索引
            if isinstance(label_col, str):
                raise ValueError("字符串类型的标签列索引在此函数中不受支持")
            feature_cols = [i for i in range(data.shape[1]) if i != label_col]
            X = data[:, feature_cols]
            y = data[:, label_col]

        # 创建元数据
        metadata = {
            "source_file": str(file_path),
            "file_size_bytes": file_path.stat().st_size,
            "n_samples": X.shape[0],
            "n_features": X.shape[1],
            "has_header": has_header,
            "label_col": label_col,
            "delimiter": delimiter,
            "wavelength_row": wavelength_row,
            "load_timestamp": datetime.now().isoformat(),
        }

        logger.info(f"从文件加载数据：{X.shape[0]} 个样本，{X.shape[1]} 个特征")
        return X, y, metadata

    except Exception as e:
        raise RuntimeError(f"从文件 {file_path} 加载数据时出错：{str(e)}")





def validate_data(X: np.ndarray, y: np.ndarray) -> None:
    """
    验证加载的数据是否存在常见问题。

    设计思路：提供全面的数据质量检查，包括形状匹配、异常值检测、
    数据范围统计和常量特征识别，确保数据质量符合机器学习要求。

    Args:
        X: 特征矩阵
        y: 目标向量

    Raises:
        ValueError: 数据验证失败时抛出
    """
    # 检查形状
    if X.shape[0] != y.shape[0]:
        raise ValueError(
            f"特征矩阵和目标向量的样本数量不同：{X.shape[0]} vs {y.shape[0]}"
        )

    # 检查 NaN 值
    if np.isnan(X).any():
        logger.warning("特征矩阵包含 NaN 值")

    if np.isnan(y).any():
        logger.warning("目标向量包含 NaN 值")

    # 检查无穷值
    if np.isinf(X).any():
        logger.warning("特征矩阵包含无穷值")

    if np.isinf(y).any():
        logger.warning("目标向量包含无穷值")

    # 记录数据统计信息
    logger.info(f"数据验证通过：{X.shape[0]} 个样本，{X.shape[1]} 个特征")
    logger.info(f"特征范围：[{np.min(X):.6f}, {np.max(X):.6f}]")
    logger.info(f"目标范围：[{np.min(y):.6f}, {np.max(y):.6f}]")

    # 检查常量特征
    constant_features = np.var(X, axis=0) == 0
    if constant_features.any():
        n_constant = np.sum(constant_features)
        logger.warning(f"发现 {n_constant} 个可能需要移除的常量特征")
