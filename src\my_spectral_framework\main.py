# src/my_spectral_framework/main.py
"""
光谱分析框架的主执行模块。

本模块提供核心执行功能，能够接收配置字典并端到端地运行实验。
它是程序化执行实验的主要入口点。

作者: txy
"""

import importlib
from typing import Dict, Any
from pathlib import Path

from my_spectral_framework.core.utils import get_logger

logger = get_logger(__name__)


def load_config_by_path(config_path: str) -> Dict[str, Any]:
    """
    从模块路径加载配置。
    
    参数:
        config_path: 模块路径，例如 'conf.runs.classification.experiment_cls_rf'
        
    返回:
        包含模块中 EXPERIMENT_CONFIG 的字典
        
    抛出:
        ImportError: 如果模块无法导入
        AttributeError: 如果在模块中未找到 EXPERIMENT_CONFIG
    """
    try:
        config_module = importlib.import_module(config_path)
        
        if not hasattr(config_module, "EXPERIMENT_CONFIG"):
            raise AttributeError(f"Module '{config_path}' does not have 'EXPERIMENT_CONFIG' attribute")
        
        config_dict = config_module.EXPERIMENT_CONFIG
        logger.info(f"Successfully loaded config from: {config_path}")
        return config_dict
        
    except ImportError as e:
        logger.error(f"Failed to import config module '{config_path}': {e}")
        raise
    except AttributeError as e:
        logger.error(f"Config loading error: {e}")
        raise


def run_experiment_from_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    核心调度函数：根据配置运行真实的机器学习实验。

    这个函数作为框架的主入口点，负责：
    1. 验证配置的完整性
    2. 根据 experiment_type 选择合适的运行器
    3. 创建执行引擎
    4. 调度实验执行

    Args:
        config: 完整的实验配置字典

    Returns:
        Dict containing experiment results and status

    Raises:
        ValueError: If the configuration is invalid
        ImportError: If required modules cannot be imported
    """
    import time
    from .core.config_models import ExperimentRootConfig
    from .core.engine import create_engine
    from .runners import SingleRunRunner, CrossValidationRunner, HyperOptRunner

    logger.info("Starting experiment execution from configuration")

    # 1. 基础配置验证
    required_sections = [
        "experiment_config",
        "data_config",
        "preprocessing_config",
        "feature_selection_config",
        "engine_config"
    ]

    for section in required_sections:
        if section not in config:
            raise ValueError(f"Missing required configuration section: {section}")

    # 2. 提取关键信息用于日志记录
    experiment_name = config.get("experiment_config", {}).get("name", "unnamed_experiment")
    experiment_type = config.get("experiment_type", "single_run")
    engine_type = config.get("engine_config", {}).get("engine", "sklearn")

    # 获取模型名称（支持不同的配置结构）
    engine_config = config.get("engine_config", {})
    if "ml_model_config" in engine_config:
        model_name = engine_config["ml_model_config"].get("name", "unknown")
    elif "pytorch_model_config" in engine_config:
        model_name = engine_config["pytorch_model_config"].get("architecture", "unknown")
    else:
        model_name = "unknown"

    logger.info(f"Experiment: {experiment_name}")
    logger.info(f"Type: {experiment_type}")
    logger.info(f"Model: {model_name}")
    logger.info(f"Engine: {engine_type}")

    try:
        # 3. 验证配置结构（使用 Pydantic）
        config_obj = ExperimentRootConfig.model_validate(config)
        logger.info("Configuration validation passed (Pydantic)")

        # 4. 创建执行引擎
        engine = create_engine(engine_type)
        logger.info(f"Created execution engine: {type(engine).__name__}")

        # 5. 根据实验类型选择运行器
        runner_map = {
            "single_run": SingleRunRunner,
            "cross_validation": CrossValidationRunner,
            "hyperparameter_optimization": HyperOptRunner,
        }

        if experiment_type not in runner_map:
            raise ValueError(f"Unsupported experiment_type: '{experiment_type}'. "
                           f"Supported types: {list(runner_map.keys())}")

        runner_class = runner_map[experiment_type]
        logger.info(f"Selected runner: {runner_class.__name__}")

        # 6. 创建并运行实验
        runner = runner_class(config_obj, engine)
        logger.info(f"Created runner instance: {runner_class.__name__}")

        # 执行实验
        start_time = time.time()
        results = runner.run()
        execution_time = time.time() - start_time

        # 7. 增强结果信息
        enhanced_results = {
            "status": "success",
            "experiment_name": experiment_name,
            "experiment_type": experiment_type,
            "model_name": model_name,
            "engine_type": engine_type,
            "runner_type": runner_class.__name__,
            "execution_time": execution_time,
            **results  # 合并运行器返回的结果
        }

        logger.info(f"Experiment '{experiment_name}' completed successfully")
        return enhanced_results

    except Exception as e:
        logger.error(f"Experiment execution failed: {e}", exc_info=True)
        return {
            "status": "failed",
            "experiment_name": experiment_name,
            "experiment_type": experiment_type,
            "error": str(e),
            "error_type": type(e).__name__
        }
