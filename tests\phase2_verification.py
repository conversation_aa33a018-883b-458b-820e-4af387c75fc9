#!/usr/bin/env python3
"""
Phase 2 验证脚本：训练过程结构化日志与数据追踪
验证所有 Phase 2 功能是否正确实现
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """测试所有修改的模块是否可以正常导入"""
    print("🔍 测试模块导入...")
    
    try:
        from my_spectral_framework.models.deep_learning.trainer import StandardTrainer
        from my_spectral_framework.optimization.sklearn_wrappers import PyTorchSklearnWrapper
        from my_spectral_framework.reporting.artifact_savers import DataSaver
        from my_spectral_framework.evaluation.evaluators import HoldOutEvaluator
        from my_spectral_framework.reporting.handlers import FileSystemResultHandler
        from my_spectral_framework.core.config_adapter import ConfigAdapter
        print("✅ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_trainer_handler_support():
    """测试 StandardTrainer 是否支持 handler 参数"""
    print("🔍 测试 StandardTrainer handler 支持...")
    
    try:
        import torch
        from my_spectral_framework.models.deep_learning.trainer import StandardTrainer
        
        # 创建一个模拟的 handler
        class MockHandler:
            def log_metric(self, name, value):
                print(f"  📊 记录指标: {name} = {value}")
        
        handler = MockHandler()
        trainer = StandardTrainer(
            optimizer_class=torch.optim.Adam,
            criterion=torch.nn.CrossEntropyLoss(),
            device='cpu',
            epochs=1,
            learning_rate=0.001,
            handler=handler
        )
        
        print("✅ StandardTrainer 支持 handler 参数")
        return True
    except Exception as e:
        print(f"❌ StandardTrainer handler 测试失败: {e}")
        return False

def test_pytorch_wrapper_handler():
    """测试 PyTorchSklearnWrapper 是否支持 handler 参数"""
    print("🔍 测试 PyTorchSklearnWrapper handler 支持...")
    
    try:
        import torch
        import torch.nn as nn
        from my_spectral_framework.optimization.sklearn_wrappers import PyTorchSklearnWrapper
        
        # 创建一个简单的模型
        class SimpleModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = nn.Linear(10, 2)
                self.task_type = 'classification'
            
            def forward(self, x):
                return self.linear(x)
        
        model = SimpleModel()
        train_params = {'epochs': 1, 'batch_size': 32, 'learning_rate': 0.001}
        
        # 创建模拟 handler
        class MockHandler:
            def log_metric(self, name, value):
                pass
        
        wrapper = PyTorchSklearnWrapper(model, train_params, handler=MockHandler())
        
        print("✅ PyTorchSklearnWrapper 支持 handler 参数")
        return True
    except Exception as e:
        print(f"❌ PyTorchSklearnWrapper handler 测试失败: {e}")
        return False

def test_data_saver_methods():
    """测试 DataSaver 新增的静态方法"""
    print("🔍 测试 DataSaver 新增方法...")
    
    try:
        import numpy as np
        import pandas as pd
        from my_spectral_framework.reporting.artifact_savers import DataSaver
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 测试 save_split_indices
            train_ids = ['train_1', 'train_2', 'train_3']
            test_ids = ['test_1', 'test_2']
            split_path = temp_path / "split_indices.json"
            
            DataSaver.save_split_indices(train_ids, test_ids, split_path)
            
            # 验证文件是否创建并包含正确内容
            assert split_path.exists(), "split_indices.json 文件未创建"
            
            with open(split_path, 'r') as f:
                split_data = json.load(f)
            
            assert split_data['train_ids'] == train_ids
            assert split_data['test_ids'] == test_ids
            assert split_data['train_count'] == 3
            assert split_data['test_count'] == 2
            
            # 测试 save_matrix_csv
            test_matrix = np.random.rand(5, 3)
            matrix_path = temp_path / "test_matrix.csv"
            
            DataSaver.save_matrix_csv("test", test_matrix, matrix_path, 
                                    id_column=['id_1', 'id_2', 'id_3', 'id_4', 'id_5'])
            
            # 验证 CSV 文件
            assert matrix_path.exists(), "matrix CSV 文件未创建"
            df = pd.read_csv(matrix_path)
            assert 'spectrum_id' in df.columns
            assert len(df) == 5
            
        print("✅ DataSaver 新增方法测试通过")
        return True
    except Exception as e:
        print(f"❌ DataSaver 方法测试失败: {e}")
        return False

def test_config_adapter_traceability():
    """测试 ConfigAdapter 的 traceability_level 支持"""
    print("🔍 测试 ConfigAdapter traceability_level...")
    
    try:
        from my_spectral_framework.core.config_adapter import ConfigAdapter
        
        # 测试 dict 格式配置
        dict_config = {
            "traceability_config": {
                "level": "full"
            }
        }
        
        adapter = ConfigAdapter(dict_config)
        level = adapter.get_traceability_level()
        assert level == "full", f"期望 'full'，得到 '{level}'"
        
        # 测试默认值
        empty_config = {}
        adapter2 = ConfigAdapter(empty_config)
        level2 = adapter2.get_traceability_level()
        assert level2 == "none", f"期望 'none'，得到 '{level2}'"
        
        print("✅ ConfigAdapter traceability_level 测试通过")
        return True
    except Exception as e:
        print(f"❌ ConfigAdapter traceability_level 测试失败: {e}")
        return False

def main():
    """运行所有验证测试"""
    print("🚀 开始 Phase 2 验证测试...")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_trainer_handler_support,
        test_pytorch_wrapper_handler,
        test_data_saver_methods,
        test_config_adapter_traceability,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Phase 2 所有功能验证通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
