"""
光谱分析框架的光谱预处理函数模块。

本模块包含从 OpenSA 改编的各种预处理方法，包括归一化、平滑、导数和散射校正方法。
设计理念：提供统一的 sklearn 兼容接口，支持管道化处理，确保预处理方法的可重用性和可扩展性。

Author: txy
License: Apache-2.0
"""

from copy import deepcopy
from typing import Any, Dict, Optional, Union

import numpy as np
from numpy.typing import NDArray
import pandas as pd
from scipy import signal
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import MinMaxScaler, StandardScaler

from ..core.utils import get_logger
from ..core.registry import register_preprocessing, get_preprocessing_function

logger = get_logger(__name__)


def MMS(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Min-Max Scaling归一化。
    此函数主要由 MMSTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        MinMaxScaler处理后的数据，形状 (n_samples, n_features)
    """
    return MinMaxScaler().fit_transform(data)


def SS(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Standard Scaling标准化。
    此函数主要由 SSTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        StandardScaler处理后的数据，形状 (n_samples, n_features)
    """
    return StandardScaler().fit_transform(data)


def CT(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Mean Centering均值中心化。
    此函数主要由 CTTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        均值中心化处理后的数据，形状 (n_samples, n_features)
    """
    data_copy = data.copy()
    for i in range(data_copy.shape[0]):
        mean_val = np.mean(data_copy[i])
        data_copy[i] = data_copy[i] - mean_val
    return data_copy


def SNV(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Standard Normal Variate标准正态变量变换。
    此函数主要由 SNVTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        SNV变换处理后的数据，形状 (n_samples, n_features)
    """
    # 确保数据为浮点类型以进行正确计算
    data = np.array(data, dtype=np.float64)
    m, n = data.shape
    logger.debug(f"SNV 处理：{m} 个样本，{n} 个特征")

    # 计算每个光谱的标准差和均值
    data_std = np.std(data, axis=1, ddof=1)  # 每个光谱的样本标准差
    data_average = np.mean(data, axis=1)  # 每个光谱的均值

    # SNV 计算，处理零标准差的情况
    data_snv = np.zeros_like(data)
    for i in range(m):
        if data_std[i] == 0:
            # 如果标准差为 0（常量光谱），设置为零
            data_snv[i] = np.zeros(n)
        else:
            # 正常的 SNV 计算
            data_snv[i] = (data[i] - data_average[i]) / data_std[i]

    return data_snv


def MA(data: NDArray[np.floating], WSZ: int = 11) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Moving Average移动平均平滑。
    此函数主要由 MATransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)
        WSZ: 窗口大小（必须为奇数）

    Returns:
        移动平均处理后的数据，形状 (n_samples, n_features)
    """
    data_copy = data.copy()

    for i in range(data_copy.shape[0]):
        out0 = np.convolve(data_copy[i], np.ones(WSZ, dtype=int), "valid") / WSZ
        r = np.arange(1, WSZ - 1, 2)
        start = np.cumsum(data_copy[i, : WSZ - 1])[::2] / r
        stop = (np.cumsum(data_copy[i, :-WSZ:-1])[::2] / r)[::-1]
        data_copy[i] = np.concatenate((start, out0, stop))

    return data_copy


def SG(data: NDArray[np.floating], w: int = 11, p: int = 2) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Savitzky-Golay平滑滤波。
    此函数主要由 SGTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)
        w: 窗口长度（必须为奇数）
        p: 多项式阶数

    Returns:
        SG滤波处理后的数据，形状 (n_samples, n_features)
    """
    return signal.savgol_filter(data, w, p)


def D1(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 计算一阶导数。
    此函数主要由 D1Transformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        一阶导数数据，形状 (n_samples, n_features-1)
    """
    n, p = data.shape
    Di = np.ones((n, p - 1))
    for i in range(n):
        Di[i] = np.diff(data[i])
    return Di


def D2(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 计算二阶导数。
    此函数主要由 D2Transformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        二阶导数数据，形状 (n_samples, n_features-2)
    """
    data_copy = deepcopy(data)
    if isinstance(data_copy, pd.DataFrame):
        data_copy = data_copy.values

    temp2 = (pd.DataFrame(data_copy)).diff(axis=1)
    temp3 = np.delete(temp2.values, 0, axis=1)
    temp4 = (pd.DataFrame(temp3)).diff(axis=1)
    spec_D2 = np.delete(temp4.values, 0, axis=1)

    return spec_D2


def DT(data: NDArray[np.floating]) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行去趋势（趋势校正）。
    此函数主要由 DTTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)

    Returns:
        去趋势处理后的数据，形状 (n_samples, n_features)
    """
    length = data.shape[1]
    x = np.asarray(range(length), dtype=np.float32)
    out = np.array(data, copy=True)

    lr = LinearRegression()
    for i in range(out.shape[0]):
        lr.fit(x.reshape(-1, 1), out[i].reshape(-1, 1))
        k = lr.coef_[0]
        b = lr.intercept_[0]
        for j in range(out.shape[1]):
            out[i][j] = out[i][j] - (j * k + b)

    return out


def MSC(data: NDArray[np.floating], mean_spectrum: Optional[NDArray[np.floating]] = None) -> NDArray[np.floating]:
    """
    [内部辅助函数] 执行Multiplicative Scatter Correction多元散射校正。
    此函数主要由 MSCTransformer 包装器调用。外部不应直接调用此函数。

    Args:
        data: 原始光谱数据，形状 (n_samples, n_features)
        mean_spectrum: 可选的预计算平均光谱。如果为None，则从数据中计算。

    Returns:
        MSC校正后的数据，形状 (n_samples, n_features)
    """
    n, p = data.shape
    msc = np.ones((n, p))

    # 使用提供的平均光谱或从数据中计算
    if mean_spectrum is None:
        mean_spectrum = np.mean(data, axis=0)

    # 对每个光谱进行线性拟合
    lr = LinearRegression()
    for i in range(n):
        y = data[i, :]
        lr.fit(mean_spectrum.reshape(-1, 1), y.reshape(-1, 1))
        k = lr.coef_[0]
        b = lr.intercept_[0]
        msc[i, :] = (y - b) / k

    return msc


# apply_processor 函数已作为架构统一的一部分被移除。
# 请使用 get_transformer() 工厂函数来获取 sklearn 兼容的变换器。


def get_available_methods() -> list:
    """
    获取可用预处理方法的列表。

    Returns:
        可用方法名称的列表
    """
    return ["None", "MMS", "SS", "CT", "SNV", "MA", "SG", "D1", "D2", "DT", "MSC"]


def validate_preprocessing_params(method: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证并设置预处理方法的默认参数。

    Args:
        method: 预处理方法名称
        params: 方法参数

    Returns:
        带有默认值的验证参数
    """
    validated_params = params.copy()

    if method == "MA":
        if "WSZ" not in validated_params:
            validated_params["WSZ"] = 11
        elif validated_params["WSZ"] % 2 == 0:
            logger.warning("MA 窗口大小应为奇数，调整为 +1")
            validated_params["WSZ"] += 1

    elif method == "SG":
        if "w" not in validated_params:
            validated_params["w"] = 11
        if "p" not in validated_params:
            validated_params["p"] = 2
        elif validated_params["w"] % 2 == 0:
            logger.warning("SG 窗口大小应为奇数，调整为 +1")
            validated_params["w"] += 1

    return validated_params


# =============================================================================
# Scikit-learn 兼容的变换器包装类
# =============================================================================


@register_preprocessing("SNV")
class SNVTransformer(BaseEstimator, TransformerMixin):
    """
    标准正态变量（SNV）变换的 Scikit-learn 兼容包装器。

    设计思路：提供与 sklearn 管道完全兼容的 SNV 变换接口，
    支持 fit/transform 模式，便于集成到机器学习工作流中。
    """

    def __init__(self):
        pass

    def fit(self, X: NDArray[np.floating], y: Optional[NDArray] = None) -> 'SNVTransformer':
        """
        SNV 不需要从训练数据中学习任何参数。

        Args:
            X: 训练数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            self
        """
        return self

    def transform(self, X: NDArray[np.floating], y: Optional[NDArray] = None) -> NDArray[np.floating]:
        """
        对数据应用 SNV 变换。

        Args:
            X: 要变换的数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            变换后的数据，形状 (n_samples, n_features)
        """
        return SNV(X)


@register_preprocessing("MSC")
class MSCTransformer(BaseEstimator, TransformerMixin):
    """
    多元散射校正（MSC）的 Scikit-learn 兼容包装器。

    设计思路：MSC 需要从训练数据中学习平均光谱，因此实现了有状态的
    fit/transform 模式，确保训练和测试数据使用相同的参考光谱。
    """

    def __init__(self):
        self.mean_spectrum_: Optional[NDArray[np.floating]] = None

    def fit(self, X: NDArray[np.floating], y: Optional[NDArray] = None) -> 'MSCTransformer':
        """
        从训练数据中学习用于 MSC 的平均光谱。

        Args:
            X: 训练数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            self
        """
        self.mean_spectrum_ = np.mean(X, axis=0)
        return self

    def transform(self, X: NDArray[np.floating], y: Optional[NDArray] = None) -> NDArray[np.floating]:
        """
        使用学习到的平均光谱应用 MSC 变换。

        Args:
            X: 要变换的数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            变换后的数据，形状 (n_samples, n_features)
        """
        if self.mean_spectrum_ is None:
            raise ValueError("MSCTransformer 必须在变换前先拟合")

        # 使用拟合的平均光谱应用 MSC
        return MSC(X, self.mean_spectrum_)


@register_preprocessing("Centering")
class CenteringTransformer(BaseEstimator, TransformerMixin):
    """
    中心化变换的 Scikit-learn 兼容包装器。

    设计思路：实现全局中心化，从训练数据中学习均值，
    然后在训练和测试数据上应用相同的中心化操作。
    """

    def __init__(self):
        pass

    def fit(self, X, y=None):
        """
        从训练数据中学习用于中心化的均值。

        Args:
            X: 训练数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            self
        """
        self.mean_ = np.mean(X, axis=0)
        return self

    def transform(self, X, y=None):
        """
        使用学习到的均值对数据应用中心化变换。

        Args:
            X: 要变换的数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            变换后的数据，形状 (n_samples, n_features)
        """
        if not hasattr(self, "mean_"):
            raise ValueError("此 CenteringTransformer 实例尚未拟合。")

        return X - self.mean_


@register_preprocessing("MinMaxScaler")
class MMSTransformer(BaseEstimator, TransformerMixin):
    """
    MinMaxScaler 变换的 Scikit-learn 兼容包装器。

    此包装器在委托给 sklearn 的 MinMaxScaler 进行实际计算的同时，
    提供与其他光谱变换器的 API 一致性。
    """

    def __init__(self):
        """初始化 MMS 变换器。"""
        pass

    def fit(self, X, y=None):
        """
        拟合变换器（对于 MMS 是无操作，因为它委托给 sklearn）。

        Args:
            X: 输入数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            self: 返回实例本身
        """
        return self

    def transform(self, X, y=None):
        """
        对输入数据应用 MinMaxScaler 变换。

        Args:
            X: 输入数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            np.ndarray: 变换后的数据，形状 (n_samples, n_features)
        """
        return MMS(X)


@register_preprocessing("StandardScaler")
class SSTransformer(BaseEstimator, TransformerMixin):
    """
    StandardScaler 变换的 Scikit-learn 兼容包装器。

    此包装器在委托给 sklearn 的 StandardScaler 进行实际计算的同时，
    提供与其他光谱变换器的 API 一致性。
    """

    def __init__(self):
        """初始化 SS 变换器。"""
        pass

    def fit(self, X, y=None):
        """
        拟合变换器（对于 SS 是无操作，因为它委托给 sklearn）。

        Args:
            X: 输入数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            self: 返回实例本身
        """
        return self

    def transform(self, X, y=None):
        """
        对输入数据应用 StandardScaler 变换。

        Args:
            X: 输入数据，形状 (n_samples, n_features)
            y: 目标值（忽略）

        Returns:
            np.ndarray: 变换后的数据，形状 (n_samples, n_features)
        """
        return SS(X)


@register_preprocessing("MovingAverage")
class MATransformer(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for Moving Average transformation.
    """

    def __init__(self, WSZ=11):
        """
        Initialize the MA transformer.

        Args:
            WSZ: Window size for moving average (default: 11)
        """
        self.WSZ = WSZ

    def fit(self, X, y=None):
        """
        Fit the transformer (no-op for MA).

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            self: Returns the instance itself
        """
        return self

    def transform(self, X, y=None):
        """
        Apply Moving Average transformation to the input data.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            np.ndarray: Transformed data, shape (n_samples, n_features)
        """
        return MA(X, WSZ=self.WSZ)


@register_preprocessing("SavitzkyGolay")
class SGTransformer(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for Savitzky-Golay transformation.
    """

    def __init__(self, w=11, p=2):
        """
        Initialize the SG transformer.

        Args:
            w: Window size (default: 11)
            p: Polynomial order (default: 2)
        """
        self.w = w
        self.p = p

    def fit(self, X, y=None):
        """
        Fit the transformer (no-op for SG).

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            self: Returns the instance itself
        """
        return self

    def transform(self, X, y=None):
        """
        Apply Savitzky-Golay transformation to the input data.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            np.ndarray: Transformed data, shape (n_samples, n_features)
        """
        return SG(X, w=self.w, p=self.p)


@register_preprocessing("FirstDerivative")
class D1Transformer(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for First Derivative transformation.
    """

    def __init__(self):
        """Initialize the D1 transformer."""
        pass

    def fit(self, X, y=None):
        """
        Fit the transformer (no-op for D1).

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            self: Returns the instance itself
        """
        return self

    def transform(self, X, y=None):
        """
        Apply First Derivative transformation to the input data.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            np.ndarray: Transformed data, shape (n_samples, n_features-1)
        """
        return D1(X)


@register_preprocessing("SecondDerivative")
class D2Transformer(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for Second Derivative transformation.
    """

    def __init__(self):
        """Initialize the D2 transformer."""
        pass

    def fit(self, X, y=None):
        """
        Fit the transformer (no-op for D2).

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            self: Returns the instance itself
        """
        return self

    def transform(self, X, y=None):
        """
        Apply Second Derivative transformation to the input data.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            np.ndarray: Transformed data, shape (n_samples, n_features-2)
        """
        return D2(X)


@register_preprocessing("Detrending")
class DTTransformer(BaseEstimator, TransformerMixin):
    """
    Scikit-learn compatible wrapper for Detrending transformation.
    """

    def __init__(self):
        """Initialize the DT transformer."""
        pass

    def fit(self, X, y=None):
        """
        Fit the transformer (no-op for DT).

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            self: Returns the instance itself
        """
        return self

    def transform(self, X, y=None):
        """
        Apply Detrending transformation to the input data.

        Args:
            X: Input data, shape (n_samples, n_features)
            y: Target values (ignored)

        Returns:
            np.ndarray: Transformed data, shape (n_samples, n_features)
        """
        return DT(X)


# =============================================================================
# 变换器工厂函数
# =============================================================================


def get_transformer(method: str, params: Optional[Dict[str, Any]] = None) -> Optional[BaseEstimator]:
    """
    创建预处理方法的 sklearn 兼容变换器的工厂函数。

    此函数现在完全由注册表驱动，支持插件化扩展。用户可以通过 @register_preprocessing
    装饰器添加自定义预处理器，无需修改此工厂函数。

    设计思路：使用注册表模式，根据方法名称动态查找并实例化相应的变换器类，
    支持参数验证和默认值设置，确保变换器的正确配置。

    Args:
        method (str): 预处理方法名称
        params (Dict[str, Any], optional): 变换器的参数

    Returns:
        BaseEstimator: Sklearn 兼容的变换器实例

    Raises:
        KeyError: 如果方法未在注册表中找到

    Example:
        >>> transformer = get_transformer("SNV")
        >>> transformer = get_transformer("MovingAverage", {"WSZ": 15})
        >>> transformer = get_transformer("SavitzkyGolay", {"w": 11, "p": 2})
    """
    if params is None:
        params = {}

    # 🔧 特殊处理：None 方法返回 None（无变换）
    if method == "None":
        return None

    # 🔧 向后兼容性映射：将旧的方法名映射到新的注册名
    legacy_method_mapping = {
        "MMS": "MinMaxScaler",
        "SS": "StandardScaler",
        "CT": "Centering",
        "MA": "MovingAverage",
        "SG": "SavitzkyGolay",
        "D1": "FirstDerivative",
        "D2": "SecondDerivative",
        "DT": "Detrending"
    }

    # 如果是旧方法名，转换为新的注册名
    registered_method = legacy_method_mapping.get(method, method)

    # 验证并设置默认参数
    validated_params = validate_preprocessing_params(method, params)

    # 🚀 从注册表获取变换器类
    try:
        transformer_class = get_preprocessing_function(registered_method)
    except KeyError:
        available_methods = get_available_methods()
        raise ValueError(f"未知的预处理方法: {registered_method}。可用方法: {available_methods}")

    # 🔧 根据不同的变换器类型，使用适当的参数实例化
    if registered_method == "MovingAverage":
        return transformer_class(WSZ=validated_params.get("WSZ", 11))
    elif registered_method == "SavitzkyGolay":
        return transformer_class(w=validated_params.get("w", 11), p=validated_params.get("p", 2))
    else:
        # 大多数变换器不需要参数
        return transformer_class()
