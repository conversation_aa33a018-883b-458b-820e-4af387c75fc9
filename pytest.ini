[tool:pytest]
# 测试发现配置
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 标记定义
markers =
    unit: 单元测试标记
    integration: 集成测试标记
    e2e: 端到端测试标记
    slow: 慢速测试标记
    benchmark: 性能基准测试标记

# 输出配置
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings

# 覆盖率配置
[coverage:run]
source = src/my_spectral_framework
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = True
precision = 2

[coverage:html]
directory = htmlcov
