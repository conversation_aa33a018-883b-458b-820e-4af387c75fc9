# conf/base/parameters.py
"""
光谱分析框架的默认参数配置。

此文件定义了一组最小化且可直接运行的默认参数，
并直接引用了模型级别的预设（例如RF_FAST_PARAMS），使配置更加模块化。
"""

# 导入一个默认的模型预设，使配置开箱即用。
from conf.model_params.rf import FAST_PARAMS as RF_FAST_PARAMS

# 定义一组简洁、最小化的默认参数。
DEFAULT_PARAMS = {
    "experiment_type": "single_run",
    "data_config": {
        "source_type": "opensa_test",
        "spectrum_id_strategy": "auto_generate",
        "type": "Cls",
        "file_path": None,
        "has_header": True,
        "label_col": "last",
        "delimiter": ",",
        "wavelength_row": False,
        "label_col_name": "label",
        "group_col_name": None,
        "spectrum_unique_id_col_name": "spectrum_id",
        "wave_number_range": None,
        "encoding_priority": ["utf-8-sig", "gbk", "utf-8", "latin1"],
        "nan_fill_value_spectra": 0.0,
        "nan_handling_key_cols": "remove_row",
        "split_method": "random",
        "test_size": 0.2,
        "random_seed": 42,
    },
    "cv_config": {"method": "stratified", "n_splits": 5, "random_state": 42},
    "preprocessing_config": {"method": "SNV", "params": {}},
    "feature_selection_config": {"method": "Pca", "params": {}},

    # ml_model_config 现在变得简单清晰，它直接调用模型级别的预设。
    "ml_model_config": {
        "type": "Classification",
        "name": "RF",
        "params": {"RF": RF_FAST_PARAMS},
    },

    "evaluation_config": {
        "metrics": {"classification": ["accuracy", "precision", "recall", "f1"], "regression": ["rmse", "r2", "mae"]},
        "save_predictions": True,
        "save_model": True,
    },
    "experiment_config": {
        "name": "unnamed_experiment",
        "description": "",
        "tags": [],
        "log_level": "INFO",
        "save_results": True,
        "results_dir": "results",
    },
    "hyperparameter_optimization_config": {
        "strategy": "grid_search",
        "cv_strategy": "stratified",
        "cv_folds": 3,
        "scoring": "accuracy",
        "param_grid": None,
        "n_trials": None,
        "timeout": None,
        "direction": "maximize",
        "save_study": True,
    },
    "traceability_config": {
        "level": "none"
    },
    "paths_config": {"data_dir": "data", "models_dir": "models", "results_dir": "results", "logs_dir": "logs"},
}
