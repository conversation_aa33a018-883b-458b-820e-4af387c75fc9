"""
光谱分析框架的模型工厂模块。

本模块提供了一种集中的方式来访问和实例化不同的机器学习模型，用于光谱分析任务。
设计理念：采用工厂模式和注册表模式，支持动态模型加载，提供统一的模型创建接口。

Author: txy
License: Apache-2.0
"""

from typing import Any, Callable, Dict, Optional, Union, Type

# 基础类型导入
from sklearn.base import BaseEstimator

from ..core.registry import get_model_function, list_registered_models, get_model_params_function
from ..core.utils import get_logger
from ..preprocessing.spectral_processors import get_transformer
from .classic_ml._base import SpectralModelWrapper

logger = get_logger(__name__)

# ===== 注册表驱动的模型工厂 =====
# 硬编码的模型类字典已被移除，现在完全依赖注册表机制
# 所有模型都通过 @register_model 装饰器注册，并通过 get_model_function 动态获取





def get_model_instance(
    model_type: str,
    model_name: str,
    model_params: Optional[Dict[str, Any]] = None,
) -> BaseEstimator:
    """
    从注册表中查找并创建基础机器学习模型实例。

    这个函数完全依赖注册表机制，符合"完全由注册表驱动"的设计原则。
    所有模型都通过 @register_model 装饰器注册，并通过 get_model_function 动态获取。

    Args:
        model_type: 模型类型（"Classification" 或 "Regression"）
        model_name: 模型名称
        model_params: 模型参数

    Returns:
        BaseEstimator: 从注册表创建的模型实例

    Raises:
        ValueError: 当模型未注册或创建失败时
    """
    model_type_lower = model_type.lower()
    model_name_upper = model_name.upper()

    logger.info(f"从注册表创建基础模型: '{model_name_upper}' ({model_type_lower})")

    try:
        # 从注册表获取模型创建函数
        model_creator_func = get_model_function(name=model_name_upper, model_type=model_type_lower)

        # 获取模型的默认参数，并与用户提供的参数合并
        default_params = get_model_default_params(model_type, model_name)
        # 移除训练函数中可能存在的 use_scaler 标志，因为它在模型实例中不适用
        if "use_scaler" in default_params:
            default_params.pop("use_scaler")

        full_params = {**default_params, **(model_params or {})}

        logger.info(f"调用注册的工厂函数创建模型，参数: {full_params}")

        # 调用注册的工厂函数创建模型实例
        model_instance = model_creator_func(**full_params)

        logger.info(f"成功创建模型实例: {type(model_instance).__name__}")
        return model_instance

    except KeyError as e:
        try:
            available_models = list_registered_models()
            available_for_type = [
                key for key in available_models
                if key.startswith(f"{model_type_lower}_")
            ]
            available_names = [
                key.split("_", 1)[1] for key in available_for_type
            ]
        except Exception:
            available_names = ["无法获取可用模型列表"]

        logger.error(f"无法为模型 '{model_name_upper}' ({model_type_lower}) 找到注册的创建函数。")
        raise ValueError(
            f"模型 '{model_name_upper}' ({model_type_lower}) 未被注册或不受支持。\n"
            f"可用的 {model_type_lower} 模型: {available_names}"
        ) from e

    except Exception as e:
        logger.error(f"创建模型实例时出错: {e}", exc_info=True)
        raise RuntimeError(
            f"创建模型 '{model_name_upper}' 实例失败: {str(e)}"
        ) from e


def _create_preprocessing_pipeline(preprocessing_config: Dict[str, Any]) -> Optional[BaseEstimator]:
    """
    根据预处理配置创建预处理管道。

    Args:
        preprocessing_config: 预处理配置，包含 steps 列表

    Returns:
        预处理器实例或 None
    """
    from sklearn.pipeline import Pipeline

    steps = preprocessing_config.get("steps", [])

    if not steps or (len(steps) == 1 and steps[0].get("method") == "None"):
        return None

    # 创建预处理步骤
    pipeline_steps = []
    for i, step in enumerate(steps):
        method = step.get("method", "None")
        params = step.get("params", {})

        if method != "None":
            transformer = get_transformer(method, params)
            if transformer is not None:
                pipeline_steps.append((f"step_{i}_{method.lower()}", transformer))

    if not pipeline_steps:
        return None

    # 如果只有一个步骤，直接返回变换器
    if len(pipeline_steps) == 1:
        return pipeline_steps[0][1]

    # 多个步骤时创建管道
    return Pipeline(pipeline_steps)


# 🚀 移除：_create_base_model 和 _get_default_model_params_for_instance_creation 函数
# 它们的功能已经合并到简化后的 get_model_instance 函数中


# 遗留的条件函数已移除 - 现在使用纯注册表查找


# 遗留的回归训练器函数已移除 - 现在使用纯注册表查找


def get_model_predictor(model_type: str, model_name: str) -> Callable:
    """
    从注册表获取模型预测函数。

    Args:
        model_type: 模型类型（"Classification" 或 "Regression"）
        model_name: 特定模型的名称

    Returns:
        模型预测函数
    """
    model_type = model_type.lower()
    model_name = model_name.upper()

    logger.info(f"从注册表获取 '{model_name}' ({model_type}) 的预测器")

    try:
        # 尝试从注册表获取预测器
        predictor_func = get_model_function(f"{model_name}_PREDICTOR", model_type)
        return predictor_func
    except KeyError:
        # 对大多数模型回退到默认的 sklearn 风格预测器
        logger.info(f"未找到 {model_name} 的特定预测器，使用默认的 sklearn 预测器")

        def default_sklearn_predict(model, X_test):
            """默认的 sklearn 风格预测函数。"""
            if hasattr(model, "scaler_") and model.scaler_ is not None:
                X_test = model.scaler_.transform(X_test)
            return model.predict(X_test)

        return default_sklearn_predict


# 遗留的预测器函数已移除 - 现在使用带回退的注册表查找


def get_available_models() -> Dict[str, list]:
    """
    从注册表获取按类型分类的可用模型列表。

    Returns:
        包含按类型分类的可用模型名称列表的字典
    """
    try:
        # 从注册表获取所有已注册的模型
        all_models = list_registered_models()

        # 解析并按类型组织
        classification_models = []
        regression_models = []

        for model_key in all_models:
            if isinstance(model_key, str) and "_" in model_key:
                parts = model_key.split("_")
                if len(parts) >= 2:
                    model_name = parts[0]
                    model_type = parts[1]

                    if model_type == "classification" and model_name not in classification_models:
                        classification_models.append(model_name)
                    elif model_type == "regression" and model_name not in regression_models:
                        regression_models.append(model_name)

        return {"classification": sorted(classification_models), "regression": sorted(regression_models)}
    except Exception as e:
        logger.warning(f"无法从注册表获取模型：{e}")
        # 回退到已知模型
        return {"classification": ["ANN", "SVM", "RF", "PLS_DA", "CNN"], "regression": ["ANN", "SVR", "RF", "PLS", "CNN"]}


def get_model_default_params(model_type: str, model_name: str) -> Dict[str, Any]:
    """
    从注册表中获取特定模型的默认参数。

    Args:
        model_type: 模型类型 ("Classification" 或 "Regression")
        model_name: 特定模型的名称

    Returns:
        默认参数字典

    Raises:
        ValueError: 如果模型未注册参数函数
    """
    model_type_lower = model_type.lower()
    model_name_upper = model_name.upper()

    logger.info(f"从注册表获取 '{model_name_upper}' ({model_type_lower}) 的默认参数")

    try:
        # 从注册表获取参数函数
        params_func = get_model_params_function(name=model_name_upper, model_type=model_type_lower)
        default_params = params_func()
        logger.debug(f"成功从注册表获取到 {model_name_upper} 的参数")
        return default_params

    except KeyError:
        # 特殊处理CNN：在未来的步骤中，CNN的默认参数也将被注册
        # 目前暂时保留硬编码作为平滑过渡
        if model_name_upper == "CNN":
            logger.warning(f"模型 'CNN' 的参数函数未在注册表中找到，使用硬编码的默认值。")
            if model_type_lower == "classification":
                return {"epochs": 100, "batch_size": 32, "learning_rate": 0.001, "device": "cpu"}
            else: # regression
                return {"epochs": 100, "batch_size": 16, "learning_rate": 0.001, "device": "cpu"}
        else:
            logger.error(f"无法为模型 '{model_name_upper}' ({model_type_lower}) 找到注册的参数函数。")
            raise ValueError(f"模型 '{model_name_upper}' ({model_type_lower}) 未注册其默认参数。")








def validate_model_params(model_type: str, model_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证并将模型参数与默认值合并。

    Args:
        model_type: 模型类型
        model_name: 模型名称
        params: 用户提供的参数

    Returns:
        与默认值合并的验证参数
    """
    default_params = get_model_default_params(model_type, model_name)
    validated_params = {**default_params, **params}

    logger.info(f"已验证 {model_type} {model_name} 的参数：{validated_params}")
    return validated_params
