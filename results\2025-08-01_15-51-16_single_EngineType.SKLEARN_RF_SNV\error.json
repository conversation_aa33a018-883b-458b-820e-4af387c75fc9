{"type": "StorageError", "message": "保存产物 'final_model' 失败: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer", "traceback": "Traceback (most recent call last):\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\storage\\local.py\", line 119, in save\n    self._format_handlers[format](data, file_path)\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\storage\\local.py\", line 289, in _save_joblib\n    joblib.dump(data, file_path)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 600, in dump\n    NumpyPickler(f, protocol=protocol).dump(value)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 487, in dump\n    self.save(obj)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 603, in save\n    self.save_reduce(obj=obj, *rv)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 717, in save_reduce\n    save(state)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 972, in save_dict\n    self._batch_setitems(obj.items())\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 998, in _batch_setitems\n    save(v)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 932, in save_list\n    self._batch_appends(obj)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 956, in _batch_appends\n    save(x)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 887, in save_tuple\n    save(element)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 603, in save\n    self.save_reduce(obj=obj, *rv)\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 687, in save_reduce\n    save(cls)\n  File \"E:\\Python\\Envs\\cu128\\Lib\\site-packages\\joblib\\numpy_pickle.py\", line 395, in save\n    return Pickler.save(self, obj)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 560, in save\n    f(self, obj)  # Call unbound method with explicit self\n    ^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 1129, in save_type\n    return self.save_global(obj)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\.pyenv\\pyenv-win\\versions\\3.11.9\\Lib\\pickle.py\", line 1076, in save_global\n    raise PicklingError(\n_pickle.PicklingError: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\scripts\\run_experiment.py\", line 430, in main\n    results = runner.run()  # 运行实验，并获取结果\n              ^^^^^^^^^^^^\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\runners.py\", line 175, in run\n    result = runner.run()\n             ^^^^^^^^^^^^\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\runners.py\", line 149, in run\n    results = evaluator.evaluate(model_pipeline, X_train, y_train, groups_train, ids_train)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\evaluation\\evaluators.py\", line 104, in evaluate\n    self.handler.log_artifact(\"final_model\", model, \"joblib\", subdir=\"models\")\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\reporting\\handlers.py\", line 276, in log_artifact\n    uri = self.storage.save(\n          ^^^^^^^^^^^^^^^^^^\n  File \"E:\\program\\GIT\\spec\\spectral_analysis_ml\\my_spectral_framework_v2\\src\\my_spectral_framework\\storage\\local.py\", line 137, in save\n    raise StorageError(error_msg) from e\nmy_spectral_framework.storage.base.StorageError: 保存产物 'final_model' 失败: Can't pickle <class 'my_spectral_framework.preprocessing.spectral_processors.SNVTransformer'>: it's not the same object as my_spectral_framework.preprocessing.spectral_processors.SNVTransformer\n", "timestamp": "2025-08-01T15:51:20.744080"}