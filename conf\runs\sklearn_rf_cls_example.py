"""
一个标准的Sklearn随机森林分类实验配置示例。

此配置展示了如何使用Sklearn引擎运行一个单次分类实验，
包括SNV预处理、PCA特征选择和一个随机森林分类器。
"""

EXPERIMENT_CONFIG = {
    "experiment_type": "single_run",
    
    "data_config": {
        "source_type": "opensa_test",
        "type": "Cls",
        "label_col_name": "label",
        "group_col_name": None,
        "spectrum_unique_id_col_name": "spectrum_id",
        "wave_number_range": None,
        "test_size": 0.2,
        "random_seed": 42,
    },
    
    "preprocessing_config": {
        "steps": [
            {"method": "SNV", "params": {}}
        ]
    },
    
    "feature_selection_config": {
        "method": "Pca",
        "params": {"n_components": 20}
    },
    
    "engine_config": {
        "engine": "sklearn",
        "ml_model_config": {
            "type": "Classification",
            "name": "RF",
            "params": {
                "n_estimators": 100,
                "max_depth": 5,
                "min_samples_split": 2,
                "min_samples_leaf": 1,
                "random_state": 42
            },
        },
    },
    
    "evaluation_config": {
        "metrics": {
            "classification": ["accuracy", "precision", "recall", "f1"],
            "regression": ["rmse", "r2", "mae"],
        },
        "save_predictions": True,
        "save_model": True,
    },
    
    "experiment_config": {
        "name": "sklearn_rf_cls_example",
        "description": "Sklearn随机森林分类示例",
        "tags": ["sklearn", "classification", "random_forest", "example"],
    },
    
    "paths_config": {
        "data_dir": "data",
        "models_dir": "models",
        "results_dir": "results",
        "logs_dir": "logs",
    },
}
